$(document).ready(function() {
    // ✅ Initialize date picker for all elements with .date-picker class
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Handle form submission for Consolidate BP report
    $('#consolidate-bp-filters').on('submit', async function(e) {
        e.preventDefault();                       // Prevent default form submission

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        $('#report-results-container').hide();    // Hide previous report
        $('#loading-spinner').show();             // Show loading indicator

        try {
            // Fetch data from API with form query parameters
            const response = await fetch(`/api/reports/consolidate-bp?${$(this).serialize()}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);        // Render report if successful
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report.');
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`); // Show error alert
        } finally {
            $('#loading-spinner').hide();         // Always hide spinner
        }
    });

    // ✅ Print handler (standard implementation)
    function printTableData() {
        const content = document.getElementById('printable-area').innerHTML;
        const printWindow = window.open('', '', 'height=600,width=900');
        printWindow.document.write('<html><head><title>Print Report</title>');
        printWindow.document.write('</head><body>');
        printWindow.document.write(content);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.print();
    }

    // ✅ Main renderer for report table
    function renderReport(data) {
        const filters = $('#consolidate-bp-filters').serialize();
        const fromDate = new URLSearchParams(filters).get('fromDate');
        const toDate = new URLSearchParams(filters).get('toDate');
        const dateTitle = fromDate ? `FROM ${fromDate} ${toDate ? 'TO ' + toDate : ''}` : `FOR ${new Date().toLocaleDateString('en-CA')}`;

        // Start building report HTML
        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body"><div id="printable-area">
                <div class="row">
                    <div class="col-xs-12 text-center"><h4>CONSOLIDATE BUYER PERFORMANCE ${dateTitle}</h4></div>
                </div><hr/>
                <div class="table-container">
                <table class="table table-bordered report-table">
                    <thead style="background-color:#e0e0e0"><tr>
                        <th style="text-align:center;">BUYERS</th><th></th>
                        <th style="background-color:#94c4e0;text-align: center;">UAE</th>
                        <th style="background-color:#a7a2f2;text-align: center;">OMAN</th>
                        <th style="background-color:#f7897c;text-align: center;">QATAR</th>
                        <th style="background-color:#f7c981;text-align: center;">KUWAIT</th>
                        <th style="background-color:#a4f5d0;text-align: center;">BAHRAIN</th>
                        <th style="text-align:center;">Total AED</th>
                    </tr></thead><tbody>`;

        if (data.length) {
            data.forEach(row => {
                const format = (val) => (val || 0).toFixed(2); // Format to 2 decimals

                // --- Totals per buyer ---
                const orderTotal = (row.UAE || 0) + (row.Oman || 0) + (row.Qatar || 0) + (row.Kuwait || 0) + (row.Bahrain || 0);
                const deliveryTotal = (row['UAE-delivery'] || 0) + (row['Oman-delivery'] || 0) + (row['Qatar-delivery'] || 0) + (row['Kuwait-delivery'] || 0) + (row['Bahrain-delivery'] || 0);
                const profitTotal = (row['UAE-profit'] || 0) + (row['Oman-profit'] || 0) + (row['Qatar-profit'] || 0) + (row['Kuwait-profit'] || 0) + (row['Bahrain-profit'] || 0);
                const gpTotal = deliveryTotal > 0 ? (profitTotal / deliveryTotal) * 100 : 0;

                // --- Add 4-row breakdown per buyer ---
                reportHtml += `
                <tr>
                    <td rowspan="4" style="text-align: center; vertical-align: middle;"><h4>${row.buyer}</h4></td>
                    <td>ORDER</td>
                    <td style="background-color:#94c4e0;">${format(row.UAE)}</td>
                    <td style="background-color:#a7a2f2;">${format(row.Oman)}</td>
                    <td style="background-color:#f7897c;">${format(row.Qatar)}</td>
                    <td style="background-color:#f7c981;">${format(row.Kuwait)}</td>
                    <td style="background-color:#a4f5d0;">${format(row.Bahrain)}</td>
                    <td style="text-align:right;"><b>${format(orderTotal)}</b></td>
                </tr>
                <tr>
                    <td>DELIVERY</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-delivery'])}</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-delivery'])}</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-delivery'])}</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-delivery'])}</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-delivery'])}</td>
                    <td style="text-align:right;"><b>${format(deliveryTotal)}</b></td>
                </tr>
                <tr>
                    <td>PROFIT</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-profit'])}</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-profit'])}</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-profit'])}</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-profit'])}</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-profit'])}</td>
                    <td style="text-align:right;"><b>${format(profitTotal)}</b></td>
                </tr>
                <tr>
                    <td>GP %</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-gp'])} %</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-gp'])} %</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-gp'])} %</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-gp'])} %</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-gp'])} %</td>
                    <td style="text-align:right;"><b>${format(gpTotal)} %</b></td>
                </tr>
                <tr><td colspan="8"></td></tr>`; // Spacer row for readability
            });
        } else {
            // No data message
            reportHtml += `<tr><td colspan="8" class="text-center">No Data Found for the Selected Period</td></tr>`;
        }

        // --- Final HTML with footer buttons ---
        reportHtml += `</tbody></table></div></div></div>
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/consolidate-bp/export?${filters}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        // Inject into DOM
        $('#report-results-container').html(reportHtml);

        // Bind print button action
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }
});
