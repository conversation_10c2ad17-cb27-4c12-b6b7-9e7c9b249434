$(document).ready(function() {
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder, allowClear: true, width: '100%',
            ajax: { url: apiUrl, dataType: 'json', delay: 250, processResults: data => ({ results: data }), cache: true }
        });
    }

    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');

    const currentYear = new Date().getFullYear();
    const $yearSelect = $('#year');
    for (let i = currentYear; i >= 2015; i--) {
        $yearSelect.append(new Option(i, i));
    }
    // Set default month and year
    $('#month').val(new Date().getMonth() + 1);
    $('#year').val(currentYear);

    function generateReport() {
        if (!$('#country').val() || !$('#month').val() || !$('#year').val()) {
            // Do not show alert on initial page load
            if ($(this).is('form')) { // Only show alert if form was submitted
                 alert('Country, Year, and Month are required.');
            }
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $('#logistic-delivery-filters').serialize();

        fetch(`/api/reports/logistic-delivery?${formData}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    renderReport(result.data);
                    $('#report-results-container').show();
                } else {
                    throw new Error(result.error || 'Failed to fetch report data.');
                }
            })
            .catch(err => {
                console.error('FETCH ERROR:', err);
                alert(`An error occurred: ${err.message}`);
            })
            .finally(() => {
                $('#loading-spinner').hide();
            });
    }

    $('#logistic-delivery-filters').on('submit', function(e) {
        e.preventDefault();
        generateReport.call(this); // Pass form context to the function
    });
    
    // Auto-generate report on page load with defaults
    generateReport();

    function renderReport(data) {
        const { reportData, totals } = data;
        const exportParams = $('#logistic-delivery-filters').serialize();

        let reportHtml = `
            <table class="table table-bordered table-striped">
                <thead>
                    <tr><th>Sender</th><th>Opening Count</th><th>Despatch (CM)</th><th>Delivered (CM)</th><th>Delivered (%)</th><th>Return (CM)</th><th>Return (%)</th><th>Pending (%)</th></tr>
                </thead>
                <tbody>`;

        if (reportData.length) {
            reportData.forEach(row => {
                reportHtml += `
                    <tr>
                        <td>${row.sender_name}</td>
                        <!-- ✅ CORRECTED: Changed row.total_shipments to row.total_shipments_display -->
                        <td>${row.total_shipments_display} - <b style="color:#F00">${row.opening_count}</b></td>
                        <td>${row.despatch_cm}</td>
                        <td>${row.delivered_cm}</td>
                        <td style="background-color:rgba(0,166,90,0.2)">${Math.round(row.delivery_percentage)}%</td>
                        <td>${row.return_cm}</td>
                        <td style="background-color:rgba(255,0,0,0.2)">${Math.round(row.return_percentage)}%</td>
                        <td style="background-color:rgba(223,156,18,0.2)">${row.pending_cm} (${Math.round(row.pending_percentage)}%)</td>
                    </tr>`;
            });
        } else {
            reportHtml += `<tr><td colspan="8" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `
                </tbody>
                <tfoot>
                    <tr style="font-weight: bold;">
                        <td>Total</td>
                        <td><a><b>${totals.total_opening || 0}</b></a></td>
                        <td><a><b>${totals.total_despatch || 0}</b></a></td>
                        <td><a><b>${totals.total_delivered || 0}</b></a></td>
                        <td style="background-color:rgba(0,166,90,0.2)"><a><b>${Math.round(totals.total_dp) || 0}%</b></a></td>
                        <td><a><b>${totals.total_return || 0}</b></a></td>
                        <td style="background-color:rgba(255,0,0,0.2)"><a><b>${Math.round(totals.total_rp) || 0}%</b></a></td>
                        <td style="background-color:rgba(223,156,18,0.2)"><a><b>(${Math.round(totals.total_pp)|| 0}%)</b></a></td>
                    </tr>
                </tfoot>
            </table>
            <div class="box-footer row no-print">
                 <a href="/api/reports/logistic-delivery/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>`;
        
        $('#report-results-container').html(reportHtml);
    }
});