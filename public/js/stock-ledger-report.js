$(document).ready(function() {

    // ✅ Initialize date pickers with time selection
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Generic function to initialize Select2 with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder, allowClear: true, width: '100%',
            ajax: {
                url: apiUrl, dataType: 'json', delay: 250,
                data: dataCallback,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize country and product dropdowns
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', params => ({
        term: params.term,
        country: $('#country').val()
    }));

    // ✅ Enable/disable product dropdown based on country selection
    $('#country').on('change', function() {
        const countryId = $(this).val();
        const $productSelect = $('#product');
        $productSelect.val(null).trigger('change');
        $productSelect.prop('disabled', !countryId);
    });

    // ✅ Reset all filters and report content
    $('#reset-button').on('click', function() {
        $('#report-filters')[0].reset();
        $('#country, #product').val(null).trigger('change');
        $('#product').prop('disabled', true);
        $('#report-results-container').hide();
    });

    // ✅ Submit handler for stock ledger form
    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        const formData = $(this).serialize();
        const filters = Object.fromEntries(new URLSearchParams(formData));

        // ⚠️ Ensure required filters are selected
        if (!filters.countryId) {
            return alert('Please select a Country.');
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        try {
            const response = await fetch(`/api/reports/stock-ledger?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data, filters);          // ✅ Render report output
                $('#report-results-container').show();
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();                  // ✅ Hide loader
        }
    });

    // ✅ Function to render the stock ledger report table
    function renderReport(data, filters) {
        const { transactions, openingBalance, openingIn, openingOut, productName } = data;

        // ✅ Format dates in dd-mm-yyyy
        const formatDate = (dateString) => {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString('en-GB').replace(/\//g, '-');
        };

        const today = formatDate(new Date());
        let runningBalance = openingBalance;

    let tableRows = `
        <tr>
            <td colspan="3" class="text-center"><strong>Opening/Closing Balance</strong></td>
            <td>${openingIn || 0}</td>
            <td>${openingOut || 0}</td>
            <td><strong>${openingBalance}</strong></td>
        </tr>
    `;

    transactions.forEach(t => {
        if (t.isPhysicalStock) {
            runningBalance = t.inQty;
        } else {
            runningBalance += (t.inQty || 0) - (t.outQty || 0);
        }

        tableRows += `
            <tr>
                <td>${formatDate(t.transaction_date)}</td>
                <td>${t.refNo || ''}</td>
                <td>${t.businessExecutive ? t.businessExecutive.replace(/<b>|<\/b>/g, '') : ''}</td>
                <td>${t.inQty || 0}</td>
                <td>${t.outQty || 0}</td>
                <td><strong>${runningBalance}</strong></td>
            </tr>
        `;
    });
    
    const fromDateFormatted = filters.fromDate ? formatDate(filters.fromDate.split(' ')[0]) : 'Start';
    const toDateFormatted = filters.toDate ? formatDate(filters.toDate.split(' ')[0]) : 'End';
    const reportTitle = `PRODUCT REPORTS: ${productName.toUpperCase()}<br><small>FROM ${fromDateFormatted} TO ${toDateFormatted}</small>`;

        // ✅ Final HTML structure
        const reportHtml = `
            <div class="box box-primary">
                <div class="box-body" id="printable-area">
                    <div class="row">
                        <div class="col-xs-6">
                            <img src="/images/logo.png" style="height:40px;">
                        </div>
                        <div class="col-xs-6 text-right">
                            <h5>Date: ${today}</h5>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 text-center">
                            <h4>${reportTitle}</h4>
                        </div>
                    </div><hr/>
                    <div class="table-container">
                        <table class="table table-bordered report-table">
                            <thead>
                                <tr>
                                    <th>Date</th><th>RefNo</th><th>Business Executive</th>
                                    <th>In Qty</th><th>Out Qty</th><th>Running Balance</th>
                                </tr>
                            </thead>
                            <tbody>${tableRows}</tbody>
                        </table>
                    </div>
                </div>
                <div class="box-footer no-print">
                    <button id="print-button" class="btn btn-default"><i class="fa fa-print"></i> Print</button>
                </div>
            </div>
        `;

        $('#report-results-container').html(reportHtml);
    }

    // ✅ Print logic for report container
    function printReport() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) {
            alert('Cannot find report content to print.');
            return;
        }

        const reportHtml = printableArea.innerHTML;
        const printWindow = window.open('', '_blank');

        printWindow.document.write('<html><head><title>Stock Ledger Report</title>');
        printWindow.document.write('<link rel="stylesheet" href="/bootstrap/css/bootstrap.min.css" type="text/css" />');
        printWindow.document.write('<style>body { padding: 20px; } .text-center { text-align: center; } h4 { font-size: 18px; } h5 { font-size: 14px; }</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write(reportHtml);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(function() {
            printWindow.print();       // ✅ Trigger print
            printWindow.close();       // ✅ Close window after print
        }, 500); // ⚠️ Delay ensures styles load before printing
    }

    // ✅ Handle print button click via event delegation
    $(document).on('click', '#print-button', function(e) {
        e.preventDefault();
        printReport();
    });

});
