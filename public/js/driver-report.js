$(document).ready(function () {
 

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d" });
 
    // ✅ Reset all filters
    $('#reset-button').on('click', function() {
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        //const toDate = $('input[name="toDate"]').val();
        if (!fromDate) {
            alert('Please select "From Date"')
            return;
        }

  

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/driverReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

   
function renderReport(ordersData) {
  const filters = ordersData?.filters || {};
  const from = filters.fromDate || '';
  const to = filters.toDate || '';

  const countryKeys = Object.keys(ordersData).filter(key => key !== 'filters');

  // Background colors for each section (same as header)
  const bgColors = {
    inQueue: 'rgba(0,166,90,0.2)',             // green
    inHand: 'rgba(223,18,18,0.2)',             // red-ish
    delivered: 'rgba(33,179,212,0.2)',          // blue-ish
    receivedPayment: 'rgba(55,161,253,0.39)',   // brighter blue
    cancelBefore: 'rgba(241,205,31,0.2)',       // yellow
    cancelAfter: 'rgba(241,205,31,0.4)'         // darker yellow
  };

  let totalInqAED = 0, totalInqCount = 0;
  let totalInhandAED = 0, totalInhandCount = 0;
  let totalDelAED = 0, totalDelCount = 0;
  let totalDoneAED = 0, totalDoneCount = 0;
  let totalCancelBeforeAED = 0, totalCancelBeforeCount = 0;
  let totalCancelAfterAED = 0, totalCancelAfterCount = 0;

  let html = `
    <div class="box box-primary">
      <div class="box-body">
        <div id="printable-order-report-area">
          <div class="row">
            <div class="col-xs-6">
              <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
            </div>
            <div class="col-xs-6 text-right">
              <h5>${from} - ${to}</h5>
            </div>
          </div>

          <div class="row">
            <div class="col-xs-12 text-center">
              <h4>Driver Order Summary</h4>
            </div>
          </div>
          <hr/>

          <div style="overflow-x:auto;">
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th rowspan="2">Country</th>
                  <th colspan="5" style="background-color:${bgColors.inQueue};">In Queue</th>
                  <th colspan="5" style="background-color:${bgColors.inHand};">In Hand</th>
                  <th colspan="5" style="background-color:${bgColors.delivered};">Delivered</th>
                  <th colspan="5" style="background-color:${bgColors.cancelAfter};">Cancelled (After Dispatch)</th>
                  </tr>
                  <tr>
                  ${Array(4).fill([
                    '<th>Cash</th>',
                    '<th>Card</th>',
                    '<th>Total</th>',
                    '<th>Total (AED)</th>',
                    '<th>Count</th>'
                  ].join('')).join('')}
                  </tr>
                  </thead>
                  <tbody>`;
                  // <th colspan="5" style="background-color:${bgColors.cancelBefore};">Cancelled (Before Dispatch)</th>
  // <th colspan="5" style="background-color:${bgColors.receivedPayment};">Received Payment</th>

  if (countryKeys.length) {
    countryKeys.forEach(key => {
      const row = ordersData[key];
      const cvalue = parseFloat(row.cvalue || 1);

      // Values
      const inqCash = row.Inq_cash || 0;
      const inqCard = row.Inq_card || 0;
      const inqTotal = row.Inqtotal || 0;
      const inqAED = inqTotal * cvalue;
      const inqCount = row.Inq_count || 0;

      const inhandCash = row.Inhand_cash || 0;
      const inhandCard = row.Inhand_card || 0;
      const inhandTotal = row.Inhandtotal || 0;
      const inhandAED = inhandTotal * cvalue;
      const inhandCount = row.Inhand_count || 0;

      const delCash = row.del_cash || 0;
      const delCard = row.del_card || 0;
      const delTotal = row.deltotal || 0;
      const delAED = delTotal * cvalue;
      const delCount = row.del_count || 0;

      // const doneCash = row.done_cash || 0;
      // const doneCard = row.done_card || 0;
      // const doneTotal = row.donetotal || 0;
      // const doneAED = doneTotal * cvalue;
      // const doneCount = row.done_count || 0;

      const cancelCash = row.cancel_cash || 0;
      const cancelCard = row.cancel_card || 0;
      const cancelTotal = row.canceltotal || 0;
      const cancelCount = row.cancel_count || 0;

      const returnCash = row.return_cash || 0;
      const returnCard = row.return_card || 0;
      const returnTotal = row.returntotal || 0;
      const returnCount = row.return_count || 0;

      // Cancel before and after dispatch
      const cancelBeforeCash = cancelCash - returnCash;
      const cancelBeforeCard = cancelCard - returnCard;
      const cancelBeforeTotal = cancelTotal - returnTotal;
      const cancelBeforeAED = cancelBeforeTotal * cvalue;
      const cancelBeforeCount = cancelCount - returnCount;

      const cancelAfterCash = cancelCash;
      const cancelAfterCard = cancelCard;
      const cancelAfterTotal = cancelTotal;
      const cancelAfterAED = cancelTotal * cvalue;
      const cancelAfterCount = cancelCount;

      // Totals accumulate
      totalInqAED += inqAED;
      totalInqCount += inqCount;

      totalInhandAED += inhandAED;
      totalInhandCount += inhandCount;

      totalDelAED += delAED;
      totalDelCount += delCount;

      // totalDoneAED += doneAED;
      // totalDoneCount += doneCount;

      totalCancelBeforeAED += cancelBeforeAED;
      totalCancelBeforeCount += cancelBeforeCount;

      totalCancelAfterAED += cancelAfterAED;
      totalCancelAfterCount += cancelAfterCount;

      html += `
        <tr>
          <td><img src="${row.flag}" alt="${key} flag" style="height:18px; margin-right:5px;"> <b>${key}</b></td>

          <td style="background-color:${bgColors.inQueue};">${inqCash.toFixed(3)}</td>
          <td style="background-color:${bgColors.inQueue};">${inqCard.toFixed(3)}</td>
          <td style="background-color:${bgColors.inQueue};">${inqTotal.toFixed(3)}</td>
          <td style="background-color:${bgColors.inQueue};">${inqAED.toFixed(3)}</td>
          <td style="background-color:${bgColors.inQueue};">${inqCount}</td>

          <td style="background-color:${bgColors.inHand};">${inhandCash.toFixed(3)}</td>
          <td style="background-color:${bgColors.inHand};">${inhandCard.toFixed(3)}</td>
          <td style="background-color:${bgColors.inHand};">${inhandTotal.toFixed(3)}</td>
          <td style="background-color:${bgColors.inHand};">${inhandAED.toFixed(3)}</td>
          <td style="background-color:${bgColors.inHand};">${inhandCount}</td>

          <td style="background-color:${bgColors.delivered};">${delCash.toFixed(3)}</td>
          <td style="background-color:${bgColors.delivered};">${delCard.toFixed(3)}</td>
          <td style="background-color:${bgColors.delivered};">${delTotal.toFixed(3)}</td>
          <td style="background-color:${bgColors.delivered};">${delAED.toFixed(3)}</td>
          <td style="background-color:${bgColors.delivered};">${delCount}</td>

          

          
          <td style="background-color:${bgColors.cancelAfter};">${cancelAfterCash.toFixed(3)}</td>
          <td style="background-color:${bgColors.cancelAfter};">${cancelAfterCard.toFixed(3)}</td>
          <td style="background-color:${bgColors.cancelAfter};">${cancelAfterTotal.toFixed(3)}</td>
          <td style="background-color:${bgColors.cancelAfter};">${cancelAfterAED.toFixed(3)}</td>
          <td style="background-color:${bgColors.cancelAfter};">${cancelAfterCount}</td>
          </tr>`;
        });
        
        // <td style="background-color:${bgColors.cancelBefore};">${cancelBeforeCash.toFixed(3)}</td>
        // <td style="background-color:${bgColors.cancelBefore};">${cancelBeforeCard.toFixed(3)}</td>
        // <td style="background-color:${bgColors.cancelBefore};">${cancelBeforeTotal.toFixed(3)}</td>
        // <td style="background-color:${bgColors.cancelBefore};">${cancelBeforeAED.toFixed(3)}</td>
        // <td style="background-color:${bgColors.cancelBefore};">${cancelBeforeCount}</td>

    // <td style="background-color:${bgColors.receivedPayment};">${doneCash.toFixed(3)}</td>
    //       <td style="background-color:${bgColors.receivedPayment};">${doneCard.toFixed(3)}</td>
    //       <td style="background-color:${bgColors.receivedPayment};">${doneTotal.toFixed(3)}</td>
    //       <td style="background-color:${bgColors.receivedPayment};">${doneAED.toFixed(3)}</td>
    //       <td style="background-color:${bgColors.receivedPayment};">${doneCount}</td>

    // Totals row
    html += `
      <tr style="font-weight:bold;">
        <td>Total</td>

        <td colspan="3"></td><td >${totalInqAED.toFixed(3)}</td><td>${totalInqCount}</td>
        <td colspan="3"></td><td>${totalInhandAED.toFixed(3)}</td><td>${totalInhandCount}</td>
        <td colspan="3"></td><td>${totalDelAED.toFixed(3)}</td><td>${totalDelCount}</td>
        <td colspan="3"></td><td>${totalCancelAfterAED.toFixed(3)}</td><td>${totalCancelAfterCount}</td>
        </tr>`;
        // <td colspan="3"></td><td>${totalCancelBeforeAED.toFixed(3)}</td><td>${totalCancelBeforeCount}</td>
  } else {
    html += `
      <tr>
        <td colspan="31" class="text-center">No Records Found</td>
      </tr>`;
  }
      //  <td colspan="3"></td><td>${totalDoneAED.toFixed(3)}</td><td>${totalDoneCount}</td>

  html += `
              </tbody>
            </table>
          </div>
          <hr/>
        </div>
      </div>
    </div>`;

  // Render HTML
  $('#report-results-container').html(html);

  // Print button handler
  $('#print-report-button').off('click').on('click', function (e) {
    e.preventDefault();
    if (typeof printTableData === 'function') {
      printTableData();
    }
  });
}








});