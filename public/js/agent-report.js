$(document).ready(function () {
 

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d" });
 
    // ✅ Reset all filters
    $('#reset-button').on('click', function() {
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        //const toDate = $('input[name="toDate"]').val();
        if (!fromDate) {
            alert('Please select "From Date"')
            return;
        }

  

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/agentReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }


    function renderReportOld(data) {
  const { result, grandTotal } = data;
  const today = new Date();
  const exportParams = $('#report-filters').serialize();

  const platformKeys = result.length
    ? Object.keys(result[0]).filter(key =>
        !['staffId', 'name', 'UAE', 'Oman', 'Qatar', 'Bahrain', 'Kuwait', 'total'].includes(key)
      )
    : [];

  let reportHtml = `
    <div class="box box-primary">
      <div class="box-body">
        <div id="printable-order-report-area">
          <div class="row">
            <div class="col-xs-6">
              <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
            </div>
            <div class="col-xs-6 text-right">
              <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 text-center">
              <h4>Agent Order Report</h4>
            </div>
          </div>
          <hr/>

          <div class="table-container">
            <table class="table table-bordered report-table">
              <thead>
                <tr>
                  <th rowspan="2">Employee Name</th>
                  <th rowspan="2">UAE</th>
                  <th rowspan="2">Oman</th>
                  <th rowspan="2">Qatar</th>
                  <th rowspan="2">Bahrain</th>
                  <th rowspan="2">Kuwait</th>
                  <th colspan="${platformKeys.length + 1}">Total Orders</th>
                </tr>
                <tr>
                  ${platformKeys.map(key => `<th>${key.toUpperCase()}</th>`).join('')}
                  <th>TOTAL</th>
                </tr>
              </thead>
              <tbody>`;

  if (result.length > 0) {
    result.forEach(agent => {
      reportHtml += `
        <tr>
          <td>${agent.name}</td>
          <td>${agent.UAE}</td>
          <td>${agent.Oman}</td>
          <td>${agent.Qatar}</td>
          <td>${agent.Bahrain}</td>
          <td>${agent.Kuwait}</td>
          ${platformKeys.map(key => `<td>${agent[key]}</td>`).join('')}
          <td><strong>${agent.total}</strong></td>
        </tr>`;
    });

    reportHtml += `
      <tr>
        <th>Total</th>
        <th>${grandTotal.UAE}</th>
        <th>${grandTotal.Oman}</th>
        <th>${grandTotal.Qatar}</th>
        <th>${grandTotal.Bahrain}</th>
        <th>${grandTotal.Kuwait}</th>
        ${platformKeys.map(key => `<th>${grandTotal[key]}</th>`).join('')}
        <th>${grandTotal.total}</th>
      </tr>`;
  } else {
    reportHtml += `
      <tr>
        <td colspan="${7 + platformKeys.length}" class="text-center">No Records Found</td>
      </tr>`;
  }

  reportHtml += `
              </tbody>
            </table>
          </div>
          <hr/>
        </div>
      </div>
    </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').off('click').on('click', function (e) {
    e.preventDefault();
    printTableData(); // Assumes this is implemented
  });
}


function renderReport(data) {
  const { result, grandTotal } = data;
  const today = new Date();
  const exportParams = $('#report-filters').serialize();

  const platformKeys = result.length
    ? Object.keys(result[0]).filter(key =>
        !['staffId', 'name', 'UAE', 'Oman', 'Qatar', 'Bahrain', 'Kuwait', 'total', 'orderDate'].includes(key)
      )
    : [];

  let reportHtml = `
    <div class="box box-primary">
      <div class="box-body">
        <div id="printable-order-report-area">
          <div class="row">
            <div class="col-xs-6">
              <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
            </div>
            <div class="col-xs-6 text-right">
              <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 text-center">
              <h4>Agent Order Report</h4>
            </div>
          </div>
          <hr/>

          <div class="table-container">
            <table class="table table-bordered report-table">
              <thead>
                <tr>
                  <th rowspan="2">Employee Name</th>
                  <th rowspan="2">UAE</th>
                  <th rowspan="2">Oman</th>
                  <th rowspan="2">Qatar</th>
                  <th rowspan="2">Bahrain</th>
                  <th rowspan="2">Kuwait</th>
                  <th colspan="${platformKeys.length + 1}">Total Orders</th>
                </tr>
                <tr>
                  ${platformKeys.map(key => `<th>${key.toUpperCase()}</th>`).join('')}
                  <th>TOTAL</th>
                </tr>
              </thead>
              <tbody>`;

  if (result.length > 0) {
    result.forEach(agent => {
      reportHtml += `
        <tr>
          <td>${agent.name}</td>
          <td>${agent.UAE}</td>
          <td>${agent.Oman}</td>
          <td>${agent.Qatar}</td>
          <td>${agent.Bahrain}</td>
          <td>${agent.Kuwait}</td>
          ${platformKeys.map(key => `<td>${agent[key]}</td>`).join('')}
          <td><strong>${agent.total}</strong></td>
        </tr>`;
    });

    reportHtml += `
      <tr>
        <th>Total</th>
        <th>${grandTotal.UAE}</th>
        <th>${grandTotal.Oman}</th>
        <th>${grandTotal.Qatar}</th>
        <th>${grandTotal.Bahrain}</th>
        <th>${grandTotal.Kuwait}</th>
        ${platformKeys.map(key => `<th>${grandTotal[key]}</th>`).join('')}
        <th>${grandTotal.total}</th>
      </tr>`;
  } else {
    reportHtml += `
      <tr>
        <td colspan="${7 + platformKeys.length}" class="text-center">No Records Found</td>
      </tr>`;
  }

  reportHtml += `
              </tbody>
            </table>
          </div>
          <hr/>
        </div>
      </div>
    </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').off('click').on('click', function (e) {
    e.preventDefault();
    printTableData(); // Assumes this is implemented
  });
}









});