$(document).ready(function() {

    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    $('#country').select2({
        placeholder: 'Select Country',
        allowClear: true,
        width: '100%',
        ajax: {
            url: '/api/lookups/countries',
            dataType: 'json',
            delay: 250,
            processResults: data => ({ results: data }),
            cache: true
        }
    });

    $('#reset-button').on('click', function() {
        $('#quick-report-filters').get(0).reset();
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#quick-report-filters').on('submit', async function(e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/quick?${formData}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report data.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function renderReport(data) {
        const { orders, currencyCode } = data; 

        if (!orders || orders.length === 0) {
            $('#report-results-container').html('<div class="box box-primary"><div class="box-body text-center">No Records Found</div></div>');
            return;
        }

        const groupedOrders = new Map();
        orders.forEach(row => {
            const orderId = row.orderid;
            if (!groupedOrders.has(orderId)) {
                groupedOrders.set(orderId, {
                    ...row, 
                    products: [] 
                });
            }
            groupedOrders.get(orderId).products.push(row.product_name);
        });
        
        const reportData = Array.from(groupedOrders.values());

        const totals = {
            totalOrders: reportData.length,
            totalAmount: 0,
            shippingCharge: 0, 
            grandTotal: 0
        };
        reportData.forEach(order => {
            totals.shippingCharge += parseFloat(order.shipping_charges || 0);
            totals.grandTotal += parseFloat(order.total_amount || 0);
        });
        totals.totalAmount = totals.grandTotal; 


        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#quick-report-filters').serialize();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <!-- Header remains the same -->
                <div class="row">...</div>

                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead>...</thead>
                        <tbody>`;

        if (reportData.length) {
            reportData.forEach((row, index) => {
                const payModeText = row.pay_mode === 'Credit' ? 'Cash on Delivery' : 'Credit Card Payments';
                const productsList = row.products.map(p => `<div>${p}</div>`).join('');
                const totalAmount = parseFloat(row.total_amount || 0).toFixed(2);
                reportHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${row.order_ref_code || ''}</td>
                        <td>${row.customer_contact || ''}</td>
                        <td>${row.customer_name || ''}</td>
                        <td>${productsList}</td>
                        <td>${row.order_status || ''}</td>
                        <td>${payModeText}</td>
                        <td>${totalAmount}</td>
                    </tr>`;
            });
        } else {
            reportHtml += `<tr><td colspan="8" class="text-center">No Records Found</td></tr>`;
        }
        
        reportHtml += `
                        </tbody>
                    </table>
                </div><hr/>
                <div class="row">
                    <div class="col-xs-6"></div>
                    <div class="col-xs-6">
                        <div class="table-responsive">
                            <table class="table">
                                <tbody>
                                    <tr><th style="width:50%">Total Orders:</th><td>${totals.totalOrders || 0}</td></tr>
                                    <tr><th>Total Amount:</th><td>${(totals.totalAmount || 0).toFixed(2)}</td></tr>
                                    <tr><th>Shipping Charge:</th><td>${(totals.shippingCharge || 0).toFixed(2)}</td></tr>
                                    <tr><th>Grand Total:</th><td><strong>${(totals.grandTotal || 0).toFixed(2)} ${currencyCode}</strong></td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="box-footer">...</div>
        </div>`;

        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }

    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return;
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Quick Report</title>');
        printWindow.document.write('<style>body{font-family:Arial,sans-serif;padding:20px}table{width:100%;border-collapse:collapse;margin-bottom:1rem;font-size:12px}th,td{padding:8px;border:1px solid #dee2e6;text-align:left}thead th{background-color:#f2f2f2}.row{display:flex;flex-wrap:wrap;margin:0 -15px}.col-xs-6{flex:0 0 50%;max-width:50%}.text-right{text-align:right!important}.text-center{text-align:center!important}hr{border-top:1px solid #dee2e6}img{max-width:150px}</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => { printWindow.print(); printWindow.close(); }, 250);
    }
});