$(document).ready(function() {
    // ✅ Initialize base select2 dropdowns
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });

    // ✅ Helper to initialize select2 with AJAX source
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                data: dataCallback,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize all dynamic dropdowns
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');

    // ✅ Product dropdown also depends on selected Category and Country
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', params => ({
        term: params.term,
        categoryId: $('#category').val(),
        country: $('#country').val()
    }));

    // ✅ Clear dependent dropdowns when country changes
    $('#country').on('change', function() {
        $('#buyer, #category, #product').val(null).trigger('change');
    });

    // ✅ Submit handler for Current Stock filter form
    $('#current-stock-filters').on('submit', async function(e) {
        e.preventDefault();

        // --- Basic validation ---
        const country = $('#country').val();
        if (!country) {
            alert('Please select a Country to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            // --- Fetch current stock report ---
            const response = await fetch(`/api/reports/current-stock?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);             // ✅ Render report
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report data.');
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();              // ✅ Always hide loader
        }
    });

    // ✅ Print function (called when print button is clicked)
    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Current Stock Report</title>');

        // --- Inline print styles ---
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; font-size: 12px; }
                th, td { padding: 6px; border: 1px solid #ccc; text-align: left; }
                thead th { background-color: #f2f2f2; }
                .row { display: flex; flex-wrap: wrap; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                .highlight-inactive { background-color: #fcd4d4 !important; }
                .highlight-active { background-color: #d4fcdf !important; }
                hr { border-top: 1px solid #ccc; }
            </style>
        `);
        printWindow.document.write('</head><body>' + printableArea.innerHTML + '</body></html>');
        printWindow.document.close();
        printWindow.focus();

        // --- Delay print to allow DOM load ---
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

    // ✅ Render stock report to HTML table
    function renderReport(data) {
        const { products, totalStockValue, filters } = data;
        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#current-stock-filters').serialize();
        const buyerName = $('#buyer').select2('data')[0]?.text || '';

        // --- Begin report HTML ---
        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body">
                <div id="printable-area">
                    <div class="row">
                        <div class="col-xs-6"><img src="/images/logo.png" style="height:40px;"></div>
                        <div class="col-xs-6 text-right"><h5>Date: ${today}</h5></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 text-center">
                            <h4>CURRENT STOCK REPORT ${buyerName ? '- ' + buyerName.toUpperCase() : ''}</h4>
                        </div>
                    </div>
                    <hr/>

                    <div class="table-container">
                        <table class="table table-bordered report-table">
                            <thead>
                                <tr>
                                    <th>Product Name</th><th>SKU</th>
                                    <th>Stock</th>
                                    <th>Cost</th>
                                    <th>Price</th>
                                    <th>Stock Value</th>
                                </tr>
                            </thead>
                            <tbody>`;

        if (products.length) {
            // ✅ Loop through products and render each row
            products.forEach(p => {
                const isActive = p.status == 1;
                const rowClass = isActive ? 'highlight-active' : 'highlight-inactive';

                reportHtml += `
                    <tr class="${rowClass}">
                        <td>${p.name || ''} </td><td><strong>${p.sku || ''}</strong></td>
                        <td>${p.stock_qty}</td>
                        <td>${parseFloat(p.cost || 0).toFixed(2)}</td>
                        <td>${parseFloat(p.selling_price || 0).toFixed(2)}</td>
                        <td>${parseFloat(p.stock_value || 0).toFixed(2)}</td>
                    </tr>`;
            });
        } else {
            // ✅ No data fallback
            reportHtml += `<tr><td colspan="10" class="text-center">No Records Found</td></tr>`;
        }

        // ✅ Add table footer with total stock value
        reportHtml += `
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="5" class="text-right"><strong>Total Stock Value:</strong></td>
                                    <td><strong>${parseFloat(totalStockValue || 0).toFixed(2)}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- ✅ Footer with buttons -->
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/current-stock/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        // ✅ Inject into DOM and bind print button
        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }
});
