$(document).ready(function () {
    // ✅ Map country codes to currency symbols
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

    // ✅ Initialize date picker with time
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Initialize Select2 single and multi-selects
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    // ✅ Helper to initialize Select2 with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dynamic dropdowns
    // initializeAjaxSelect2('#sender', '/api/lookups/senders', 'Select Sender');
    initializeAjaxSelect2('#staffName', '/api/lookups/staff', 'Select Staff Name');
    initializeAjaxSelect2('#nationality', '/api/lookups/nationalities', 'Select Nationality');
    initializeAjaxSelect2('#paymentGatewayType', '/api/lookups/payment-gateways', 'Select Gateway');
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');

    // Disable sender initially
    $('#sender').select2({
        placeholder: 'Select Sender',
        disabled: true
    });

    // Use delegated event binding to ensure the change is captured correctly
    $(document).on('change', '#country', function () {
        const selectedCountry = $(this).val();
        if (selectedCountry) {
            $('#sender').prop('disabled', false);

            $('#sender').empty().select2({
                placeholder: 'Select Sender',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/senders?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });
        } else {
            $('#sender').val(null).trigger('change');
            $('#sender').prop('disabled', true);
        }
    });

    // ✅ Handle report filter form submission
    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            // ✅ Fetch report data
            const response = await fetch(`/api/reports/order?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);           // ✅ Render report if successful
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();           // ✅ Always hide loader
        }
    });

    // ✅ Print report window setup
    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

    // ✅ Render HTML table report from API response
    function renderReport(data) {
        const { orders, totals, filters } = data;
        const today = new Date();
        const currencySymbol = currencyMap[filters.country] || 'AED';
        const exportParams = $('#report-filters').serialize();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body">
                <!-- ✅ Printable Area Start -->
                <div id="printable-order-report-area">
                    <div class="row">
                        <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
                        <div class="col-xs-6 text-right"><h5>Date: ${today.toLocaleDateString('en-GB')}</h5></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 text-center">
                            <h4>ORDER REPORT FROM ${filters.fromDate} TO ${filters.toDate}</h4>
                        </div>
                    </div><hr/>
                    <div class="table-container">
                        <table class="table table-striped table-bordered report-table">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Order Id</th>
                                    <th>Order Date</th>
                                    <th>Mobile</th>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Pay Mode</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>`;

        // ✅ Render rows or fallback message
        if (orders.length) {
         orders.forEach((order, index) => {
                let customerName = '';
                let gender = '';
                let emirateName = '';

                if (order.customer_details) {
                    try {
                        const details = JSON.parse(order.customer_details || '{}');
                        customerName = details.name || '';
                        gender = details.gender || '';
                        emirateName = details.emirate_name || '';
                    } catch (err) {
                        // invalid JSON, fallback to empty strings
                        customerName = '';
                        gender = '';
                        emirateName = '';
                    }
                }

                reportHtml += `<tr>
                    <td>${index + 1}</td>
                    <td>${order.orderid || ''}<br><a>${order.payment_methodid != 2 ? order.fort_id || '' : ''}</a></td> 
                    <td>${new Date(order.order_date).toLocaleString('en-US')}</td>
                    <td>${order.contact || ''}</td>
                    <td>${customerName || 'N/A'} - ${gender || 'N/A'}</a></td>
                    <td>${order.status || 'N/A'}</td>
                    <td>${order.payment_methodid == 1 ? 'CreditCardPayments' : 'CashOnDelivery'}<br><a><b>${order.orderfrom || ''}</b></a></td>
                    <td>${parseFloat(order.display_amount || 0).toFixed(2)}</td>
                </tr>`;
            });

        } else {
            reportHtml += `<tr><td colspan="8" class="text-center">No Records Found</td></tr>`;
        }

        // ✅ Summary Table with Totals
        reportHtml += `</tbody></table></div>
            <div class="row">
                <div class="col-xs-6"></div>
                <div class="col-xs-6">
                    <table class="table summary-table">
                        <tbody>
                            <tr><th>Total Orders</th><td>:</td><td>${totals.totalOrders || 0}</td></tr>
                            <tr><th>Total Amount</th><td>:</td><td>${parseFloat(totals.totalAmount || 0).toFixed(2)}</td></tr>
                            <tr><th>Shipping Charge</th><td>:</td><td>${parseFloat(totals.shippingCharge || 0).toFixed(2)}</td></tr>
                            <tr><th>Processing Fees</th><td>:</td><td>${parseFloat(totals.processingFees || 0).toFixed(2)}</td></tr>
                            <tr><th>Vat</th><td>:</td><td>${parseFloat(totals.vat || 0).toFixed(2)}</td></tr>
                            <tr><th>Donation Fee</th><td>:</td><td>${parseFloat(totals.donation_fee || 0).toFixed(2)}</td></tr>
                            <tr><th>Discount Amount</th><td>:</td><td>${parseFloat(totals.discountAmount || 0).toFixed(2)}</td></tr>
                            <tr><th style="font-weight:bold;">Grand Total</th><td style="font-weight:bold;">:</td><td><strong>${parseFloat(totals.grandTotal || 0).toFixed(2)} ${currencySymbol}</strong></td></tr>
                        </tbody>
                    </table>
                </div>
            </div><hr/>
        </div> <!-- ✅ Printable Area End -->
        </div>

        <!-- ✅ Action Buttons (Print / Export) -->
        <div class="box-footer">
            <div class="report-actions">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/order/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>
        </div>`;

        // ✅ Inject HTML into DOM and bind print button
        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }

    
});
