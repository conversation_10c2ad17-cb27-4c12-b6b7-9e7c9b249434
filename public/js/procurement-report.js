$(document).ready(function() {

    // ✅ Initialize Select2 with AJAX for country dropdown
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder, allowClear: true, width: '100%',
            ajax: { url: apiUrl, dataType: 'json', processResults: data => ({ results: data }) }
        });
    }

    // ✅ Load country list
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select a Country');

    // ✅ Handle report filter form submission
    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const countryId = $('#country').val();

        // ⚠️ Validate required input
        if (!countryId) return alert('Please select a Country.');

        // ✅ Hide results and show loader
        $('#report-results-container, #action-buttons, #modals-container').hide();
        $('#loading-spinner').show();

        try {
            // ✅ Fetch report data
            const response = await fetch(`/api/reports/procurement?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data, formData);              // ✅ Render report UI
                $('#report-results-container, #action-buttons').show();
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();                        // ✅ Hide loader
        }
    });

    // ✅ Create modal for supplier payment summary
    function createPaymentModal(summary) {
        return `
            <div class="modal fade" id="payment-type-modal" tabindex="-1" role="dialog">
                <div class="modal-dialog"><div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">Supplier Payment Details</h4>
                    </div>
                    <div class="modal-body">
                        <table class="table">
                            <tr><th>Total Cash Payments</th><td>${summary.Cash.count} [ <b>AED ${parseFloat(summary.Cash.value || 0).toFixed(2)}</b> ]</td></tr>
                            <tr><th>Total Credit Payments</th><td>${summary.Credit.count} [ <b>AED ${parseFloat(summary.Credit.value || 0).toFixed(2)}</b> ]</td></tr>
                            <tr><th>Other</th><td>${summary.Other.count} [ <b>AED ${parseFloat(summary.Other.value || 0).toFixed(2)}</b> ]</td></tr>
                        </table>
                    </div>
                </div></div>
            </div>`;
    }

    // ✅ Render report table and payment summary modal
    function renderReport(data, formData) {
        const { reportData, paymentSummary } = data;

        // ✅ Set export download URLs
        $('#grouped-download-link').attr('href', `/api/reports/procurement/export?type=grouped&${formData}`);
        $('#excel-download-link').attr('href', `/api/reports/procurement/export?type=standard&${formData}`);

        let tableRows = '';
        reportData.forEach(row => {
            const actualCost = parseFloat(row.actualCost || 0);

            // ✅ Highlight rows with missing cost
            const rowStyle = actualCost < 1 ? 'style="background-color:rgba(223,37,58,0.2)"' : '';

            // ✅ Format order date
            const orderDate = new Date(row.order_date).toISOString().slice(0, 19).replace('T', ' ');

            tableRows += `
                <tr ${rowStyle} data-product-id="${row.pid}">
                    <td><a><b>${row.orderid}</b></a></td>
                    <td><b>${orderDate}</b></td>
                    <td>${row.buyer_name || ''}</td>
                    <td>
                        <a>${row.supplier?.company_name || 'N/A'}</a><br>
                        <select class="paymode-select" data-supplier-id="${row.supplier?.sid || ''}">
                            <option value="0">0</option>
                            <option value="Cash" ${row.supplier?.paymode === 'Cash' ? 'selected' : ''}>Cash</option>
                            <option value="Credit" ${row.supplier?.paymode === 'Credit' ? 'selected' : ''}>Credit</option>
                        </select>
                    </td>
                    <td>${row.name} - [<a><b>${row.pqty}</b></a>]</td>
                    <td><b>${row.product_code}</b></td>
                    <td>${row.orderqty}</td>
                    <td>${row.stockQty}</td>
                    <td><b>${row.buyQty}</b></td>
                    <td>
                        <div class="cost-cell">
                            <input type="text" value="${actualCost.toFixed(2)}" class="cost-input" readonly data-pid="${row.pid}" style="width:100%; border:none; background-color:transparent;">
                            <span class="update-status" style="color: green; font-size: 12px;"></span>
                        </div>
                    </td>
                </tr>`;
        });

        // ✅ Final report HTML
        const reportHtml = `<div class="box box-primary">
            <div class="table-container">
                <table class="table table-hover table-striped report-table">
                    <thead>
                        <tr>
                            <th>Order Id</th><th>Order Date</th><th>Buyer</th><th>Supplier</th>
                            <th style="width:25%;">Product</th><th>SKU</th><th>Order Qty</th>
                            <th>Stock Qty</th><th>Buyer Qty</th><th>Cost/Pc</th>
                        </tr>
                    </thead>
                    <tbody>${tableRows}</tbody>
                </table>
            </div>
        </div>`;

        // ✅ Append report and modal to DOM
        $('#report-results-container').html(reportHtml);
        $('#modals-container').html(createPaymentModal(paymentSummary)).show();
    }

    // ✅ Enable inline editing on double click
    $(document).on('dblclick', '.cost-input', function() {
        $(this).prop('readonly', false).focus();
    });

    // ✅ Handle cost value update on change
    $(document).on('change', '.cost-input', async function() {
        const input = $(this);
        const pid = input.data('pid');
        const cost = input.val();
        const countryId = $('#country').val();

        input.prop('readonly', true);

        const statusSpan = input.closest('.cost-cell').find('.update-status');
        statusSpan.text('Updating...').css('color', 'orange');

        try {
            const response = await fetch('/api/reports/procurement/update-cost', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ productId: pid, cost, countryId })
            });
            const result = await response.json();

            if (result.success) {
                statusSpan.text(result.message).css('color', 'green');
                setTimeout(() => statusSpan.fadeOut(), 3000);
            } else {
                throw new Error(result.message);
            }
        } catch (err) {
            statusSpan.text('Update failed!').css('color', 'red');
            alert(err.message);
        }
    });

    // ✅ Handle paymode dropdown change
    $(document).on('change', '.paymode-select', async function() {
        const select = $(this);
        const sid = select.data('supplier-id');
        const paymode = select.val();

        // ⚠️ Ignore invalid selections
        if (!sid || paymode === "0") return;

        try {
            const response = await fetch('/api/reports/procurement/update-paymode', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ supplierId: sid, paymode })
            });
            const result = await response.json();

            if (result.success) {
                alert('Paymode updated.'); // ✅ Optional: Add toast instead
            } else {
                throw new Error(result.message);
            }
        } catch (err) {
            alert('Failed to update paymode.');
        }
    });
});
