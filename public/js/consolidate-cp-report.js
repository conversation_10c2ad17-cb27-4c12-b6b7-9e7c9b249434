$(document).ready(function() {
    // ✅ Initialize flatpickr date picker
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Initialize Select2 for Buyer dropdown with AJAX
    $('#buyer').select2({
        placeholder: "All Buyers",
        allowClear: true,
        width: '100%',
        ajax: {
            url: '/api/lookups/buyers',
            dataType: 'json',
            delay: 250,
            processResults: data => ({ results: data }),
            cache: true
        }
    });

    // ✅ Submit handler for Consolidate CP filter form
    $('#consolidate-cp-filters').on('submit', async function(e) {
        e.preventDefault();                       // Prevent page reload

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        $('#report-results-container').hide();    // Hide existing report
        $('#loading-spinner').show();             // Show loading indicator

        try {
            // Fetch report data from API using serialized filters
            const response = await fetch(`/api/reports/consolidate-cp?${$(this).serialize()}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);        // Call renderer
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report.');
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();         // Always hide loader
        }
    });

    // ✅ Renderer function for report table
    function renderReport(data) {
        const filters = $('#consolidate-cp-filters').serialize();
        const fromDate = new URLSearchParams(filters).get('fromDate');
        const toDate = new URLSearchParams(filters).get('toDate');
        const dateTitle = fromDate ? `FROM ${fromDate} ${toDate ? 'TO ' + toDate : ''}` : `FOR ${new Date().toLocaleDateString('en-CA')}`;

        // --- Start building report HTML ---
        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body"><div id="printable-area">
                <div class="row"><div class="col-xs-12 text-center"><h4>CONSOLIDATE CATEGORY PERFORMANCE ${dateTitle}</h4></div></div><hr/>
                <div class="table-container">
                <table class="table table-bordered report-table">
                    <thead style="background-color:#e0e0e0"><tr>
                        <th style="text-align:center;">CATEGORY</th><th></th>
                        <th style="background-color:#94c4e0;text-align: center;">UAE</th>
                        <th style="background-color:#a7a2f2;text-align: center;">OMAN</th>
                        <th style="background-color:#f7897c;text-align: center;">QATAR</th>
                        <th style="background-color:#f7c981;text-align: center;">KUWAIT</th>
                        <th style="background-color:#a4f5d0;text-align: center;">BAHRAIN</th>
                        <th style="text-align:center;">Total AED</th>
                    </tr></thead><tbody>`;

        if (data.length) {
            data.forEach(row => {
                const format = (val) => (val || 0).toFixed(2); // Format number

                // --- Calculate Totals ---
                const orderTotal = (row.UAE || 0) + (row.Oman || 0) + (row.Qatar || 0) + (row.Kuwait || 0) + (row.Bahrain || 0);
                const deliveryTotal = (row['UAE-delivery'] || 0) + (row['Oman-delivery'] || 0) + (row['Qatar-delivery'] || 0) + (row['Kuwait-delivery'] || 0) + (row['Bahrain-delivery'] || 0);
                const profitTotal = (row['UAE-profit'] || 0) + (row['Oman-profit'] || 0) + (row['Qatar-profit'] || 0) + (row['Kuwait-profit'] || 0) + (row['Bahrain-profit'] || 0);
                const gpTotal = deliveryTotal > 0 ? (profitTotal / deliveryTotal) * 100 : 0;

                // --- Add Category Row with 4 Sections ---
                reportHtml += `
                <tr>
                    <td rowspan="4" style="text-align: center; vertical-align: middle;"><h4>${row.category}</h4></td>
                    <td>ORDER</td>
                    <td style="background-color:#94c4e0;">${format(row.UAE)}</td>
                    <td style="background-color:#a7a2f2;">${format(row.Oman)}</td>
                    <td style="background-color:#f7897c;">${format(row.Qatar)}</td>
                    <td style="background-color:#f7c981;">${format(row.Kuwait)}</td>
                    <td style="background-color:#a4f5d0;">${format(row.Bahrain)}</td>
                    <td style="text-align:right;"><b>${format(orderTotal)}</b></td>
                </tr>
                <tr>
                    <td>DELIVERY</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-delivery'])}</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-delivery'])}</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-delivery'])}</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-delivery'])}</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-delivery'])}</td>
                    <td style="text-align:right;"><b>${format(deliveryTotal)}</b></td>
                </tr>
                <tr>
                    <td>PROFIT</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-profit'])}</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-profit'])}</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-profit'])}</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-profit'])}</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-profit'])}</td>
                    <td style="text-align:right;"><b>${format(profitTotal)}</b></td>
                </tr>
                <tr>
                    <td>GP %</td>
                    <td style="background-color:#94c4e0;">${format(row['UAE-gp'])} %</td>
                    <td style="background-color:#a7a2f2;">${format(row['Oman-gp'])} %</td>
                    <td style="background-color:#f7897c;">${format(row['Qatar-gp'])} %</td>
                    <td style="background-color:#f7c981;">${format(row['Kuwait-gp'])} %</td>
                    <td style="background-color:#a4f5d0;">${format(row['Bahrain-gp'])} %</td>
                    <td style="text-align:right;"><b>${format(gpTotal)} %</b></td>
                </tr>
                <tr><td colspan="8"></td></tr>`; // Spacer row
            });
        } else {
            // --- No results message ---
            reportHtml += `<tr><td colspan="8" class="text-center">No Data Found for the Selected Period</td></tr>`;
        }

        // --- Final HTML with footer ---
        reportHtml += `</tbody></table></div></div></div>
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/consolidate-cp/export?${filters}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        // Inject into DOM
        $('#report-results-container').html(reportHtml);

        // ✅ Bind print handler to button
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }

    // ✅ Standard print helper
    function printTableData() {
        const content = document.getElementById('printable-area').innerHTML;
        const printWindow = window.open('', '', 'height=600,width=900');
        printWindow.document.write('<html><head><title>Print Report</title></head><body>');
        printWindow.document.write(content);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.print();
    }
});
