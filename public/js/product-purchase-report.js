$(document).ready(function () {
  const currencyMap = {
    1: "AED",
    2: "OMR",
    3: "QAR",
    5: "KWD",
    6: "BHD",
    7: "SAR",
  };

  $(".date-picker").flatpickr({ enableTime: true, dateFormat: "Y-m-d" });
  $(".select2").select2({
    placeholder: "Select One",
    allowClear: true,
    width: "100%",
  });
  $(".select2-multi").select2({
    placeholder: "Select One or More",
    width: "100%",
  });

  function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
    $(elementId).select2({
      placeholder,
      allowClear: true,
      width: "100%",
      ajax: {
        url: apiUrl,
        dataType: "json",
        delay: 250,
        processResults: (data) => ({ results: data }),
        cache: true,
      },
    });
  }

  // ✅ Initialize Select2 Dropdowns with AJAX
  function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
    $(elementId).select2({
      placeholder,
      allowClear: true,
      width: "100%",
      ajax: {
        url: apiUrl,
        dataType: "json",
        delay: 250,
        data: dataCallback, // ✅ Dynamic parameters (if any)
        processResults: (data) => ({ results: data }),
        cache: true,
      },
    });
  }

  // ✅ Initialize dropdowns for filters
  initializeAjaxSelect2("#country", "/api/lookups/countries", "Select Country");

  // ✅ Clear product if category or country changes
  $("#category, #country").on("change", function () {
    $("#product").val(null).trigger("change");
  });

  // ✅ Reset all filters
  $("#reset-button").on("click", function () {
    $(".select2-ajax").val(null).trigger("change");
  });

  $("#report-filters").on("submit", async function (e) {
    e.preventDefault();

    const fromDate = $('input[name="fromDate"]').val();
    //const toDate = $('input[name="toDate"]').val();
    // if (!fromDate) {
    //     alert('Please select "From Date"')
    //     return;
    // }

    $("#report-results-container").hide();
    $("#loading-spinner").show();
    const formData = $(this).serialize();

    try {
      const response = await fetch(
        `/api/reports/productPurchaseReport?${formData}`
      );
      if (!response.ok)
        throw new Error(
          (await response.json()).error || `Status: ${response.status}`
        );
      const result = await response.json();

      if (result.success) {
        renderReport(result.data);
        $("#report-results-container").show();
      } else {
        throw new Error(result.error || "Unknown error.");
      }
    } catch (err) {
      console.error("FETCH ERROR:", err);
      alert(`An error occurred: ${err.message}`);
    } finally {
      $("#loading-spinner").hide();
    }
  });

  function printTableData() {
    const printableArea = document.getElementById(
      "printable-order-report-area"
    );
    if (!printableArea) return alert("No report found to print.");

    const printWindow = window.open("", "_blank");
    printWindow.document.write("<html><head><title>Order Report</title>");
    printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
    printWindow.document.write("</head><body>");
    printWindow.document.write(printableArea.innerHTML);
    printWindow.document.write("</body></html>");
    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  }

  function renderReport(data) {
    const { productReports,productReturnReports, filters } = data;
    const today = new Date();
    const currencySymbol = currencyMap[filters.country] || "AED";
    const exportParams = $("#report-filters").serialize();

    let totalSubTotal = 0;
    let totalRoundof = 0;
    let totalVat = 0;
    let totalDiscount = 0;
    let total = 0;
    let grandTotals = 0;

    let returnVat = 0;
    let returnTotal = 0
    let returnSubTotal = 0;


    let reportHtml = `
  <div class="box box-primary">
    <div class="box-body">
      <div id="printable-order-report-area">
        <div class="row">
          <div class="col-xs-6"><img src="${
            window.BASE_URL || ""
          }/images/logo.png" style="height:40px;"></div>
          <div class="col-xs-6 text-right"><h5>Date: ${today.toLocaleDateString(
            "en-GB"
          )}</h5></div>
        </div>
        <div class="row"><div class="col-xs-12 text-center"><h4>PURCHASE REPORT FROM ${
          filters.fromDate
        } TO ${filters.toDate}</h4></div></div><hr/>
        
        <div class="table-container">
          <table class="table table-bordered report-table">
            <thead>
              <tr>
                <th width:"20px">Supplier Name	</th>
                <th width:"10px">Bill Date & No	</th>
                <th>Sub Total	</th>
                <th>Round Off	</th>
                <th>VAT</th>
                <th>Discount</th>
                <th>Total</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>`;

    if (productReports.length) {
      productReports.forEach((report, i) => {
        // const total = parseFloat(report.price * report.quantity).toFixed(2);

        reportHtml += `
        <tr>
          <td>${report.supplier_name}<br> Enterd By <a>${report.auth_name}</a> on ${report.entry_date}</td>
          <td>${report.bill_date}<br> <strong>#${report.bill_no}</strong> <a>[&nbsp; <b>Pur id: ${report.purchase_id}</b>&nbsp;]</a></td>
          <td>${report.subtotal}</td>
          <td>${report.roundof}</td>
          <td>${report.vat}</td>
          <td>${report.discount}</td>
          <td><strong>${report.total}</strong></td>
          <td><a href="/product-purchase-invoice/?id=${report.purchase_id}" target="_blank" rel="noopener noreferrer">
            View
          </a>
    </td>
        </tr>`;

        totalSubTotal += parseFloat(report.subtotal);
        
        totalVat += parseFloat(report.vat);
        total += parseFloat(report.total);
        totalDiscount += parseFloat(report.discount);
        totalRoundof += parseFloat(report.roundof);

        

        
      });
    } else {
      reportHtml += `<tr><td colspan="7" class="text-center">No Records Found</td></tr>`;
    }
    // finalTotal =
    //   parseFloat(totalVat + totalSubTotal + totalRoundof + grandTotals) -
    //   parseFloat(totalDiscount);
    
    reportHtml += `
            </tbody>
          </table>
          
        </div>
        <div class="row">
          <div class="col-xs-6"></div>
          
          <hr/>
          <h2><center>Return Report</center></h2>
          <div class="table-container">
          <table class="table table-bordered report-table">
            <thead>
              <tr>
                <th width:"20px">Supplier Name	</th>
                <th width:"10px">Return Date & Bill No	</th>
                <th>Sub Total	</th>
                
                <th>VAT</th>
                
                <th>Total</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>`;

    if (productReturnReports.length) {
      productReturnReports.forEach((report, i) => {
       

        reportHtml += `
        <tr>
          <td>${report.supplier_name}<br> Enterd By <a>${report.auth_name}</a> on ${report.entry_date}</td>
          <td>${report.return_date}<br> <strong>#${report.bill_no}</strong> <a>[Pur id: ${report.purchase_id}]</a></td>
          <td>${report.subtotal}</td>
          
          <td>${report.vat}</td>
          
          <td><strong>${report.total}</strong></td>
           <td><a href="/product-return-invoice/?id=${report.id}" target="_blank" rel="noopener noreferrer">
            View
          </a>
    </td>
        </tr>`;

            
            returnVat += parseFloat(report.vat); 
            returnTotal  += parseFloat(report.total);
            returnSubTotal += returnTotal - returnVat;
        
           
      });
    } else {
      reportHtml += `<tr><td colspan="7" class="text-center">No Records Found</td></tr>`;
    }
    // finalTotal =
    //   parseFloat(totalVat + totalSubTotal + totalRoundof + grandTotals) -
    //   parseFloat(totalDiscount);
    reportHtml += `
            </tbody>
          </table>
          
        </div>
        <div class="row">
          <div class="col-xs-6"></div>
          <div class="col-xs-6">
            <table class="table summary-table">
              <tbody>
             
                <tr><th>Round Off	</th><td>:</td><td>${parseFloat(
                  totalRoundof
                ).toFixed(2)} ${currencySymbol}</td></tr>
                <tr><th> Vat</th><td>:</td><td>${parseFloat(totalVat).toFixed(
                  2
                )} ${currencySymbol}</td></tr>
                <tr><th>Discount</th><td>:</td><td><strong>${parseFloat(
                  totalDiscount
                ).toFixed(2)} ${currencySymbol}</strong></td></tr>
                <tr><th>Purchase Sub Total</th><td>:</td><td><strong>${parseFloat(
                  totalSubTotal
                ).toFixed(2)} ${currencySymbol}</strong></td></tr>
                <tr><th>Purchase Total</th><td>:</td><td><strong>${parseFloat(
                  total
                ).toFixed(2)} ${currencySymbol}</strong></td></tr>
                <tr>
                        <th>Return Total</th>
                        <td>:</td>
                        <td><b>${(returnTotal).toFixed(2)}</b></br><a> [ TotVat : <b>${(returnVat).toFixed(2)}</b> Subtotal : <b>${(returnSubTotal).toFixed(2)}</b> ]</a></td>
                    </tr>
                <tr><th>Net Total	</th><td>:</td><td><strong>${parseFloat(
                  (total-returnTotal)
                ).toFixed(2)} ${currencySymbol}</strong></td></tr>
              </tbody>
            </table>
          </div>
        </div><hr/>
      </div> <!-- End of printable area -->
    </div>

    <div class="box-footer">
      <div class="report-actions">
        <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
        <a href="/api/reports/productPurchaseReport/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
      </div>
    </div>
  </div>`;

    $("#report-results-container").html(reportHtml);

    $("#print-report-button").on("click", (e) => {
      e.preventDefault();
      printTableData();
    });
  }
});