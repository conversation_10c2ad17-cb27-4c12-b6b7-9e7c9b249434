$(document).ready(async function () {


	try {
		const idParam = new URLSearchParams(window.location.search).get('id');
		// optionally coerce to number
		const id = idParam ? Number(idParam) : null;
		if (!id || Number.isNaN(id)) {
		console.error('Missing/invalid id in URL');
		}
		const response =   await fetch(`/api/reports/product-purchase-invoice/${id}`);
		

		if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
		const result =  await response.json();

		if (result.success) {
			renderReport(result.data);
			$('#purchase-report-invoice-container').show();
		} else {
			throw new Error(result.error || 'Unknown error.');
		}
	} catch (err) {
		console.error('FETCH ERROR:', err);
		alert(`An error occurred: ${err.message}`);
	} finally {
		$('#loading-spinner').hide();
	}

	
	function renderReport(data) {
		// Accept object with productReports OR an array directly
		const productReports = Array.isArray(data) ? data : (data?.productReports ?? []);

		if (!Array.isArray(productReports) || productReports.length === 0) {
			$('#purchase-report-invoice-container').html('<p>No data</p>');
			return;
		}

		// --- helpers ---
		const currencyMap = { 1: "AED", 2: "OMR", 3: "QAR", 5: "KWD", 6: "BHD", 7: "SAR" };

		const firstNum = (...vals) => {
			for (const v of vals) {
			const x = Number(v);
			if (Number.isFinite(x)) return x;
			}
			return 0;
		};

		const esc = (s='') => String(s)
			.replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;')
			.replace(/"/g,'&quot;').replace(/'/g,'&#39;');

		

		// Use first row for supplier/meta (same for all lines)
		const H = productReports[0] || {};
		const C = currencyMap[H.country_id] || 'AED';
		const money = new Intl.NumberFormat('en-AE', { style: 'currency', currency: C, minimumFractionDigits: 2, maximumFractionDigits: 2 });
		const num2  = new Intl.NumberFormat('en-AE', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

		const mfmt = (v) => money.format(firstNum(v));
		const nfmt = (v) => num2.format(firstNum(v));

		// Build items (one row per product line)
		let subtotal = 0;
		let discount = 0;
		let vat = 0;
		const items = productReports.map(r => {
			
			const qty   = Number(r.inventory) || 0;         // inventory is your qty
			const unit  = Number(r.price) || 0;             // price is your unit price
			const total = (qty * unit); // prefer order_total

			subtotal += total;
			discount += r.discount;
			vat += r.vat;

			return {
				purchase_id: r.purchase_id,
				country_id: r.country_id,
				title: r.title,
				qty,
				unit,
				total
			};
		});

		
		

		

		// VAT logic: prefer amount if present; else compute from % if provided
		const vatAmountProvided = firstNum(H.vat_amount, H.vat, NaN);
		const vatPercent = firstNum(H.vat_percent, H.vat_rate, NaN);
		let vatAmount = Number.isFinite(vatAmountProvided) ? vatAmountProvided : 0;
		if (!vatAmount && Number.isFinite(vatPercent) && vatPercent > 0) {
			vatAmount = (subtotal - discount) * (vatPercent / 100);
		}

		const roundof = firstNum(H.roundof, H.round_off, 0);

		const computedGrand = subtotal - discount + vat + roundof;
		const grandTotal = firstNum(H.grand_total, H.total, computedGrand);

		// Rows HTML
		const itemRowsHtml = items.map((r, i) => `
			<tr class="item-row">
			<td><span style="float:left;width:30px; margin-top:5px;">
				<b style="border:1px solid #333; padding:0 3px;">${i + 1}</b></span>
			</td>
			<td class="item-name" colspan="2" style="width:400px;">
				<div style="text-align:left;">${esc(r.title)}</div>
			</td>
			<td align="right" class="item-qty" style="width:80px;">
				<div class="qty">${(r.qty)}</div>
			</td>
			<td align="center" class="item-price" style="width:100px;">
				<div class="cost">${mfmt(r.unit)}</div>
			</td>
			<td align="right" class="item-total">
				<span class="price">${mfmt(r.total)}</span>
			</td>
			</tr>
		`).join('');

		const supplierName = esc(H.supplier_name ?? H.vendor_name ?? H.supplier ?? '');
		const email = esc(H.emailid ?? H.email ?? '');
		const phone = esc(H.primary_contact ?? H.mobile ?? H.phone ?? '');

		const vatLabel = Number.isFinite(vatPercent) && vatPercent > 0 ? `${nfmt(vatPercent)}% VAT` : 'VAT';

		const reportInvoiceHtml = `
		<div id="identity">
		<div style="clear:both"></div>

		<div id="customer">
			<span style="font-weight:bold; font-size:16px;">Supplier Details</span><br/>
			<div id="address">
			<span style="text-transform:capitalize">${supplierName}</span><br/>
			${email ? `${email}<br/>` : ''}
			${phone ? `<b>Mobile&nbsp;:&nbsp;</b> ${phone}<br/>` : ''}
			${H.bill_date ? `<b>Date&nbsp;:&nbsp;</b> ${H.bill_date}` : ''}
			</div>

			<table id="meta">
			<tr><td class="meta-head">Purchase ID #</td><td><div>${esc(H.purchase_id ?? '')}</div></td></tr>
			<tr><td class="meta-head">Bill No #</td><td><div>${esc(H.bill_no ?? '')}</div></td></tr>
			<tr><td class="meta-head">Bill Date</td><td><div id="date">${esc(H.bill_date ?? '')}</div></td></tr>
			<tr><td class="meta-head">Total Amount</td><td><div><b>${mfmt(grandTotal)}</b></div></td></tr>
			</table>
		</div>

		<table id="items">
			<tr>
			<th>No</th>
			<th colspan="2" class="item-name">Item</th>
			<th>Quantity</th>
			<th>Unit Price</th>
			<th>Total</th>
			</tr>

			<tr>
			<td colspan="6">
				<div style="float:left; width:100%; overflow:hidden;">
				<table width="100%" id="items1">
					${itemRowsHtml}
				</table>
				</div>
			</td>
			</tr>

			<tr id="hiderow"><td colspan="6">&nbsp;</td></tr>

			<tr>
			<td colspan="3" rowspan="2" class="blank"></td>
			<td colspan="2" class="total-line">Subtotal&nbsp;:</td>
			<td class="total-value"><div id="subtotal">${mfmt(subtotal)}</div></td>
			</tr>
			<tr>
			<td colspan="2" class="total-line">Discount&nbsp;:</td>
			<td class="total-value"><div id="discount">${mfmt(discount)}</div></td>
			</tr>
			<tr>
			<td colspan="3" class="blank"></td>
			<td colspan="2" class="total-line">${esc(vatLabel)}&nbsp;:</td>
			<td class="total-value"><div class="due">${mfmt(vatAmount)}</div></td>
			</tr>
			<tr>
			<td colspan="3" class="blank"></td>
			<td colspan="2" class="total-line">Round off&nbsp;:</td>
			<td class="total-value"><div class="due">${mfmt(roundof)}</div></td>
			</tr>
			<tr>
			<td colspan="3" class="blank"></td>
			<td colspan="2" class="total-line balance">Grand Total&nbsp;:</td>
			<td class="total-value balance"><div class="due">${mfmt(grandTotal)}</div></td>
			</tr>
		</table>
		<br/>
		</div>`;

		$('#purchase-report-invoice-container').html(reportInvoiceHtml);
	}





	
})