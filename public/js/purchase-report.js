$(document).ready(function () {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d" });
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }


   // ✅ Initialize Select2 Dropdowns with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                data: dataCallback, // ✅ Dynamic parameters (if any)
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dropdowns for filters
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');

    // ✅ Product dropdown depends on selected country + category
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', function(params) {
        return {
            term: params.term,
            categoryId: $('#category').val(),
            country: $('#country').val()
        };
    });

    // ✅ Clear product if category or country changes
    $('#category, #country').on('change', function() {
        $('#product').val(null).trigger('change');
    });

    // ✅ Reset all filters
    $('#reset-button').on('click', function() {
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        //const toDate = $('input[name="toDate"]').val();
        if (!fromDate) {
            alert('Please select "From Date"')
            return;
        }

  

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/purchaseReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }


function renderReport(data) {
  const { productReports, grandTotals, filters } = data;
  const today = new Date();
  const currencySymbol = currencyMap[filters.country] || 'AED';
  const exportParams = $('#report-filters').serialize();

  let reportHtml = `
  <div class="box box-primary">
    <div class="box-body">
      <div id="printable-order-report-area">
        <div class="row">
          <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
          <div class="col-xs-6 text-right"><h5>Date: ${today.toLocaleDateString('en-GB')}</h5></div>
        </div>
        <div class="row"><div class="col-xs-12 text-center"><h4>PURCHASE REPORT FROM ${filters.fromDate} TO ${filters.toDate}</h4></div></div><hr/>
        
        <div class="table-container">
          <table class="table table-bordered report-table">
            <thead>
              <tr>
                <th>No</th>
                <th>Purchase Date</th>
                <th>SKU</th>
                <th width:"35px">Product</th>
                <th>Category</th>
                <th>Sub Category</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Total</th>
                <th>Supplier</th>
                <th>Buyer</th>
              </tr>
            </thead>
            <tbody>`;

            let grandTotal = 0;
            let totalQuantity = 0;
            
  if (productReports.length) {
    productReports.forEach((p, i) => {
      // const total = parseFloat(p.price * p.quantity).toFixed(2);
      grandTotal += parseFloat(p.totalCost);
      totalQuantity += parseInt(p.quantity);
      

      reportHtml += `
        <tr>
          <td>${i + 1}</td>
          <td>${p.bill_date}</td>
          <td>${p.sku}</td>
          <td>${p.productName || ''}</td>
          <td>${p.parent_category || ''}</td>
          <td>${p.sub_category || ''}</td>
          <td>${p.quantity}</td>
          <td>${parseFloat(p.price).toFixed(2)}</td>
          <td>${p.totalCost}</td>
          <td>${p.supplier_name}</td>
          <td>${p.buyer_name}</td>
        </tr>`;
    });
  } else {
    reportHtml += `<tr><td colspan="11" class="text-center">No Records Found</td></tr>`;
  }

  reportHtml += `
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-xs-6"></div>
          <div class="col-xs-6">
            <table class="table summary-table">
              <tbody>
                <tr><th>Total Items</th><td>:</td><td>${productReports.length}</td></tr>
                <tr><th>Total Quantity</th><td>:</td><td>${totalQuantity}</td></tr>
                <tr><th>Total Amount</th><td>:</td><td><strong>${parseFloat(grandTotal).toFixed(2)} ${currencySymbol}</strong></td></tr>
              </tbody>
            </table>
          </div>
        </div><hr/>
      </div> <!-- End of printable area -->
    </div>

    <div class="box-footer">
      <div class="report-actions">
        <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
        <a href="/api/reports/purchaseReport/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
      </div>
    </div>
  </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').on('click', (e) => {
    e.preventDefault();
    printTableData();
  });
}










});