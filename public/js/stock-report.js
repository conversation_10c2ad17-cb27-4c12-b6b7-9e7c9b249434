$(document).ready(function() {

    // ✅ Initialize Select2 Dropdowns with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                data: dataCallback, // ✅ Dynamic parameters (if any)
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dropdowns for filters
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');

    // ✅ Product dropdown depends on selected country + category
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', function(params) {
        return {
            term: params.term,
            categoryId: $('#category').val(),
            country: $('#country').val()
        };
    });
    
    // ✅ Enable/disable product dropdown based on country selection
    $('#country').on('change', function() {
        const countryId = $(this).val();
        const $productSelect = $('#product');
        $productSelect.val(null).trigger('change');
        $productSelect.prop('disabled', !countryId);
    });

    // ✅ Clear product if category or country changes
    $('#category, #country').on('change', function() {
        $('#product').val(null).trigger('change');
    });

    // ✅ Reset all filters
    $('#reset-button').on('click', function() {
        $('.select2-ajax').val(null).trigger('change');
    });

    // ✅ Submit report request
    $('#stock-report-filters').on('submit', async function(e) {
        e.preventDefault();

        const country = $('#country').val();
        if (!country) {
            alert('Please select a Country to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/stock?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);          // ✅ Render report output
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report data.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();          // ✅ Hide loader
        }
    });

    // ✅ Render the report table
    function renderReport(data) {
        const { products, filters } = data;
        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#stock-report-filters').serialize();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6">
                        <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
                    </div>
                    <div class="col-xs-6 text-right">
                        <h5>Date: ${today}</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 text-center">
                        <h4>STOCK REPORT</h4>
                    </div>
                </div><hr/>

                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead>
                            <tr>
                                <th>Product/Item Code</th>
                                <th>Product Name</th>
                                <th>Stock In</th>
                                <th>Stock Out</th>
                                <th>Balance</th>
                            </tr>
                        </thead>
                        <tbody>`;

        if (products.length) {
            // ✅ Populate each product row
            products.forEach(p => {
                reportHtml += `
                    <tr>
                        <td>${p.product_code || ''}</td>
                        <td>${p.product_name || ''}</td>
                        <td>${p.stock_in}</td>
                        <td>${p.stock_out}</td>
                        <td>${p.balance}</td>
                    </tr>`;
            });
        } else {
            // ⚠️ No records message
            reportHtml += `<tr><td colspan="5" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `
                        </tbody>
                    </table>
                </div><hr/>
            </div>

            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/stock/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        $('#report-results-container').html(reportHtml);

        // ✅ Bind print handler after DOM update
        $('#print-report-button').on('click', function(e) {
            e.preventDefault();
            printTableData();
        });
    }

    // ✅ Print the rendered report content
    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Stock Report</title>');

        // ✅ Basic styling for print layout
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; padding: 0 15px; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                img { max-width: 150px; }
            </style>
        `);

        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();   // ✅ Trigger print dialog
            printWindow.close();   // ✅ Close print window
        }, 250); // ⚠️ Slight delay to allow styles to load
    }
});
