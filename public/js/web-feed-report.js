$(document).ready(function() {

    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');

    $('#reset-button').on('click', function() {
        $('#web-feed-filters').get(0).reset();
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#web-feed-filters').on('submit', async function(e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/web-feed?${formData}`);
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.error || `HTTP error! status: ${response.status}`);
            }
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report data.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function renderReport(data) {
        const { reportData } = data;
        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#web-feed-filters').serialize();
        const totalOrders = reportData.length;

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6">
                        <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
                    </div>
                    <div class="col-xs-6 text-right">
                        <h5>Date: ${today}</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 text-center">
                        <h4>WEB FEED REPORT</h4>
                    </div>
                </div><hr/>

                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead>
                            <tr>
                                <th>Sl. No.</th>
                                <th>Customer Name</th>
                                <th>Mobile No</th>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Quantity</th>
                                <th>Price/Pc.</th>
                                <th>Date</th>
                                <th>Order From</th>
                                <th>Status</th>
                                <th>Comment</th>
                                <th>Status Message</th>
                            </tr>
                        </thead>
                        <tbody>`;

        if (totalOrders > 0) {
            reportData.forEach((row, index) => {
                const orderDate = new Date(row.order_date).toLocaleString('en-GB', { timeZone: 'Asia/Dubai' });
                reportHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${row.customer || ''}</td>
                        <td>${row.mobileno || ''}</td>
                        <td>${row.product_name || 'N/A'}</td>
                        <td>${row.sku || 'N/A'}</td>
                        <td>${row.quantity || 'N/A'}</td>
                        <td>${row.price || 'N/A'}</td>
                        <td>${orderDate}</td>
                        <td>${row.order_from || ''}</td>
                        <td>${row.status || ''}</td>
                        <td>${row.comment || ''}</td>
                        <td>${row.quick_status || ''}</td>
                    </tr>`;
            });
        } else {
            reportHtml += `<tr><td colspan="9" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `
                        </tbody>
                    </table>
                </div><hr/>
                <div class="row">
                    <div class="col-xs-6"></div>
                    <div class="col-xs-6">
                        <div class="table-responsive">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <th style="width:50%">Total Orders:</th>
                                        <td>${totalOrders}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/web-feed/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        $('#report-results-container').html(reportHtml);

        $('#print-report-button').on('click', function(e) {
            e.preventDefault();
            printTableData();
        });
    }

    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Web Feed Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; font-size: 12px; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; padding: 0 15px; }
                .text-right { text-align: right !important; } .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; } img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }
});