$(document).ready(function () {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d" });
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

   // ✅ Generic function to initialize Select2 with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder, allowClear: true, width: '100%',
            ajax: {
                url: apiUrl, dataType: 'json', delay: 250,
                data: dataCallback,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize country and product dropdowns
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2(
    '#product',
    '/api/lookups/products',
    'Search for a product...',
    params => ({
        term: params.term,
        country: $('#country').val()
    })
);


    // ✅ Enable/disable product dropdown based on country selection
    $('#country').on('change', function() {
        const countryId = $(this).val();
        const $productSelect = $('#product');
        $productSelect.val(null).trigger('change');
        $productSelect.prop('disabled', !countryId);
    });

      // ✅ Reset all filters and report content
    $('#reset-button').on('click', function() {
        $('#report-filters')[0].reset();
        $('#country, #product').val(null).trigger('change');
        $('#product').prop('disabled', true);
        $('#report-results-container').hide();
    });


    // Disable sender initially
    $('#sender').select2({
        placeholder: 'Select Sender',
        disabled: true
    });

    $('#emirate').select2({
        placeholder: 'Select Emirate',
        disabled: true
    });

    // Use delegated event binding to ensure the change is captured correctly
    $(document).on('change', '#country', function () {
        const selectedCountry = $(this).val();
        console.log(selectedCountry);


        if (selectedCountry) {
            $('#sender').prop('disabled', false);
            $('#emirate').prop('disabled', false);

            $('#sender').empty().select2({
                placeholder: 'Select Sender',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/senders?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });

             $('#paymentGatewayType').empty().select2({
                placeholder: 'Select Gateway',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/payment-gateways?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });

            $('#emirate').empty().select2({
                placeholder: 'Select Emirate',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/emirates?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });

        } else {
            $('#sender').val(null).trigger('change');
            $('#sender').prop('disabled', true);
            $('#emirate').val(null).trigger('change');
            $('#emirate').prop('disabled', true);
        }
    });

    $('#emirate').on('change', function () {
    const emirateId = $(this).val();

    if (emirateId) {
        $('#area').prop('disabled', false).empty().select2({
            placeholder: 'Select Area',
            allowClear: true,
            width: '100%',
            ajax: {
                url: `/api/lookups/areas?emirate=${emirateId}`,
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return { results: data };
                },
                cache: true
            }
        });
    } else {
        $('#area').val(null).trigger('change').prop('disabled', true);
    }
  });

 
    
    initializeAjaxSelect2('#staffName', '/api/lookups/staff', 'Select Staff Name');
    initializeAjaxSelect2('#cancelled_staffName', '/api/lookups/staff', 'Select Staff Name');
   

    initializeAjaxSelect2('#orderFrom', '/api/lookups/order-from', 'Select Order From');
    initializeAjaxSelect2('#cancelReason', '/api/lookups/cancel-reason', 'Select Cancel Reason');

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/senderReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }


function renderReport(data) {
  const { orders = [], filters = {},partialAmount = 0 } = data;
  const today = new Date();
  const currencySymbol = currencyMap[filters.country] || 'AED';
  const exportParams = $('#report-filters').serialize();

  // Total calculations
  let subTotal = 0;
  let shippingCharge = 0;
  let grandTotal = 0;
  let totalWarrantyAmount = 0;
  let discount = 0;
  let donation = 0;
  let processing_fee = 0;
  let totalVat = 0;

  let reportHtml = `
    <div class="box box-primary">
      <div class="box-body">
        <div id="printable-order-report-area">
          <div class="row">
            <div class="col-xs-6">
              <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
            </div>
            <div class="col-xs-6 text-right">
              <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 text-center">
              <h4>SENDER REPORT FROM ${filters.fromDate || ''} TO ${filters.toDate || ''}</h4>
            </div>
          </div>
          <hr/>
          <div class="table-container">
            <table class="table table-striped table-bordered report-table">
              <thead>
                <tr>
                  <th>No</th>
                  <th>Order ID</th>
                  <th>Mobile No</th>
                  <th>Sender Name</th>
                  <th>Emirate</th>
                  <th>Order Date</th>
                  <th>Delivery Date</th>
                  <th>Amount (${currencySymbol})</th>
                  <th>Payment Type</th>
                  <th>Status</th>
                  <th>Order Type</th>
                </tr>
              </thead>
              <tbody>`;

  if (orders.length) {
    let rowIndex = 0;

    orders.forEach((od) => {
      let customerName = '';
      let emirateName = '';
      let areaName = '';

      // Safe JSON parse with fallback for invalid JSON
      if (od.customer_details) {
        try {
          const fixedJson = od.customer_details.replace(/:\s*,/g, ':null,');
          const details = JSON.parse(fixedJson);

          customerName = details.name || '';
          emirateName = details.emirate_name || '';
          areaName = details.area_name || '';
        } catch (e) {
          console.warn(`Invalid JSON for order ${od.order_id}:`, od.customer_details);
        }
      }

      // Skip test entries
      if (customerName.toLowerCase().includes('test')) return;

      rowIndex++;

      const orderDate = od.order_date ? new Date(od.order_date) : null;
      const sendDate = od.delivery_date ? new Date(od.delivery_date) : null;
      const dDate = od.formatted_delivery ? new Date(od.formatted_delivery) : null;

      const collectedAmount = parseFloat(od.calculated_amount || 0);
      const chargeAmount = parseFloat(od.charge || 0);
      const warrantyAmount = parseFloat(od.warranty || 0);
      const discountAmount = parseFloat(od.discount_amount || 0);
      const donationAmount = parseFloat(od.donation_fee || 0);
      const pfAmount = parseFloat(od.processing_fee || 0);

      const vatOncollectedAmount = collectedAmount - donationAmount;

       // VAT calculation based on country
      let vat = 0;
      if (filters.country == 1 || filters.country == 2) {
        vat = vatOncollectedAmount - (vatOncollectedAmount / 1.05); // 5% VAT included
      } else if (filters.country == 6) {
        vat = vatOncollectedAmount - (vatOncollectedAmount / 1.10); // 10% VAT included
      } else if (filters.country == 3 || filters.country == 5) {
        vat = 0; // No VAT
      }


      subTotal += collectedAmount;
      shippingCharge += chargeAmount;
      totalWarrantyAmount += warrantyAmount;
      discount += discountAmount;
      donation += donationAmount;
      processing_fee += pfAmount;
      totalVat +=  vat;

     



      reportHtml += `<tr>
        <td>${rowIndex}</td>
        <td>${od.order_id || ''}<br><a><b>${od.fort_id || ''}</b></a></td>
        <td>${od.contact || ''}</td>
        <td>${od.sender_name || ''}<a><br>${od.order_from || ''}</a></td>
        <td>${od.emirate_name}<br><a>${od.area_name}</a></td>
        <td>${orderDate ? orderDate.toLocaleDateString('en-GB') : ''}</td>
        <td>${sendDate ? sendDate.toLocaleDateString('en-GB') : ''}</td>
        <td>${collectedAmount.toFixed(2)}</td>
        <td>${od.payment_methodid == 1 ? 'CreditCardPayments' : 'CashOnDelivery'}</td> 
        <td>${dDate ? dDate.toLocaleDateString('en-GB') : ''}<br>${od.status || ''}</td>
        <td>${od.splitStatus || ''}</td>
      </tr>`;
    });

    grandTotal = subTotal+partialAmount;
  } else {
    reportHtml += `<tr><td colspan="11" class="text-center">No Records Found</td></tr>`;
  }

  // End table and append summary
  reportHtml += `
              </tbody>
            </table>
          </div>

          <!-- Summary Block -->
          <div class="row" style="margin-bottom:20px;">
            <div class="col-xs-6">&nbsp;</div>
            <div class="col-xs-6">
              <div class="table-responsive">
                <table class="table">
                  <tr>
                    <th>Total Items</th>
                    <td>:</td>
                    <td>${orders.length}</td>
                  </tr>
                  <tr>
                    <th>Sub Total.</th>
                    <td>:</td>
                    <td>${((subTotal + discount)- (shippingCharge + processing_fee + donation + totalVat)).toFixed(2)} ${currencySymbol}</td>
                  </tr>
                  <tr>
                    <th>Vat</th>
                    <td>:</td>
                    <td>${totalVat.toFixed(2)} ${currencySymbol}</td>
                  </tr>
                  <tr>
                    <th>Shipping Charge</th>
                    <td>:</td>
                    <td>${shippingCharge.toFixed(2)} ${currencySymbol}</td>
                  </tr>
                  <tr>
                    <th>Processing Fee</th>
                    <td>:</td>
                    <td>${processing_fee.toFixed(2)} ${currencySymbol}</td>
                  </tr>

                  <tr>
                    <th>Donation</th>
                    <td>:</td>
                    <td>${donation.toFixed(2)} ${currencySymbol}</td>
                  </tr>

                  <tr>
                    <th>Discount</th>
                    <td>:</td>
                    <td>-${discount.toFixed(2)} ${currencySymbol}</td>
                  </tr>

                  <tr>
                    <th>Partial Amount To Be Collected From Card Orders </th>
                    <td>:</td>
                    <td>${partialAmount.toFixed(2)} ${currencySymbol}</td>
                  </tr>

                  <tr>
                    <th>Grand Total.</th>
                    <td>:</td>
                    <td><b>${grandTotal.toFixed(2)} ${currencySymbol}</b></td>
                  </tr>`;

                  if (totalWarrantyAmount > 0) {
                  reportHtml += `
                  <tr>
                    <th>Warranty Amount</th>
                    <td>:</td>
                    <td><b>${totalWarrantyAmount.toFixed(2)} ${currencySymbol}</b></td>
                  </tr>`;
                  }

  reportHtml += `
                </table>
              </div>
            </div>
          </div>

        </div> <!-- end of printable area -->

        <div class="box-footer">
          <div class="report-actions">
            <a id="print-report-button" href="#" class="btn btn-default">
              <i class="fa fa-print"></i> Print
            </a>
            <a href="/api/reports/senderReport/export?${exportParams}" class="btn btn-default">
              <i class="fa fa-file-excel-o"></i> Excel Download
            </a>
          </div>
        </div>
      </div>
    </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').on('click', e => {
    e.preventDefault();
    printTableData();
  });
}



});