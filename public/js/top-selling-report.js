$(document).ready(function() {

    // ✅ Initialize AJAX-enabled Select2 dropdowns
    function initializeAjaxSelect2(element, url, placeholder, multiple = false) {
        $(element).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            multiple: multiple,
            ajax: {
                url,
                dataType: 'json',
                processResults: data => ({ results: data })
            }
        });
    }

    // ✅ Apply Select2 and AJAX sources
    $('.select2').select2({ placeholder: 'Select One', allowClear: true, width: '100%' });
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('select[name="sug_buyer"]', '/api/lookups/buyers', 'Select Buyer');

    // ✅ SKU-based Select2 dropdown (searchable, dependent on buyer)
    $('#product').select2({
        placeholder: 'Search & Select SKUs',
        allowClear: true,
        width: '100%',
        multiple: true,
        minimumInputLength: 2,
        ajax: {
            url: '/api/lookups/skus',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    buyer: $('#buyer').val()
                };
            },
            processResults: function (data) {
                return { results: data.results };
            },
            cache: true
        }
    });

    // ✅ Fetch and render buyer suggestions on load or after submission
    async function fetchAndRenderSuggestions() {
        const buyer = $('#buyer').val();
        try {
            const response = await fetch(`/api/reports/suggestions?buyer=${buyer || ''}`);
            const result = await response.json();
            if (result.success) renderSuggestions(result.suggestions);
        } catch (err) {
            console.error('Failed to load suggestions', err);
        }
    }

    // ✅ Render suggestions table
    function renderSuggestions(suggestions) {
        let rows = '';
        suggestions.forEach((s, index) => {
            rows += `<tr>
                <td>${index + 1}</td>
                <td>${s.product_title}</td>
                <td>${s.buyer_name || ''}</td>
                <td>${new Date(s.post_date).toLocaleString()}</td>
                <td>${s.post_by || ''}</td>
                <td><button class="btn btn-danger btn-xs delete-suggestion" data-id="${s.id}"><i class="fa fa-trash"></i></button></td>
            </tr>`;
        });

        const table = `
            <table class="table table-condensed">
                <thead>
                    <tr><th>#</th><th>Title</th><th>Buyer</th><th>Date</th><th>By</th><th>Action</th></tr>
                </thead>
                <tbody>${rows}</tbody>
            </table>`;

        $('#suggestions-list').html(table);
    }

    // ✅ Handle report form submission and fetch report + suggestions
    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/top-selling-pending?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                fetchAndRenderSuggestions(); // refresh suggestions list
                $('#report-results-container').show();
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    // ✅ Add new suggestion form submission
    $('#add-suggestion-form').on('submit', async function(e) {
        e.preventDefault();

        const formData = $(this).serialize();

        try {
            const response = await fetch('/api/reports/suggestions/add', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                renderSuggestions(result.suggestions);
                $(this)[0].reset();
                $('select[name="sug_buyer"]').val(null).trigger('change');
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`Failed to add suggestion: ${err.message}`);
        }
    });

    // ✅ Delete suggestion action
    $(document).on('click', '.delete-suggestion', async function() {
        if (!confirm('Are you sure you want to delete this suggestion?')) return;

        const id = $(this).data('id');
        const buyer = $('#buyer').val();

        try {
            const response = await fetch('/api/reports/suggestions/delete', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id, buyer })
            });

            const result = await response.json();

            if (result.success) {
                renderSuggestions(result.suggestions);
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`Failed to delete suggestion: ${err.message}`);
        }
    });

    // ✅ Render dynamic report table for Top Selling Pending
    function renderReport(data) {
        const { reportData, countries } = data;

        // ✅ Header row for grouped countries
        let header1 = '<th class="sticky-col col-1 sticky-header" colspan="4">Product Details</th>';

        // ✅ Second row - actual column names
        let header2 = `
            <th class="sticky-col col-1 sticky-header">#</th>
            <th class="sticky-col col-2 sticky-header">SKU</th>
            <th class="sticky-col col-3 col-title sticky-header">Title</th>
            <th class="sticky-col col-4 sticky-header">Buyer</th>
        `;

        // ✅ Define custom colors for country blocks
        const countryColors = {
            1: 'uae-clr', 2: 'oman-clr', 3: 'qatar-clr',
            5: 'kuwait-clr', 6: 'bahrain-clr', 7: 'saudi-clr'
        };

        countries.forEach(c => {
            const className = countryColors[c.id] || '';
            header1 += `<th colspan="5" class="text-center ${className}">${c.name}</th>`;
            header2 += `
                <th class="${className}">Qty</th>
                <th class="${className}">Tr Qty</th>
                <th class="${className}">Cost</th>
                <th class="${className}">Price</th>
                <th class="${className}">Profit</th>`;
        });

        // ✅ Report body content generation
        let bodyRows = '';
        if (reportData.length === 0) {
            bodyRows = '<tr><td colspan="100%" class="text-center">No data found for the selected filters.</td></tr>';
        } else {
            reportData.forEach((row, index) => {
                bodyRows += `<tr>
                    <td class="sticky-col col-1">${index + 1}</td>
                    <td class="sticky-col col-2">${row.sku}</td>
                    <td class="sticky-col col-3 col-title">${row.name}</td>
                    <td class="sticky-col col-4">${row.buyer}</td>`;

                countries.forEach(c => {
                    const countryData = row.countries[c.id] || {};
                    const aedPrice = (countryData.price || 0) * (c.currency_value || 1);
                    let qtyClass = countryColors[c.id] || '';

                    // ✅ Highlight logic for low/avg quantities
                    if ((countryData.cqty + countryData.tqty) < 0) qtyClass += ' low-qty';
                    else if ((countryData.cqty + countryData.tqty) < 10 && aedPrice > 100) qtyClass += ' avg-qty';
                    else if ((countryData.cqty + countryData.tqty) < 15 && aedPrice < 100) qtyClass += ' avg-qty';

                    bodyRows += `
                        <td class="${qtyClass}">${countryData.cqty}</td>
                        <td class="${qtyClass}">${countryData.tqty}</td>
                        <td class="text-right ${qtyClass}">${parseFloat(countryData.cost || 0).toFixed(2)}</td>
                        <td class="text-right ${qtyClass}">${parseFloat(countryData.price || 0).toFixed(2)}</td>
                        <td class="text-right ${qtyClass}">${parseFloat(countryData.profit || 0).toFixed(2)}</td>`;
                });

                bodyRows += '</tr>';
            });
        }

        // ✅ Final report structure
        const tableHtml = `
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">Product Details</h3>
                </div>
                <div class="box-body">
                    <div class="table-container">
                        <table class="table table-bordered table-hover report-table">
                            <thead class="sticky-header">
                                <tr>${header1}</tr>
                                <tr>${header2}</tr>
                            </thead>
                            <tbody>${bodyRows}</tbody>
                        </table>
                    </div>
                </div>
            </div>`;

        $('#report-results-container').html(tableHtml);
    }

    // ✅ Load suggestions initially
    fetchAndRenderSuggestions();
});
