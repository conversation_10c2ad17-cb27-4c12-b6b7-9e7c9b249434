$(document).ready(function() {
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });
    $('.select2').select2();

    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        const formData = $(this).serialize();
        $('#report-results-container').hide();
        $('#loading-spinner').show();
        try {
            const response = await fetch(`/api/reports/consolidate-os?${formData}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data, Object.fromEntries(new URLSearchParams(formData)));
                $('#report-results-container').show();
            } else { throw new Error(result.error || 'Failed to fetch report'); }
        } catch (err) { alert(`An error occurred: ${err.message}`); } 
        finally { $('#loading-spinner').hide(); }
    });

    const formatNum = (num) => (num || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    const formatInt = (num) => (num || 0).toLocaleString();

  function renderReport(data, filters) {
    let titleDate = filters.fromDate || new Date().toISOString().slice(0, 10);
    if (filters.toDate) titleDate += ` - ${filters.toDate}`;

    let statusReportRows = '';
    const grandTotals = {
        orders: 0, value: 0, abv: 0,
        deliveredCount: 0, deliveredValue: 0,
        cancelledCount: 0, cancelledBeforeCount: 0, cancelledBeforeValue: 0, cancelledAfterCount: 0, cancelledAfterValue: 0,
        dispatchedCount: 0, dispatchedValue: 0, toBeDispatchedCount: 0, toBeDispatchedValue: 0
    };
    const countryNames = Object.keys(data);

    // Calculate total orders
    countryNames.forEach(countryName => {
        grandTotals.orders += parseFloat(data[countryName].totalOrders || 0);
    });

    countryNames.forEach(countryName => {
        const country = data[countryName];
        const countryAbv = country.totalOrders > 0 ? country.totalValue / country.totalOrders : 0;

        grandTotals.value += parseFloat(country.totalValue || 0);
        grandTotals.abv += countryAbv;

        grandTotals.deliveredCount += parseFloat(country.deliveredCount || 0);
        grandTotals.deliveredValue += parseFloat(country.deliveredValueCM || 0); // renamed CM to value

        grandTotals.cancelledCount += parseFloat(country.cancelledCount || 0);
        grandTotals.cancelledBeforeCount += parseFloat(country.cancelledBeforeCount || 0);
        grandTotals.cancelledBeforeValue += parseFloat(country.cancelledBeforeValue || 0);
        grandTotals.cancelledAfterCount += parseFloat(country.cancelledAfterCount || 0);
        grandTotals.cancelledAfterValue += parseFloat(country.cancelledAfterValue || 0);

        grandTotals.dispatchedCount += parseFloat(country.dispatchedCount || 0);
        grandTotals.dispatchedValue += parseFloat(country.dispatchedValue || 0);
        grandTotals.toBeDispatchedCount += parseFloat(country.toBeDispatchedCount || 0);
        grandTotals.toBeDispatchedValue += parseFloat(country.toBeDispatchedValue || 0);

        const totalCancelledValueForReport = (country.cancelledBeforeValue || 0) + (country.cancelledAfterValue || 0);
        const toBeDispatchedCount = country.totalOrders - country.deliveredCount - country.cancelledCount - country.dispatchedCount;

        statusReportRows += `
            <tr>
                <td style="border-right:3px groove black; font-weight:bold;">${countryName}</td>
                <td class="text-center">${formatInt(country.totalOrders)}</td>
                <td class="text-center"><b>${grandTotals.orders > 0 ? Math.round(country.totalOrders / grandTotals.orders * 100) : 0}</b></td>
                <td class="text-right">${formatNum(country.totalValue)}</td>
                <td style="border-right:3px groove black;" class="text-right">${formatNum(countryAbv)}</td>

                <td class="text-center">${formatInt(country.deliveredCount)}</td>
                <td class="text-center"><b>${country.totalOrders > 0 ? Math.round(country.deliveredCount / country.totalOrders * 100) : 0}</b></td>
                <td class="text-right" style="border-right:3px groove black;">${formatNum(country.deliveredValueCM)}</td>

                <td class="text-center">${formatInt(country.cancelledCount)}</td>
                <td class="text-center"><b>${country.totalOrders > 0 ? Math.round(country.cancelledCount / country.totalOrders * 100) : 0}</b></td>
                <td class="text-center">${formatInt(country.cancelledBeforeCount)}</td>
                <td class="text-right">${formatNum(country.cancelledBeforeValue)}</td>
                <td class="text-center">${formatInt(country.cancelledAfterCount)}</td>
                <td class="text-right">${formatNum(country.cancelledAfterValue)}</td>
                <td style="border-right:3px groove black;" class="text-right">${formatNum(totalCancelledValueForReport)}</td>

                <td class="text-center">${formatInt(country.dispatchedCount)}</td>
                <td class="text-center"><b>${country.totalOrders > 0 ? Math.round(country.dispatchedCount / country.totalOrders * 100) : 0}</b></td>
                <td style="border-right:3px groove black;" class="text-right">${formatNum(country.dispatchedValue)}</td>

                <td class="text-center">${formatInt(toBeDispatchedCount)}</td>
                <td class="text-center"><b>${country.totalOrders > 0 && toBeDispatchedCount > 0 ? Math.round(toBeDispatchedCount / country.totalOrders * 100) : 0}</b></td>
                <td class="text-right">${formatNum(country.toBeDispatchedValue)}</td>
            </tr>`;
    });

    const totalCancelledValueGrand = grandTotals.cancelledBeforeValue + grandTotals.cancelledAfterValue;
    const totalRow = `
        <tr style="background-color:black;color:white;font-weight:bold;">
            <td>Total</td>
            <td class="text-center">${formatInt(grandTotals.orders)}</td><td></td><td class="text-right">${formatNum(grandTotals.value)}</td>
            <td class="text-right" style="border-right:3px groove black;">${formatNum(grandTotals.abv)}</td>
            <td class="text-center">${formatInt(grandTotals.deliveredCount)}</td><td></td><td class="text-right" style="border-right:3px groove black;">${formatNum(grandTotals.deliveredValue)}</td>
            <td class="text-center">${formatInt(grandTotals.cancelledCount)}</td><td></td><td class="text-center">${formatInt(grandTotals.cancelledBeforeCount)}</td>
            <td class="text-right">${formatNum(grandTotals.cancelledBeforeValue)}</td>
            <td class="text-center">${formatInt(grandTotals.cancelledAfterCount)}</td>
            <td class="text-right">${formatNum(grandTotals.cancelledAfterValue)}</td>
            <td class="text-right" style="border-right:3px groove black;">${formatNum(totalCancelledValueGrand)}</td>
            <td class="text-center">${formatInt(grandTotals.dispatchedCount)}</td><td></td><td class="text-right" style="border-right:3px groove black;">${formatNum(grandTotals.dispatchedValue)}</td>
            <td class="text-center">${formatInt(grandTotals.toBeDispatchedCount)}</td><td></td><td class="text-right">${formatNum(grandTotals.toBeDispatchedValue)}</td>
        </tr>`;
    statusReportRows += totalRow;

    const statusTableHeaders = `
        <tr style="background-color:#e0e0e0;border-top:3px groove black;">
            <th rowspan="2" class="text-center" style="vertical-align: middle; border-right:3px groove black;">Regions</th>
            <th colspan="4" class="text-center" style="background-color:#ffd596; border-right:3px groove black;">ORDER</th>
            <th colspan="3" class="text-center" style="background-color:#b6fac3; border-right:3px groove black;">DELIVERED</th>
            <th colspan="7" class="text-center" style="background-color:#ffd596; border-right:3px groove black;">CANCELLED</th>
            <th colspan="3" class="text-center" style="background-color:#b6fac3; border-right:3px groove black;">DISPATCHED</th>
            <th colspan="3" class="text-center" style="background-color:#ffd596;">TO BE DISPATCHED</th>
        </tr>
        <tr style="background-color:#e0e0e0;border-top:2.2px groove black;">
            <th class="text-center">COUNT</th><th class="text-center">%</th><th class="text-center">VALUE</th><th class="text-center" style="border-right:3px groove black;">ABV</th>
            <th class="text-center">COUNT</th><th class="text-center">%</th><th class="text-center" style="border-right:3px groove black;">VALUE</th>
            <th class="text-center">COUNT</th><th class="text-center">%</th><th class="text-center">BD COUNT</th><th class="text-center">BD</th>
            <th class="text-center">AD COUNT</th><th class="text-center">AD</th><th class="text-center" style="border-right:3px groove black;">TC</th>
            <th class="text-center">COUNT</th><th class="text-center">%</th><th class="text-center" style="border-right:3px groove black;">VALUE</th>
            <th class="text-center">COUNT</th><th class="text-center">%</th><th class="text-center">VALUE</th>
        </tr>`;

    const reportHtml = `
        <div class="box box-primary"><div class="box-body conso-re">
        <div class="table-container">
            <table class="table table-bordered report-table">
                <thead>
                    <tr><th colspan="24" class="text-center" style="background-color:black;color:white;">CONSOLIDATE ORDER STATUS REPORT</th></tr>
                    <tr style="border-top:3px groove black;"><th colspan="24" class="text-center" style="background-color:powderblue;">${titleDate}</th></tr>
                    ${statusTableHeaders}
                </thead>
                <tbody>${statusReportRows}</tbody>
            </table>
        </div></div></div>`;

    $('#report-results-container').html(reportHtml);
}

});