$(document).ready(function () {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d H:i" });
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }


      // Initialize Country dropdown (AJAX)
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');

    // Disable sender initially
    $('#sender').select2({
        placeholder: 'Select Sender',
        disabled: true
    });

    $('#emirate').select2({
        placeholder: 'Select Emirate',
        disabled: true
    });

    // Use delegated event binding to ensure the change is captured correctly
    $(document).on('change', '#country', function () {
        const selectedCountry = $(this).val();


        if (selectedCountry) {
            $('#sender').prop('disabled', false);
            $('#emirate').prop('disabled', false);

            $('#sender').empty().select2({
                placeholder: 'Select Sender',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/senders?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });

            $('#emirate').empty().select2({
                placeholder: 'Select Emirate',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/emirates?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });

        } else {
            $('#sender').val(null).trigger('change');
            $('#sender').prop('disabled', true);
            $('#emirate').val(null).trigger('change');
            $('#emirate').prop('disabled', true);
        }
    });

    $('#emirate').on('change', function () {
    const emirateId = $(this).val();

    if (emirateId) {
        $('#area').prop('disabled', false).empty().select2({
            placeholder: 'Select Area',
            allowClear: true,
            width: '100%',
            ajax: {
                url: `/api/lookups/areas?emirate=${emirateId}`,
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return { results: data };
                },
                cache: true
            }
        });
    } else {
        $('#area').val(null).trigger('change').prop('disabled', true);
    }
  });

    
    initializeAjaxSelect2('#staffName', '/api/lookups/staff', 'Select Staff Name');
    initializeAjaxSelect2('#cancelled_staffName', '/api/lookups/staff', 'Select Staff Name');
    initializeAjaxSelect2('#paymentGatewayType', '/api/lookups/payment-gateways', 'Select Gateway');
    initializeAjaxSelect2('#orderFrom', '/api/lookups/order-from', 'Select Order From');
    initializeAjaxSelect2('#cancelReason', '/api/lookups/cancel-reason', 'Select Cancel Reason');

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/cancelProductReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

    function renderReport(data) {
        const { orders, totals, filters } = data;
        const today = new Date();
        const currencySymbol = currencyMap[filters.country] || 'AED';
        const exportParams = $('#report-filters').serialize();
        let gTotal = 0; 
        let gCount = 0;

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body">
                <!-- This div contains everything that will be printed -->
                <div id="printable-order-report-area">
                    <div class="row">
                        <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
                        <div class="col-xs-6 text-right"><h5>Date: ${today.toLocaleDateString('en-GB')}</h5></div>
                    </div>
                    <div class="row"><div class="col-xs-12 text-center"><h4>CANCEL PRODUCT REPORT FROM ${filters.fromDate} TO ${filters.toDate}</h4></div></div><hr/>
                    <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead><tr><th>No</th><th>Country</th><th>Order Id</th><th>SKU</th><th>Product Name</th><th>Qty</th><th>Pay Mode</th>
                        <th>Order From </th><th>Cancel Det</th><th>Delivery Agent</th><th>Buyer</th><th>Duration</th><th>Amount</th></tr></thead>
                        <tbody>`;
            if (orders.length) {

                    
                orders.forEach((od, k) => {
                    const paytype = od.payment_mode === 'Credit' ? 'COD' : 'Card';
                    const payname = od.payment_method || '';
                    const order_confirm_by = od.confirmed_by || '';
                    const order_cancel_by = od.cancelled_by || '';
                    const reason = od.cancel_reason || '';
                    const sname = od.delivery_agent || '';
                    const dur = od.durdays || 0;
                    const totalPrice = od.price *  od.qty;
                    const displayAmount = parseFloat(totalPrice || 0).toFixed(2);
                    gTotal += parseFloat(displayAmount);
                    gCount += 1;
                    

                

                    reportHtml += `<tr>
                        <td>${k + 1}</td>
                        <td>${od.country_name || ''}</td>
                        <td>${od.orderid || ''}<br><a>${od.fort_id == 0 ? '' : od.fort_id || ''}</a></td>
                        <td>${od.sku || ''}</td>
                        <td>${od.product_name || ''}<br><a>${od.catname || ''}</a></br><a>${od.subcatname || ''}</a></td>
                        <td>${od.qty || ''}</td>
                        <td>${paytype}<br><b><a>${payname}</a></b></td>
                        <td><b>${order_confirm_by}</b><br>${new Date(od.order_date).toLocaleDateString()}<br><b><a>${od.orderfrom || ''}</a></b></td>
                        <td><b>${order_cancel_by}</b><br>${new Date(od.cancel_date).toLocaleDateString()}<br><b><a>${reason}</a></b></td>
                        <td>${sname}</td>
                        <td>${od.buyer || ''}</td>
                        <td>${dur} Days</td>
                        <td>${displayAmount}</td>
                    </tr>`;
                });
            }else {
            reportHtml += `<tr><td colspan="8" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `</tbody></table></div>
                <div class="row"><div class="col-xs-6"></div><div class="col-xs-6">
                    <table class="table summary-table"><tbody>
                        <tr><th>Total Orders</th><td>:</td><td>${parseFloat(gCount) || 0}</td></tr>
                        <tr><th>Total Amount</th><td>:</td><td>${parseFloat(totals?.totalAmount || 0).toFixed(2)}</td></tr>
                    </tbody></table></div></div><hr/>
                </div> <!-- End of printable area -->
            </div>

            <!-- **CHANGE 2**: Buttons are now in the box-footer, outside the printable area -->
            <div class="box-footer">
                <div class="report-actions">
                    <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                    <a href="/api/reports/cancelProductReport/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
                </div>
            </div>
        </div>`;

        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }
});