$(document).ready(function() {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };
    
    // --- Initialize Filters ---
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    function formatNum(num) {
        return parseFloat(num || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => {
                    const allOption = { id: 'all', text: 'All Country' };
                    return {
                        results: [allOption, ...data]
                    };
                },
                cache: true
            }
        });
    }

    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select a Country');

    // --- Form Submission ---
    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();
        
        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        if (!$('select[name="country"]').val()) {
            return alert('Please select a Country.');
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/sales-register?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
            } else { 
                throw new Error(result.error || 'Failed to fetch report'); 
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
            $('#report-results-container').show();
        }
    });

    // --- RENDER FUNCTION ---
    function renderReport(data) {
        const { data: reportData = [],summary: orderSummary = {}, filters } = data;
        const currencySymbol = currencyMap[filters.country] || 'USD';

        let reportHtml = `
            <div class="box box-primary">
                <div class="box-body">
                    <div class="table-container">
                        <table class="table table-striped table-bordered report-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>Date</th>
                                    <th>Order ID</th>
                                    <th>Category</th>
                                    <th>Sub Category</th>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>QTY</th>
                                    <th>Selling Price (Before VAT)</th>
                                    <th>VAT on Selling Price</th>
                                    <th>Selling Price</th>
                                    <th>Payment Mode</th>
                                    <th>Payment Type</th>
                                </tr>
                            </thead>
                            <tbody>`;

        if (reportData.length) {
            reportData.forEach((item, index) => {
                reportHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.date ? new Date(item.date).toLocaleDateString('en-GB') : 'N/A'}</td>
                        <td>${item.order_id || 'N/A'}</td>
                        <td>${item.category || 'N/A'}</td> 
                        <td>${item.sub_category || 'N/A'}</td> 
                        <td>${item.product_name || 'N/A'}</td>
                        <td>${item.sku || 'N/A'}</td>
                        <td>${item.qty || 0}</td>
                        <td>${currencySymbol} ${parseFloat(item.selling_price_before_vat || 0).toFixed(2)}</td> 
                        <td>${currencySymbol} ${parseFloat(item.selling_vat || 0).toFixed(2)}</td> 
                        <td>${currencySymbol} ${parseFloat(item.selling_price || 0).toFixed(2)}</td>
                        <td>${
                            item.payment_mode 
                                ? (item.payment_mode == 1 ? 'CreditCardPayments' : 'CashOnDelivery') 
                                : 'N/A'
                        }</td>
                        <td>${item.payment_type || 'N/A'}</td>
                    </tr>`;
            });
        } else {
            // Change 4: 'colspan' ko 13 kar diya gaya hai.
            reportHtml += '<tr><td colspan="13" class="text-center">No records found.</td></tr>';
        }

        reportHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>`;

                // --- CALCULATIONS FOR DELIVERY SUMMARY ---
                const totalQuantity = reportData.reduce((sum, item) => sum + (parseFloat(item.qty) || 0), 0);
                const orderTotal = reportData.reduce((sum, item) => sum + (parseFloat(item.selling_price) || 0), 0);

                // Get values from the summary object fetched from the backend
                const salesReturn = parseFloat(orderSummary.salesReturn || 0);
                const discount = parseFloat(orderSummary.discount || 0);
                const shippingCharge = parseFloat(orderSummary.shippingcharge || 0);
                const processingFee = parseFloat(orderSummary.pfee || 0);
                const donation_fee = parseFloat(orderSummary.donation_fee || 0);
                const balance_amount = parseFloat(orderSummary.balance_amount || 0);
                const credit_amount = parseFloat(orderSummary.credit_amount || 0);
                const tax_amount = parseFloat(orderSummary.tax_amount || 0);
                const grand_total = parseFloat(orderSummary.grand_total || 0);

                const discountedAmount = orderTotal - discount;
                const adjustedTotal = (discountedAmount - salesReturn) + shippingCharge + processingFee;

                // --- DELIVERY SUMMARY ---
                const summaryHtml = `
                <div class="row">
                    <div class="col-xs-6">&nbsp;</div>
                    <div class="col-xs-6">
                    <div class="table" style="float:right;">
                    <table class="table table-sm table-bordered" style="border-width: 2px;">
                        <tr><th colspan="3"><h3>Delivery Summary</h3></th></tr>
                        <tr><th>No of items</th><td>:</td><td class="text-right">${formatNum(totalQuantity)}</td></tr>
                        <tr><th>Total Orders</th><td>:</td><td class="text-right">${formatNum(orderSummary.totalorder || 0)}</td></tr>
                        <tr><th>Deliverd Incl. VAT</th><td>:</td><td class="text-right">${formatNum(orderTotal)}</td></tr>
                        <tr><th>Delivery Return</th><td>:</td><td class="text-right">${formatNum(salesReturn)}</td></tr>
                        <tr><th>Discount</th><td>:</td><td class="text-right">${formatNum(discount)}</td></tr>
                        <tr><th><strong>Total Delivery Inc.VAT</strong></th><td>:</td><td class="text-right"><strong>${formatNum(discountedAmount)}</strong></td></tr>
                        <tr><th>Shipping Charge</th><td>:</td><td class="text-right">${formatNum(shippingCharge)}</td></tr>
                        <tr><th>Processing Fee</th><td>:</td><td class="text-right">${formatNum(processingFee)}</td></tr>
                        <tr><th>Donation Fee</th><td>:</td><td class="text-right">${formatNum(donation_fee)}</td></tr>
                       
                        <tr><th><strong>Net Delivery Inc.VAT</strong></th><td>:</td><td class="text-right"><strong>${formatNum(grand_total)} ${currencySymbol}</strong></td></tr>
                        </table>
                    </div>
                    </div>
                </div>

                <div class="box-footer">
                    <a href="/api/reports/sales-register/export?${$('#report-filters').serialize()}" class="btn btn-default">
                        <i class="fa fa-file-excel-o"></i> Export to Excel
                    </a>
                </div>
            </div>`;

        $('#report-results-container').html(reportHtml + summaryHtml).show();
    }
});