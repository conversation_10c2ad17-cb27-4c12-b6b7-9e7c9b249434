$(document).ready(function() {
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder, allowClear: true, width: '100%',
            ajax: { url: apiUrl, dataType: 'json', delay: 250, processResults: data => ({ results: data }), cache: true }
        });
    }

    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#sender', '/api/lookups/senders', 'Select Sender');
    initializeAjaxSelect2('#staff', '/api/lookups/staff', 'Select Staff');
    initializeAjaxSelect2('#orderfrom', '/api/lookups/order-from', 'Select Order From');
    
    const currentYear = new Date().getFullYear();
    const $yearSelect = $('#year');
    for (let i = currentYear; i >= 2015; i--) {
        $yearSelect.append(new Option(i, i));
    }

    $('#monthly-order-filters').on('submit', async function(e) {
        e.preventDefault();
        if (!$('#country').val() || !$('#month').val() || !$('#year').val()) {
            alert('Country, Year, and Month are required.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/monthly-order?${formData}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else { throw new Error(result.error || 'Failed to fetch report data.'); }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function renderReport(data) {
        const { reportData, totals } = data;
        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#monthly-order-filters').serialize();
        const monthName = $('#month option:selected').text();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
                    <div class="col-xs-6 text-right"><h5>Date: ${today}</h5></div>
                </div>
                <div class="row"><div class="col-xs-12 text-center"><h4>${monthName} Month Date Wise Report</h4></div></div><hr/>
                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead><tr><th>No</th><th>Order Date</th><th>Order Count</th><th>Value</th><th>Shipping Charge</th><th>Total Value</th></tr></thead>
                        <tbody>`;

        if (reportData.length) {
            reportData.forEach((row, index) => {
                const totalValue = parseFloat(row.value) + parseFloat(row.shippingcharge);
                reportHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${new Date(row.order_date).toLocaleDateString('en-GB')}</td>
                        <td>${row.orders}</td>
                        <td>${parseFloat(row.value).toFixed(2)}</td>
                        <td>${parseFloat(row.shippingcharge).toFixed(2)}</td>
                        <td>${totalValue.toFixed(2)}</td>
                    </tr>`;
            });
        } else {
            reportHtml += `<tr><td colspan="6" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `</tbody></table></div><hr/>
                <div class="row"><div class="col-xs-6"></div><div class="col-xs-6"><div class="table-responsive">
                    <table class="table">
                        <tbody>
                            <tr><th style="width:50%">Total Orders:</th><td>${totals.totalOrders}</td></tr>
                            <tr><th>Total Amount:</th><td>${totals.totalAmount.toFixed(2)}</td></tr>
                            <tr><th>Shipping Charge:</th><td>${totals.shippingCharge.toFixed(2)}</td></tr>
                            <tr><th>Grand Total:</th><td><strong>${totals.grandTotal.toFixed(2)} AED</strong></td></tr>
                        </tbody>
                    </table>
                </div></div></div>
            </div>
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/monthly-order/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;
        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => { e.preventDefault(); printTableData(); });
    }

    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) {
            return alert('Report content not found to print.');
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Monthly Order Report</title>');
        
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; font-size: 12px; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; padding: 0 15px; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; margin-top: 1rem; margin-bottom: 1rem; }
                img { max-width: 150px; }
                .table-responsive { display: block; width: 100%; overflow-x: auto; }
            </style>
        `);
        
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }
});