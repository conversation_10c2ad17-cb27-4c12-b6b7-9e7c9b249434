$(document).ready(function () {
    // ✅ Map country codes to currency symbols
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

    // ✅ Initialize date picker with time
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Initialize Select2 single and multi-selects
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    // ✅ Helper to initialize Select2 with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dynamic dropdowns
    // initializeAjaxSelect2('#sender', '/api/lookups/senders', 'Select Sender');
    initializeAjaxSelect2('#staffName', '/api/lookups/staff', 'Select Staff Name');
    initializeAjaxSelect2('#nationality', '/api/lookups/nationalities', 'Select Nationality');
    initializeAjaxSelect2('#paymentGatewayType', '/api/lookups/payment-gateways', 'Select Gateway');
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');

    // Disable sender initially
    $('#sender').select2({
        placeholder: 'Select Sender',
        disabled: true
    });

    // Use delegated event binding to ensure the change is captured correctly
    $(document).on('change', '#country', function () {
        const selectedCountry = $(this).val();
        if (selectedCountry) {
            $('#sender').prop('disabled', false);

            $('#sender').empty().select2({
                placeholder: 'Select Sender',
                allowClear: true,
                width: '100%',
                ajax: {
                    url: `/api/lookups/senders?country=${selectedCountry}`,
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return { results: data };
                    },
                    cache: true
                }
            });
        } else {
            $('#sender').val(null).trigger('change');
            $('#sender').prop('disabled', true);
        }
    });

    // ✅ Handle report filter form submission
    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            // ✅ Fetch report data
            const response = await fetch(`/api/reports/dailyOrderStatus?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);           // ✅ Render report if successful
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();           // ✅ Always hide loader
        }
    });

    // ✅ Print report window setup
    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

function formatLocalDate(dateStr) {
    const d = new Date(dateStr);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0'); // months 0-11
    const day = String(d.getDate()).padStart(2, '0');
    return `${day}-${month}-${year}`;
}   

function renderReport(data) {
    const { orderPlatform, grandTotal, filters } = data;
    const today = new Date();
    const currencySymbol = currencyMap[filters.country] || 'AED';

    let reportHtml = `
    <div class="box box-primary">
        <div class="box-body">
            <!-- ✅ Printable Area Start -->
            <div id="printable-order-summary-area">
                <div class="row">
                    <div class="col-xs-6">
                        <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
                    </div>
                    <div class="col-xs-6 text-right">
                        <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 text-center">
                        <h4>ORDER SUMMARY REPORT FROM ${filters.fromDate} TO ${filters.toDate}</h4>
                    </div>
                </div><hr/>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped thin-bordered">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th colspan="2">Total Orders</th>
                                <th colspan="2">Delivered</th>
                                <th colspan="4">Cancelled</th>
                                <th colspan="2">Dispatch</th>
                                <th colspan="2">To Be Dispatched</th>
                                
                            </tr>
                            <tr>
                                <th></th>
                                <th>Orders</th>
                                <th>Value (${currencySymbol})</th>
                                <th>Delivered</th>
                                <th>Value (${currencySymbol})</th>
                                <th>Cancelled BF</th>
                                <th>Value (${currencySymbol})</th>
                                <th>Cancelled AF</th>
                                <th>Value (${currencySymbol})</th>
                                <th>Dispatch</th>
                                <th>Value (${currencySymbol})</th>
                                <th>To Be Dispatched</th>
                                <th>Value (${currencySymbol})</th>
                               
                            </tr>
                        </thead>
                        <tbody>`;

    // Loop through each date entry
    Object.keys(orderPlatform).forEach(date => {
        const row = orderPlatform[date];

        // Format row date -> YYYY-MM-DD
        const formattedDate = formatLocalDate(date);

        // Combine dispatched + out_for_delivery
        const dispatchedCount = row.dispatched.count + row.out_for_delivery.count;
        const dispatchedAmount = row.dispatched.amount + row.out_for_delivery.amount;

        // Others bucket
        const othersCount = row.others?.count || 0;
        const othersAmount = row.others?.amount || 0;

        reportHtml += `
            <tr>
                <td>${formattedDate}</td>
                <td>${row.total_orders}</td>
                <td>${parseFloat(row.total_amount).toFixed(2)}</td>
                <td>${row.delivered.count}</td>
                <td>${parseFloat(row.delivered.amount).toFixed(2)}</td>
                <td>${row.cancelled_before_dispatch.count}</td>
                <td>${parseFloat(row.cancelled_before_dispatch.amount).toFixed(2)}</td>
                <td>${row.cancelled_after_dispatch.count}</td>
                <td>${parseFloat(row.cancelled_after_dispatch.amount).toFixed(2)}</td>
                <td>${dispatchedCount}</td>
                <td>${parseFloat(dispatchedAmount).toFixed(2)}</td>
                <td>${row.pending.count}</td>
                <td>${parseFloat(row.pending.amount).toFixed(2)}</td>
            </tr>`;
    });

    // ✅ Grand Total Row
    const grandOthersCount = grandTotal.others?.count || 0;
    const grandOthersAmount = grandTotal.others?.amount || 0;
    const grandDispatchedCount = grandTotal.dispatched.count + grandTotal.out_for_delivery.count;
    const grandDispatchedAmount = grandTotal.dispatched.amount + grandTotal.out_for_delivery.amount;

    reportHtml += `
        <tr style="font-weight:bold; background:#f9f9f9;">
            <td>Grand Total</td>
            <td>${grandTotal.total_orders}</td>
            <td>${parseFloat(grandTotal.total_amount).toFixed(2)}</td>
            <td>${grandTotal.delivered.count}</td>
            <td>${parseFloat(grandTotal.delivered.amount).toFixed(2)}</td>
            <td>${grandTotal.cancelled_before_dispatch.count}</td>
            <td>${parseFloat(grandTotal.cancelled_before_dispatch.amount).toFixed(2)}</td>
            <td>${grandTotal.cancelled_after_dispatch.count}</td>
            <td>${parseFloat(grandTotal.cancelled_after_dispatch.amount).toFixed(2)}</td>
            <td>${grandDispatchedCount}</td>
            <td>${parseFloat(grandDispatchedAmount).toFixed(2)}</td>
            <td>${grandTotal.pending.count}</td>
            <td>${parseFloat(grandTotal.pending.amount).toFixed(2)}</td>
        </tr>
    </tbody></table>
    </div><hr/>
    </div> <!-- ✅ Printable Area End -->
    </div>

    <!-- ✅ Action Buttons -->
    <div class="box-footer">
        <div class="report-actions">
          
            <a href="/api/reports/dailyOrderStatus/export?${$('#report-filters').serialize()}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
        </div>
    </div>`;

    // ✅ Inject into DOM
    $('#report-results-container').html(reportHtml);

    // ✅ Bind print
    $('#print-summary-button').on('click', e => {
        e.preventDefault();
        printTableData('#printable-order-summary-area');
    });
}

    
});
