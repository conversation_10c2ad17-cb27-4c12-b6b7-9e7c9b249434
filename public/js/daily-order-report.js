$(document).ready(function () {
    
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate) {
            alert('Please select "From Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/dailyOrderReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

  
function renderReport(data) {
    const { ordersData, orderPlatform, filters } = data;
    const today = new Date();
    
    const platformKeys = {
        Website: 'Website',
        Android: 'Android',
        IOS: 'iOS',
        WhatsApp: 'WhatsApp',
        Facebook: 'Facebook',
        WebFeed: 'WebFeed',
        Telephone: 'Telephone',
        Fbfeedlist: 'Fb Feed List',
        WebFeedOs : 'WebFeedOs',
        APP: 'APP',
        Other: 'Other'
    };

    let reportHtml = `
    <div class="box box-primary">
        <div class="box-body">
            <div id="printable-order-report-area">
                <div class="row">
                    <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
                    <div class="col-xs-6 text-right"><h5>Date: ${today.toLocaleDateString('en-GB')}</h5></div>
                </div>
                <div class="row">
                    <div class="col-xs-12 text-center"><h4>DAILY ORDER REPORT FROM ${filters.fromDate || 'Start'} TO ${filters.toDate || filters.fromDate}</h4></div>
                </div><hr/>
                <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                    <tr>
                        <th>Country</th>
                        <th>Orders</th>
                        <th>Value in AED</th>
                        <th>Dispatch</th>
                        <th>Value in AED</th>
                        <th>Delivered</th>
                        <th>Value in AED</th>
                    </tr>
                    </thead>
                    <tbody>`;

    let totalOrders = 0, totalDispatchOrders = 0, totalDeliveredOrders = 0;
    let totalValueAed = 0, totalDispatchValueAed = 0, totalDeliveredValueAed = 0;

    for (const [country, item] of Object.entries(ordersData)) {
        const totalValue = parseFloat(item.totalValue) || 0;
        const totalValueAedLocal = parseFloat(item.totalValueAed) || 0;
        const dispatchValue = parseFloat(item.totalDispatchValue) || 0;
        const dispatchValueAed = parseFloat(item.totalDispatchValueAed) || 0;
        const deliveredValue = parseFloat(item.totalDeliveredValue) || 0;
        const deliveredValueAed = parseFloat(item.totalDeliveredValueAed) || 0;

        reportHtml += `
        <tr>
            <td><img title="${country}" src="${item.webFlagImage}"></td>
            <td>${item.totalOrders}<br><a><b>${totalValue.toFixed(2)} ${item.currency}</b></a></td>
            <td style="background-color:rgba(0,166,90,0.2);">${totalValueAedLocal.toFixed(2)}</td>
            <td>${item.totalDispatchOrders}<br><b>${dispatchValue.toFixed(2)} ${item.currency}</b></td>
            <td style="background-color:rgba(223,156,18,0.2);">${dispatchValueAed.toFixed(2)}</td>
            <td>${item.totalDeliveredOrders}<br><a><b>${deliveredValue.toFixed(2)} ${item.currency}</b></a></td>
            <td style="background-color:rgba(223,37,58,0.2);">${deliveredValueAed.toFixed(2)}</td>
        </tr>`;

        totalOrders += item.totalOrders || 0;
        totalDispatchOrders += item.totalDispatchOrders || 0;
        totalDeliveredOrders += item.totalDeliveredOrders || 0;
        totalValueAed += totalValueAedLocal;
        totalDispatchValueAed += dispatchValueAed;
        totalDeliveredValueAed += deliveredValueAed;
    }

    reportHtml += `
        <tr>
            <td><b>Total</b></td>
            <td><b>${totalOrders}</b></td>
            <td style="background-color:rgba(0,166,90,0.2);"><b>${totalValueAed.toFixed(2)} AED</b></td>
            <td><b>${totalDispatchOrders}</b></td>
            <td style="background-color:rgba(223,156,18,0.2);"><b>${totalDispatchValueAed.toFixed(2)} AED</b></td>
            <td><b>${totalDeliveredOrders}</b></td>
            <td style="background-color:rgba(223,37,58,0.2);"><b>${totalDeliveredValueAed.toFixed(2)} AED</b></td>
        </tr>
    </tbody></table></div>

    <div class="table-responsive"><table class="table table-bordered table-striped">
        <thead><tr><th>Country</th>`;

    for (const label of Object.values(platformKeys)) {
        reportHtml += `<th>${label}</th>`;
    }

    reportHtml += `<th style="background-color:rgba(223,37,58,0.2);">Total</th></tr></thead><tbody>`;

    let allcount = 0;
    const grandTotals = {};

    for (const [country, op] of Object.entries(orderPlatform)) {
        const cvalue = parseFloat(op.cvalue) > 0 ? parseFloat(op.cvalue) : 1;
        reportHtml += `<tr><td><img title="${country}" src="${op.webFlagImage}"></td>`;

        for (const pKey of Object.keys(platformKeys)) {
            const count = parseInt(op[pKey + 'total']) || 0;
            const amount = parseFloat(op[pKey + '_amt']) || 0;

            reportHtml += `<td>${count}<br><a><b>${amount.toFixed(2)} ${op.currency}</b></a></td>`;
            
            grandTotals[pKey] = grandTotals[pKey] || { count: 0, amt: 0 };
            grandTotals[pKey].count += count;
            grandTotals[pKey].amt += amount * cvalue;
        }

        const gTotal = parseFloat(op.grandTot) || 0;
        const gCount = parseInt(op.grandCount) || 0;
        allcount += gCount;

        reportHtml += `<td style="background-color:rgba(223,37,58,0.2);">
            ${gCount}<br><a><b>${gTotal.toFixed(2)} ${op.currency}</b></a><br>
            <a style="color:#009900"><b>${(gTotal * cvalue).toFixed(2)} AED</b></a></td></tr>`;
    }

    const allTotal = Object.values(grandTotals).reduce((sum, item) => sum + (parseFloat(item.amt) || 0), 0);

    reportHtml += `<tr style="background-color:rgba(223,37,58,0.2);"><td><b>Total</b></td>`;

    for (const key of Object.keys(platformKeys)) {
        const gt = grandTotals[key] || { count: 0, amt: 0 };
        reportHtml += `<td><b>${gt.count}<br><a style="color:#009900">${gt.amt.toFixed(2)} AED</a></b></td>`;
    }

    reportHtml += `<td><b>${allcount}<br><a style="color:#009900">${allTotal.toFixed(2)} AED</a></b></td></tr>`;
    reportHtml += `</tbody></table></div></div>
    </div>`;

    $('#report-results-container').html(reportHtml);
    $('#print-report-button').on('click', e => {
        e.preventDefault();
        printTableData();
    });
}



});