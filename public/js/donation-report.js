// public/js/reports/donation-report.js

$(document).ready(function() {
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // Store Excel data globally
    let currentExcelData = null;
    let currentFileName = null;

    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        const formData = $(this).serialize();
        
        $('#report-results-container').hide();
        $('#loading-spinner').show();
        try {
            const response = await fetch(`/api/reports/donation?${formData}`);
            const result = await response.json();
            if (result.success) {

                currentExcelData = result.excelFile;
                currentFileName = result.fileName;


                renderReport(result.data);
                $('#report-results-container').show();
            } else { throw new Error(result.error || 'Failed to fetch report'); }
        } catch (err) { alert(`An error occurred: ${err.message}`); } 
        finally { $('#loading-spinner').hide(); }
    });
    
    $('#reset-button').on('click', function() {
        $('#report-filters')[0].reset();
        $('#report-results-container').hide();
    });

    // const formatNum = (num) => (num || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    
    function renderReport(data) {
        const today = new Date().toLocaleDateString('en-GB');
        let tableRows = '';
        const totals = data.totals;

        if (data && data.orders.length > 0) {
            data.orders.forEach((row, index) => {

                // // correct migration data for payment methods 
                // if(row.payment_method_name === null) {
                //     if (row.payment_method === 'Credit') row.payment_method_name = 'COD'
                //     if (row.payment_method === 'Cash') {
                //         if (row.gway_transaction_id.split('-').length === 1) {
                //             row.payment_method_name = 'Payfort'
                //         } else {
                //             row.payment_method_name = 'Tabby'
                //         }
                //     }
                // }

                // const charge = (parseFloat(row.donation_fee || 0) * parseFloat(Pay_percentage[row.payment_method_name] || 0)) / 100;
                // const total = parseFloat(row.donation_fee || 0) - charge;
                // let displayStatus = 'Pending';
                
                // if(row.payment_method_name != 'COD'){
                //     displayStatus = 'Received';
                //     totals.ramount += total;
                // } else {
                //     if(row.status.toLowerCase().includes('cancelled')) {
                //         displayStatus = 'Cancelled';
                //         totals.camount += total;
                //     } else if (row.status === 'Delivered') {
                //         displayStatus = 'Received';
                //         totals.ramount += total;
                //     } else {
                //         totals.pamount += total;
                //     }
                // }
                // if (row.status === 'Delivered') {
                //     displayStatus = 'Received';
                //     totals.ramount += total;
                // } else if (row.payment_method_name === 'COD' && row.status.toLowerCase().includes('cancelled')) {
                //     displayStatus = 'Cancelled';
                //     totals.camount += total;
                // } else {
                //     totals.pamount += total;
                // }
                // totals.ftotal += total;
                
                tableRows += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${row.orderid}<br>${new Date(row.order_date).toLocaleString()}<br><b><a>${row.status}</a></b></td>
                        <td>${row.contact || ''}</td>
                        <td>${row.customer || ''}</td>
                        <td>${row.payment_method_name || 'N/A'}</td>
                        <td class="text-right">${row.donation_fee}</td>
                        <td class="text-right">${row.charge}</td>
                        <td class="text-right"><b>${row.total}</b></td>
                        <td><b><a>${row.displayStatus}</a></b></td>
                    </tr>`;
            });
        } else {
            tableRows = '<tr><td colspan="9" class="text-center">No Records Found</td></tr>';
        }

        const summaryHtml = `
            <div class="row" style="margin-top: 20px;">
                <div class="col-xs-6"></div>
                <div class="col-xs-6">
                    <div class="table-responsive">
                        <table class="table">
                            <tr><th>Total</th><td>:</td><td><b>${totals.ftotal}</b></td></tr>
                            <tr><th>Pending</th><td>:</td><td><b>${totals.pamount}</b></td></tr>
                            <tr><th>Received</th><td>:</td><td><b>${totals.ramount}</b></td></tr>
                            <tr><th>Cancelled</th><td>:</td><td><b>${totals.camount}</b></td></tr>
                        </table>
                    </div>
                </div>
            </div>`;

        // const downloadButtonHtml = `
        //     <div class="text-left" style="margin-top: 20px;">
        //         <button id="download-excel" class="btn btn-success">
        //             <i class="fa fa-download"></i> Download Excel
        //         </button>
        //     </div>`;

        const downloadButtonHtml = `
        <div class="box-footer">
            <div class="report-actions">
              <a id="download-excel" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
          </div>`

        const reportHtml = `
            <div class="box box-primary">
                <div class="box-body" id="printable-area">
                    <div class="row">
                        <div class="col-xs-6"><img src="/images/logo.png" style="height:40px;"></div>
                        <div class="col-xs-6 text-right"><h5 style="margin:0;">Date: ${today}</h5></div>
                    </div><hr/>
                    <div class="table-container">
                        <table class="table table-striped report-table">
                            <thead>
                                <tr>
                                    <th>No</th><th>Order Details</th><th>Mobile</th><th>Customer</th>
                                    <th>Pay Mode</th><th class="text-right">Donation Fee</th><th class="text-right">Charges</th>
                                    <th class="text-right">Final Amount</th><th>Status</th>
                                </tr>
                            </thead>
                            <tbody>${tableRows}</tbody>
                        </table>
                    </div>
                    ${summaryHtml}
                </div>
                ${downloadButtonHtml}
            </div>`;
            
        $('#report-results-container').html(reportHtml);
         $('#download-excel').on('click', function() {
            downloadExcelReport();
        });
    }

        function downloadExcelReport() {
        if (!currentExcelData) {
            alert('No Excel data available. Please generate the report first.');
            return;
        }

        const button = $('#download-excel');
        
        // Show loading state
        button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Downloading...');
        
        try {
            // Convert base64 to blob
            const binaryString = window.atob(currentExcelData);
            const bytes = new Uint8Array(binaryString.length);
            
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            
            const blob = new Blob([bytes], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = currentFileName || 'Donation_Report.xlsx';
            
            // Trigger download
            document.body.appendChild(a);
            a.click();
            
            // Cleanup
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('Download error:', error);
            alert('Failed to download Excel file. Please try again.');
        } finally {
            // Reset button state
            setTimeout(() => {
                button.prop('disabled', false).html('<i class="fa fa-download"></i> Download Excel');
            }, 1000);
        }
    }
});