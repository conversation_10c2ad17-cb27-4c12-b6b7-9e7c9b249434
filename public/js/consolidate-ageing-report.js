$(document).ready(function () {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

     $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // Initialize Country dropdown (AJAX)
     initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/consolidateAgeingReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

  


function renderReport(data) {
  const { countries = {}, totals = {}, filters = {} } = data;
  const buyerName = filters.buyer_name || '';

  let table = `
    <table class="table table-striped">
      <thead>
        <tr style="text-align: center;background-color:black;color:white; font-size: 14px;">
          <th colspan="8" style="text-align:center;background-color:black;color:white;">Consolidate Stock Ageing Report ${buyerName ? '-- ' + buyerName : ''}</th>
        </tr>
        <tr>
          <th style="text-align:center; background-color:powderblue !important;">Country</th>               
          <th style="text-align:center; background-color:powderblue !important;">0-1</th>
          <th style="text-align:center; background-color:powderblue !important;">1-3</th>  
          <th style="text-align:center; background-color:powderblue !important;">3-6</th>  
          <th style="text-align:center; background-color:powderblue !important;">MORE THAN 6</th> 
          <th style="text-align:center; background-color:powderblue !important;">TOTAL</th>
          <th style="text-align:center; background-color:powderblue !important;">TRANSIT</th>
          <th style="text-align:center; background-color:powderblue !important;">NET</th>
        </tr>

      </thead>
      <tbody>`;

  for (const country in countries) {
    const row = countries[country] || {};
    const currentStock = row.currentStock || 0;
    const trans = row.trans || 0;
    const total = currentStock + trans;
  
    table += `
      <tr>
        <td style="text-align:center"><b>${country}</b></td>
        <td style="text-align:right">${(row.firstMonth || 0).toFixed(2)}</td>
        <td style="text-align:right">${(row.secondMonth || 0).toFixed(2)}</td>
        <td style="text-align:right">${(row.sixthMonth || 0).toFixed(2)}</td>
        <td style="text-align:right">${(row.lastYear || 0).toFixed(2)}</td>
        <td style="text-align:right"><b>${currentStock.toFixed(2)}</b></td>
        <td style="text-align:right">
          <a href="#" style="color: black;" data-toggle="modal" data-target="#${country.toLowerCase()}Trans">
            <b>${trans.toFixed(2)}</b>
          </a>
        </td>
        <td style="text-align:right"><b style="font-size:14px">${total.toFixed(2)}</b></td>
      </tr>
    `;
  }

  table += `
    <tr style="background-color:black;color:white;text-align: center; font-size: 15px;">
      <td><b>Total In AED</b></td>
      <td style="text-align:right">${(totals.firstMonthTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
      <td style="text-align:right">${(totals.secondMonthTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
      <td style="text-align:right">${(totals.sixthMonthTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
      <td style="text-align:right">${(totals.lastYearTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
      <td style="text-align:right"><b>${(totals.currentStockTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</b></td>
      <td style="text-align:right"><b>${(totals.totalTransit || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</b></td>
      <td style="text-align:right"><b style="font-size:16px">${(totals.totalNet || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</b></td>
    </tr>

    </tbody>
  </table>
  `;

  document.getElementById('report-results-container').innerHTML = table;
}



   function renderReportOld(data) {
  const { data: purchaseProducts = [], duration = 'N/A' ,filters } = data;
  const currencySymbol = currencyMap[filters.country] || 'AED';
  const today = new Date();

  let reportHtml = `
  <div class="box box-primary">
    <div class="box-body">
      <div id="printable-order-report-area">
        <div class="row">
          <div class="col-xs-6">
            <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
          </div>
          <div class="col-xs-6 text-right">
            <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12 text-center">
            <h4>AGEING REPORT - ${duration}</h4>
          </div>
        </div>
        <hr/>
        <div class="row">
          <div class="col-xs-12 table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Product</th>
                  <th>Supplier</th>
                  <th>Purchase Date</th>
                  <th>Order Date</th>
                  <th>Qty</th>
                  <th>Cost</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>`;

  let grandTotal = 0;
  let totalQuantity = 0;

  if (purchaseProducts.length) {
    purchaseProducts.forEach((product, i) => {
      const total = product.quantity * product.cost;
      grandTotal += total;
      totalQuantity += product.quantity;

      reportHtml += `
        <tr>
          <td style="width:40%">${product.product_name} <strong>[ ${product.product_code} ]</strong></td>
          <td><strong>${product.supplier_name || product.buyer_name || 'N/A'}</strong></td>
          <td>${product.order_date ? new Date(product.order_date).toLocaleDateString() : ''}</td>
          <td>
            ${product.latest_order_date || ''}<br/>
            ${
              product.latest_orderid
                ? `[<a><b>${product.latest_orderid}</b> - ${product.latest_qty || 0}</a>]`
                : ''
            }
          </td>
          <td>${product.quantity}</td>
          <td>${parseFloat(product.cost).toFixed(2)}</td>
          <td>${total.toFixed(2)}</td>
        </tr>`;
    });
  } else {
    reportHtml += `<tr><td colspan="7" class="text-center">Zero record found.</td></tr>`;
  }

  reportHtml += `
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-6">&nbsp;</div>
          <div class="col-xs-6">
            <div class="table-responsive" style="float:right;">
              <table class="table">
                <tr><th>Total Items</th><td>:</td><td>${purchaseProducts.length}</td></tr>
                <tr><th>Total Quantity</th><td>:</td><td>${totalQuantity}</td></tr>
                <tr><th>Total Amount</th><td>:</td><td>${currencySymbol} ${grandTotal.toFixed(2)}</td></tr>
              </table>
            </div>
          </div>
        </div>
      </div> <!-- End of printable area -->
    </div>
    <div class="box-footer">
      <div class="report-actions">
        <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
        <a href="/api/reports/ageingReport/export?${$('#report-filters').serialize()}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
      </div>
    </div>
  </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').on('click', e => {
    e.preventDefault();
    printTableData();
  });
}



function renderConsolidatedTable({ array = {}, totals = {}, buyer_name = '' }) {
  const countries = Object.keys(array);

  let tableHtml = `
  <table class="table table-striped">
    <thead>
      <tr style="text-align: center;background-color:black;color:white; font-size: 14px;">
        <th colspan="8" style="text-align:center;">Consolidate Stock Ageing Report ${buyer_name ? `-- ${buyer_name}` : ''}</th>
      </tr>
      <tr style="background-color:powderblue;">
        <th style="text-align:center">Country</th>
        <th style="text-align:center">0-1</th>
        <th style="text-align:center">1-3</th>
        <th style="text-align:center">3-6</th>
        <th style="text-align:center">MORE THAN 6</th>
        <th style="text-align:center">TOTAL</th>
        <th style="text-align:center">TRANSIT</th>
        <th style="text-align:center">NET</th>
      </tr>
    </thead>
    <tbody>
  `;

  countries.forEach(country => {
    const row = array[country];

    tableHtml += `
      <tr>
        <td style="text-align:center"><b>${country}</b></td>
        <td style="text-align:right">${Number(row.firstMonth).toFixed(2)}</td>
        <td style="text-align:right">${Number(row.secondMonth).toFixed(2)}</td>
        <td style="text-align:right">${Number(row.sixthMonth).toFixed(2)}</td>
        <td style="text-align:right">${Number(row.lastYear).toFixed(2)}</td>
        <td style="text-align:right"><b>${Number(row.currentStock).toFixed(2)}</b></td>
        <td style="text-align:right">
          <a href="#" class="transit-link" data-country="${country}" data-toggle="modal" data-target="#modal-transit-${country}">
            <b style="color:black">${Number(row.trans).toFixed(2)}</b>
          </a>
        </td>
        <td style="text-align:right"><b style="font-size:14px">${(Number(row.trans) + Number(row.currentStock)).toFixed(2)}</b></td>
      </tr>
    `;
  });

  tableHtml += `
      <tr style="background-color:black;color:white;text-align: center; font-size: 15px;">
        <td><b>Total In AED</b></td>
        <td style="text-align:right">${totals.firstMonthTotal.toFixed(2)}</td>
        <td style="text-align:right">${totals.secondMonthTotal.toFixed(2)}</td>
        <td style="text-align:right">${totals.sixthMonthTotal.toFixed(2)}</td>
        <td style="text-align:right">${totals.lastYearTotal.toFixed(2)}</td>
        <td style="text-align:right"><b>${totals.currentStockTotal.toFixed(2)}</b></td>
        <td style="text-align:right"><b>${totals.totalTransit.toFixed(2)}</b></td>
        <td style="text-align:right"><b style="font-size:16px">${totals.totalNet.toFixed(2)}</b></td>
      </tr>
    </tbody>
  </table>
  `;

  $('#summary-report-container').html(tableHtml);
}



});