$(document).ready(function () {

    // ✅ Initialize Select2 with AJAX for dynamic country list
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                processResults: data => ({ results: data })
            }
        });
    }

    // ✅ Apply Select2 on country dropdown
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select a Country');

    // ✅ Reset button clears form and result container
    $('#reset-button').on('click', function () {
        $('#country').val(null).trigger('change');
        $('#report-results-container, #modals-container').empty().hide();
    });

    // ✅ On form submit, fetch procurement delay report
    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();
        const formData = $(this).serialize();
        const countryId = $('#country').val();
       // if (!countryId) return alert('Please select a Country.');

        $('#report-results-container, #modals-container').hide();
        $('#loading-spinner').show();

        try {
            const response = await fetch(`/api/reports/procurement-delay?${formData}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data, formData); // ✅ Render report UI
                $('#report-results-container').show();
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide(); // ✅ Always hide loading spinner
        }
    });

    // ✅ Create Bootstrap modal dynamically for each buyer/order group
    function createModal(modalId, title, orderDetails, countryDetails) {
        // ✅ Country-specific CRM links
        const baseLink = '#';

        let modalRows = '';
        orderDetails.forEach((detail, index) => {
            modalRows += `<tr><td>${index + 1}</td><td>${detail.orderid}</td></tr>`;
        });

        if (orderDetails.length === 0) {
            modalRows = '<tr><td colspan="2" class="text-center">No orders in this category.</td></tr>';
        }

        return `
            <div class="modal fade" id="${modalId}" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title" style="background-color:black; color:white; padding: 10px; text-align: center;">${title}</h4>
                        </div>
                        <div class="modal-body">
                            <table class="table table-striped">
                                <thead style="background-color:powderblue;">
                                    <tr><th>SL No.</th><th>Order ID</th></tr>
                                </thead>
                                <tbody>${modalRows}</tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>`;
    }

    // ✅ Render main procurement delay report and modals
    function renderReport(data, formData) {
        const { reportData, countryDetails } = data;
        const countryName = countryDetails?.name || 'Report';
        const today = new Date().toLocaleDateString('en-GB');
        const exportUrl = `/api/reports/procurement-delay/export?${formData}`;

        let tableRows = '';
        let modalsHtml = '';
        let totals = { ztt: 0, ttf: 0, stt: 0, ta: 0, grand: 0 };

        // ✅ Build rows and modal HTML
        reportData.forEach(details => {
            const bTotal = details.zeroToTwo + details.threeToFive + details.sixToTen + details.tenAbove;
            totals.grand += bTotal;
            totals.ztt += details.zeroToTwo;
            totals.ttf += details.threeToFive;
            totals.stt += details.sixToTen;
            totals.ta += details.tenAbove;

            tableRows += `
                <tr>
                    <td style="font-weight: bold;">${details.buyer_name}</td>
                    <td><a href="#" class="modal-trigger" data-target="#modal_ztt_${details.buyer_id}">${details.zeroToTwo}</a></td>
                    <td><a href="#" class="modal-trigger" data-target="#modal_ttf_${details.buyer_id}">${details.threeToFive}</a></td>
                    <td><a href="#" class="modal-trigger" data-target="#modal_stt_${details.buyer_id}">${details.sixToTen}</a></td>
                    <td><a href="#" class="modal-trigger" data-target="#modal_ta_${details.buyer_id}">${details.tenAbove}</a></td>
                    <td style="font-weight: bold;">${bTotal}</td>
                </tr>`;

            // ✅ Generate modal for each delay category
            modalsHtml += createModal(`modal_ztt_${details.buyer_id}`, `Procurement Delay - 0-2 Days - ${details.buyer_name}`, details.zeroToTwo_oids, countryDetails);
            modalsHtml += createModal(`modal_ttf_${details.buyer_id}`, `Procurement Delay - 3-5 Days - ${details.buyer_name}`, details.threeToFive_oids, countryDetails);
            modalsHtml += createModal(`modal_stt_${details.buyer_id}`, `Procurement Delay - 6-10 Days - ${details.buyer_name}`, details.sixToTen_oids, countryDetails);
            modalsHtml += createModal(`modal_ta_${details.buyer_id}`, `Procurement Delay - More than 10 Days - ${details.buyer_name}`, details.tenAbove_oids, countryDetails);
        });

        // ✅ Grand total row with percentages
        const grandTotalRow = `
            <tr style="background-color:black;color:white;font-weight:bold;">
                <td>Grand Total</td>
                <td>${totals.ztt} - ${totals.grand > 0 ? Math.round((totals.ztt / totals.grand) * 100) : 0}%</td>
                <td>${totals.ttf} - ${totals.grand > 0 ? Math.round((totals.ttf / totals.grand) * 100) : 0}%</td>
                <td>${totals.stt} - ${totals.grand > 0 ? Math.round((totals.stt / totals.grand) * 100) : 0}%</td>
                <td>${totals.ta} - ${totals.grand > 0 ? Math.round((totals.ta / totals.grand) * 100) : 0}%</td>
                <td>${totals.grand}</td>
            </tr>`;

        // ✅ Final report markup with scroll container (add CSS for .table-container)
        const reportHtml = `
            <div class="box box-primary">
                <div class="box-body">
                    <div class="table-container" id="printable-area"> <!-- ✅ Add scroll container -->
                        <table class="table table-bordered report-table">
                            <thead>
                                <tr>
                                    <th colspan="6" style="text-align:center;background-color:black;color:white;">
                                        Procurement Delay - ${countryName} - ${today}
                                    </th>
                                </tr>
                                <tr style="background-color:powderblue;">
                                    <th>Buyer</th>
                                    <th>0-2 Days</th>
                                    <th>3-5 Days</th>
                                    <th>6-10 Days</th>
                                    <th>More than 10 Days</th>
                                    <th>Grand Total</th>
                                </tr>
                            </thead>
                            <tbody>${tableRows}${grandTotalRow}</tbody>
                        </table>
                    </div> <!-- end table-container -->
                </div>
               
            </div>`;

        // ✅ Insert generated HTML into DOM
        $('#report-results-container').html(reportHtml);
        $('#modals-container').html(modalsHtml).show();
    }

    // ✅ Modal trigger binding using data-target
    $(document).on('click', '.modal-trigger', function (e) {
        e.preventDefault();
        const modalId = $(this).data('target');
        $(modalId).modal('show');
    });

    // ✅ Print functionality for report table
    $(document).on('click', '#print-button', function (e) {
        const printContents = document.getElementById('printable-area').innerHTML;
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Print Report</title>');
        printWindow.document.write('<link rel="stylesheet" href="/bootstrap/css/bootstrap.min.css" type="text/css" />');
        printWindow.document.write('<style>body { padding: 20px; } table th, table td { border: 1px solid #ddd !important; }</style>');
        printWindow.document.write('</head><body>' + printContents + '</body></html>');
        printWindow.document.close();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    });

});
