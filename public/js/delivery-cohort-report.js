$(document).ready(function() {
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });
    $('#status, #orderFrom').select2({ placeholder: "Select One", allowClear: true });
    $('.select2-multi').select2({
        placeholder: "Select one or more",
        width: '100%'
    });

    $('#report-filters').on('submit', async function(e) {
        e.preventDefault();
        const fromDate = $('input[name="fromDate"]').val();
        if (!fromDate) {
            alert('Please select at least a "From Date" to generate the report.');
            return;
        }

        if ($('#country').val().length === 0) {
            alert('Please select at least one Country.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/delivery-cohort?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReportTable(result.data); 
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function renderReportTable(reportRows) {
        if (!reportRows || reportRows.length === 0) {
            $('#report-results-container').html('<div class="box box-primary"><div class="box-body text-center">No Records Found</div></div>');
            return;
        }
    
        const exportParams = $('#report-filters').serialize();
    
        let tableHtml = `
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Delivery Cohort Report</h3>
                    <div class="box-tools pull-right">
                        <a href="/api/reports/delivery-cohort/export?${exportParams}" class="btn btn-default btn-sm">
                            <i class="fa fa-file-excel-o"></i> Excel Download
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="table-container">
                        <table class="table table-bordered table-striped report-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Orders</th>
                                    <th>D0</th>
                                    <th>D1</th>
                                    <th>D2</th>
                                    <th>D3</th>
                                    <th>D4</th>
                                    <th>&lt; D5</th>
                                </tr>
                            </thead>
                            <tbody>`;
        
        reportRows.forEach(row => {
            tableHtml += `
                <tr>
                    <td>${row.Date}</td>
                    <td>${row.Orders}</td>
                    <td>${row['D0']}</td>
                    <td>${row['D1']}</td>
                    <td>${row['D2']}</td>
                    <td>${row['D3']}</td>
                    <td>${row['D4']}</td>
                    <td style="font-weight: bold;">${row['< D5']}</td>
                </tr>
            `;
        });
    
        tableHtml += '</tbody></table></div></div></div>';

        $('#report-results-container').html(tableHtml);
    }
});