$(document).ready(function() {

    $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d H:i" });

    // ✅ Initialize Select2 Dropdowns with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                data: dataCallback, // ✅ Dynamic parameters (if any)
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dropdowns for filters
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');
    initializeAjaxSelect2('#subcategory', '/api/lookups/subcategories', 'Select Category');
    initializeAjaxSelect2('#subsubcategory', '/api/lookups/subsubcategories', 'Select Category');
    initializeAjaxSelect2('#brand', '/api/lookups/brand', 'Select Brand');
    initializeAjaxSelect2('#agent', '/api/lookups/catelogueagent', 'Select Agent');

    // ✅ Product dropdown depends on selected country + category
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', function(params) {
        return {
            term: params.term,
            categoryId: $('#category').val(),
            country: $('#country').val()
        };
    });
    
    // ✅ Enable/disable product dropdown based on country selection
    $('#country').on('change', function() {
        const countryId = $(this).val();
        const $productSelect = $('#product');
        $productSelect.val(null).trigger('change');
        $productSelect.prop('disabled', !countryId);
    });

    // ✅ Clear product if category or country changes
    $('#category, #country').on('change', function() {
        $('#product').val(null).trigger('change');
    });

    // ✅ Reset all filters
    $('#reset-button').on('click', function() {
        $('.select2-ajax').val(null).trigger('change');
    });

    // ✅ Submit report request
    $('#stock-report-filters').on('submit', async function(e) {
        e.preventDefault();

        const country = $('#country').val();
        if (!country) {
            alert('Please select a Country to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/skuReports?${formData}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);          // ✅ Render report output
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report data.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();          // ✅ Hide loader
        }
    });



function renderReport1(data) {
    const { products, filters } = data;
    const today = new Date().toLocaleDateString('en-GB');
    const exportParams = $('#stock-report-filters').serialize();

    // --- Pagination settings ---
    const pageSize = 5000;
    let currentPage = 1;

    // --- Counts ---
    const totalCount = products.length;
    const activeCount = products.filter(p => p.status == 1).length;
    const inactiveCount = products.filter(p => p.status == 0).length;

    function paginate(array, pageSize, page) {
        return array.slice((page - 1) * pageSize, page * pageSize);
    }

    function renderTable(pageData) {
        let rowsHtml = "";
        if (pageData.length) {
            pageData.forEach(p => {
                rowsHtml += `
                    <tr class="${p.status == 1 ? 'success' : 'danger'}">
                        <td>${p.sr_no || ''}</td>
                        <td>${p.product_code || ''}</td>
                        <td>${p.product_name || ''}</td>
                        <td>${p.price || 0}</td>
                        <td>${p.cost || 0}</td>
                        <td>${p.category || ''}</td>
                        <td>${p.subcategory || ''}</td>
                        <td>${p.subsubcategory || ''}</td>
                        <td>${p.product_model || ''}</td>
                        <td>${p.brand || ''}</td>
                        <td>${p.buyer || ''}</td>
                        <td>${p.status == 1 ? 'Active' : 'Inactive'}</td>
                    </tr>`;
            });
        } else {
            rowsHtml = `<tr><td colspan="16" class="text-center">No Records Found</td></tr>`;
        }
        return rowsHtml;
    }

    function renderPaginationControls(totalItems, currentPage, pageSize) {
        const totalPages = Math.ceil(totalItems / pageSize);
        let controlsHtml = `<div class="pagination">`;

        if (currentPage > 1) {
            controlsHtml += `<button class="btn btn-sm btn-default page-btn" data-page="${currentPage - 1}">Prev</button>`;
        }

        for (let i = 1; i <= totalPages; i++) {
            controlsHtml += `<button class="btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-default'} page-btn" data-page="${i}">${i}</button>`;
        }

        if (currentPage < totalPages) {
            controlsHtml += `<button class="btn btn-sm btn-default page-btn" data-page="${currentPage + 1}">Next</button>`;
        }

        controlsHtml += `</div>`;
        return controlsHtml;
    }

    function renderReportPage(page) {
        currentPage = page;
        const pageData = paginate(products, pageSize, page);
        
        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6">
                        <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
                    </div>
                    <div class="col-xs-6 text-right">
                        <h5>Date: ${today}</h5>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-xs-4">
                        <div class="alert alert-info"><b>Total:</b> ${totalCount}</div>
                    </div>
                    <div class="col-xs-4">
                        <div class="alert alert-success"><b>Active:</b> ${activeCount}</div>
                    </div>
                    <div class="col-xs-4">
                        <div class="alert alert-danger"><b>Inactive:</b> ${inactiveCount}</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-12 text-center">
                        <h4>SKU REPORT</h4>
                    </div>
                </div><hr/>

                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead>
                            <tr>
                                <th>Sr. No.</th>
                                <th>SKU</th>
                                <th>Product Name</th>
                                <th>Price</th>
                                <th>Cost</th>
                                <th>Category</th>
                                <th>Sub Category</th>
                                <th>Sub Sub Category</th>
                                <th>Product Model</th>
                                <th>Brand</th>
                                <th>Buyer</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${renderTable(pageData)}
                        </tbody>
                    </table>
                </div>
                ${renderPaginationControls(products.length, currentPage, pageSize)}
                <hr/>
            </div>

            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/skuReports/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        $('#report-results-container').html(reportHtml);

        // Bind pagination
        $('.page-btn').on('click', function() {
            const page = parseInt($(this).data('page'));
            renderReportPage(page);
        });

        // Bind print handler
        $('#print-report-button').on('click', function(e) {
            e.preventDefault();
            printTableData();
        });
    }

    // Initial load
    renderReportPage(currentPage);
}


function renderReport(data) {
    const { products, summary, filters } = data; // summary from DB (optional)
    const today = new Date().toLocaleDateString('en-GB');
    const exportParams = $('#stock-report-filters').serialize();

    // --- Pagination settings ---
    const pageSize = 5000;
    let currentPage = 1;

    // --- Counts ---
    const totalCount = products.length;
    const activeCount = products.filter(p => p.status == 1).length;
    const inactiveCount = products.filter(p => p.status == 0).length;

    // --- Escape special characters ---
    function escapeHtml(text) {
        if (typeof text !== 'string') return text || '';
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // --- Pagination function ---
    function paginate(array, pageSize, page) {
        return array.slice((page - 1) * pageSize, page * pageSize);
    }

    // --- Table rows rendering ---
    function renderTable(pageData) {
        if (!pageData.length) return `<tr><td colspan="12" class="text-center">No Records Found</td></tr>`;

        return pageData.map(p => `
            <tr class="${p.status == 1 ? 'success' : 'danger'}">
                <td class="wrap-text">${escapeHtml(p.sr_no || '')}</td>
                <td class="wrap-text">${escapeHtml(p.product_code || '')}</td>
                <td class="wrap-text">${escapeHtml(p.product_name || '')}</td>
                <td>${p.price || 0}</td>
                <td>${p.cost || 0}</td>
                <td class="wrap-text">${escapeHtml(p.category || '')}</td>
                <td class="wrap-text">${escapeHtml(p.subcategory || '')}</td>
                <td class="wrap-text">${escapeHtml(p.subsubcategory || '')}</td>
                <td class="wrap-text">${escapeHtml(p.product_model || '')}</td>
                <td class="wrap-text">${escapeHtml(p.brand || '')}</td>
                <td class="wrap-text">${escapeHtml(p.buyer || '')}</td>
                <td>${p.status == 1 ? 'Active' : 'Inactive'}</td>
            </tr>
        `).join('');
    }

    // --- Pagination controls ---
    function renderPaginationControls(totalItems, currentPage, pageSize) {
        const totalPages = Math.ceil(totalItems / pageSize);
        let controlsHtml = `<div class="pagination text-center" style="margin:10px 0;">`;

        if (currentPage > 1) {
            controlsHtml += `<button class="btn btn-sm btn-default page-btn" data-page="${currentPage - 1}">Prev</button>`;
        }

        for (let i = 1; i <= totalPages; i++) {
            controlsHtml += `<button class="btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-default'} page-btn" data-page="${i}">${i}</button>`;
        }

        if (currentPage < totalPages) {
            controlsHtml += `<button class="btn btn-sm btn-default page-btn" data-page="${currentPage + 1}">Next</button>`;
        }

        controlsHtml += `</div>`;
        return controlsHtml;
    }

    // --- Render a single page of the report ---
    function renderReportPage(page) {
        currentPage = page;
        const pageData = paginate(products, pageSize, page);

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6">
                        <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
                    </div>
                    <div class="col-xs-6 text-right">
                        <h5>Date: ${today}</h5>
                    </div>
                </div>

                <!-- Summary row -->
                <div class="row text-center">
                    <div class="col-xs-4"><div class="alert alert-info"><b>Total:</b> ${totalCount}</div></div>
                    <div class="col-xs-4"><div class="alert alert-success"><b>Active:</b> ${activeCount}</div></div>
                    <div class="col-xs-4"><div class="alert alert-danger"><b>Inactive:</b> ${inactiveCount}</div></div>
                </div>

                <!-- Optional DB summary row -->
                ${summary ? `
                <div class="row text-center">
                    <div class="col-xs-4"><div class="alert alert-info"><b>Total (DB):</b> ${summary.total_skus}</div></div>
                    <div class="col-xs-4"><div class="alert alert-success"><b>Active (DB):</b> ${summary.active_skus}</div></div>
                    <div class="col-xs-4"><div class="alert alert-danger"><b>Inactive (DB):</b> ${summary.inactive_skus}</div></div>
                </div>` : ''}

                <div class="row">
                    <div class="col-xs-12 text-center"><h4>SKU REPORT</h4></div>
                </div><hr/>

                <div class="table-container" style="overflow-x:auto;">
                    <table class="table table-striped table-bordered report-table">
                        <thead>
                            <tr>
                                <th>Sr. No.</th>
                                <th>SKU</th>
                                <th>Product Name</th>
                                <th>Price</th>
                                <th>Cost</th>
                                <th>Category</th>
                                <th>Sub Category</th>
                                <th>Sub Sub Category</th>
                                <th>Product Model</th>
                                <th>Brand</th>
                                <th>Buyer</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${renderTable(pageData)}
                        </tbody>
                    </table>
                </div>

                ${renderPaginationControls(products.length, currentPage, pageSize)}
                <hr/>
            </div>

            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/skuReports/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        $('#report-results-container').html(reportHtml);

        // --- Bind pagination buttons ---
        $('.page-btn').on('click', function() {
            const page = parseInt($(this).data('page'));
            renderReportPage(page);
        });

        // --- Bind print ---
        $('#print-report-button').on('click', function(e) {
            e.preventDefault();
            printTableData();
        });
    }

    // Initial load
    renderReportPage(currentPage);
}






    // ✅ Print the rendered report content
    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Stock Report</title>');

        // ✅ Basic styling for print layout
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                .col-xs-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
                .col-xs-12 { flex: 0 0 100%; max-width: 100%; padding: 0 15px; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                img { max-width: 150px; }
            </style>
        `);

        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();   // ✅ Trigger print dialog
            printWindow.close();   // ✅ Close print window
        }, 250); // ⚠️ Slight delay to allow styles to load
    }
});
