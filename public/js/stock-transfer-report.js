let countriesPromise;
let countriesList = [];
let countryById = new Map();
  const currencyMap = {
    '1': 'AED', '2': 'OMR', '3': 'QAR',
    '5': 'KWD', '6': 'BHD', '7': 'SAR'
  };

function loadCountries() {
    countriesPromise = (async () => {
        const res = await fetch('/api/lookups/countries');
        if (!res.ok) throw new Error(`countries fetch failed: ${res.status}`);
        const data = await res.json();
        countriesList = Array.isArray(data) ? data : [];
        // Use string keys to align with Select2's string id guidance
    })();
    return countriesPromise;
}
const countryName = id => {
   return countriesList.find(d => d.id == id)?.text
};
$(document).ready(function () {
    loadCountries();
    // ✅ Initialize date picker
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Initialize AJAX-based Select2 dropdown
    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                processResults: data => ({ results: data })
            }
        });
    }

    // ✅ Basic select2 and AJAX select2 initialization
    $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    // initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select a Country');
    initializeAjaxSelect2('#transferFrom', '/api/lookups/countries', 'Transfer From');
    initializeAjaxSelect2('#transferTo', '/api/lookups/countries', 'Transfer To');
    initializeAjaxSelect2('#store', '/api/lookups/stores', 'Select a Store');

    // ✅ Handle report form submission
    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        const formData = $(this).serialize();
        // if (!$('#country').val()) return alert('Please select a Country.');

        $('#report-results-container').hide();
        $('#loading-spinner').show();

        try {
            // Ensure countries are ready before rendering
            if (!countriesPromise) loadCountries();
            await countriesPromise;

            const response = await fetch(`/api/reports/stock-transfer?${$(this).serialize()}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data, $(this).serialize());
                $('#report-results-container').show();
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    // ✅ Build and render report table
    function renderReport(data, formData) {
        const transferFrom = data?.[0]?.transfer_from
        const exportUrl = `/api/reports/stock-transfer/export?${formData}`;
        const today = new Date().toLocaleDateString('en-GB');
        let grandTotals = { qty: 0, price: 0, cost: 0, profit: 0 };
        let reportContent = '';

        // ✅ Loop through each category and render products
        data.forEach(bill => {
            const toId = bill.transfer_to ?? bill._transfer_to; // fallback if API uses underscore
            const fromName = countryName(bill.transfer_from);
            const currency = currencyMap[bill.transfer_from]
            const toName = countryName(toId);
            let transferMedia; 
            switch (bill.transfer_media) {
                case "1":
                    transferMedia = "Road"
                    break;
                case "2":
                    transferMedia = "Air"
                    break;
                case "3":
                    transferMedia = "Sea"
                    break;
                default:
                    break;
            }
            reportContent += `
            <tr style="background-color:#ddd;">
                <td colspan="7">
                From <b>${fromName}</b> → To <b>${toName}</b> VIA <b>${transferMedia}</b> |
                Bill No. <b>${bill.bill_no}</b> BY <b>${bill.logisticName}</b> 
                Billed on <b>${new Date(bill.bill_date).toLocaleDateString()}</b>
                </td>
            </tr>`;
            bill.products.forEach(p => {
                reportContent += `
                    <tr>
                        <td>${p.productName}</td>
                        <td>${p.sku}</td>
                        <td class="text-center">${p.qty}</td>
                        <td class="text-center">${p.costPerPc.toFixed(2)}</td>
                        <td class="text-center">${p.totalCost.toFixed(2)}</td>
                        <td class="text-center">${p.pricePerPc.toFixed(2)}</td>
                        <td class="text-center">${p.totalPrice.toFixed(2)}</td>
                    </tr>`;
            });

            reportContent += `
                <tr style="background-color:#eee;font-weight:bolder">
                    <td><b>Total</b></td>
                    <td></td>
                    <td class="text-center"><b>${bill.totals.qty}</b></td>
                    <td></td>
                    <td class="text-center"><b>${bill.totals.cost.toFixed(2)} ${currency}</b></td>
                    <td></td>
                    <td class="text-center"><b>${bill.totals.price.toFixed(2)} ${currency}</b></td>
                </tr>`;

            // ✅ Accumulate grand totals
            grandTotals.qty += bill.totals.qty;
            grandTotals.price += bill.totals.price;
            grandTotals.cost += bill.totals.cost;
            grandTotals.profit += bill.totals.profit;
        });

        // ✅ Calculate grand GP%
        const grandGP = grandTotals.price > 0 ? ((grandTotals.profit / grandTotals.price) * 100).toFixed(2) : '0.00';

        // ✅ Grand totals section
        const grandTotalSection = `
            <div class="row">
                <div class="col-xs-6"></div>
                <div class="col-xs-6">
                    <div class="table-responsive">
                        <table class="table">
                            <tr><th>Total Quantity</th><td>:</td><td>${grandTotals.qty}</td></tr>
                            <tr><th>Total Price</th><td>:</td><td>${grandTotals.price.toFixed(2)}</td></tr>
                            <tr><th>Total Cost</th><td>:</td><td>${grandTotals.cost.toFixed(2)}</td></tr>
                            <tr><th>Total Profit</th><td>:</td><td><b>${grandTotals.profit.toFixed(2)} ${currencyMap?.[transferFrom] || ""}</b></td></tr>
                            <tr><th>Total GP %</th><td>:</td><td><b>${grandGP}%</b></td></tr>
                        </table>
                    </div>
                </div>
            </div>`;

        // ✅ Final full HTML with print section and export/download
        const reportHtml = `
            <div class="box box-primary">
                <div class="box-body" id="printable-area">
                    <div class="row">
                        <div class="col-xs-6"><img src="/images/logo.png" style="height:40px;"></div>
                        <div class="col-xs-6 text-right"><h5 style="margin:0;">Date: ${today}</h5></div>
                    </div><hr/>
                    <div class="table-container">
                        <table class="table table-striped report-table">
                            <thead>
                                <tr>
                                    <th style="width:50%">Product Name</th>
                                    <th style="width:50%">SKU</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-center">Cost/Ps.</th>
                                    <th class="text-center">Total Cost</th>
                                    <th class="text-center">Selling Price/Ps.</th>
                                    <th class="text-center">Total SP</th>
                                </tr>
                            </thead>
                            <tbody>${reportContent}</tbody>
                        </table>
                    </div>
                    <hr/>${grandTotalSection}
                </div>

                <div class="box-footer no-print">
                    <button id="print-report-button" class="btn btn-default"><i class="fa fa-print"></i> Print</button>
                    <a href="${exportUrl}" class="btn btn-primary"><i class="fa fa-download"></i> Excel Download</a>
                </div>
            </div>`;

        $('#report-results-container').html(reportHtml);
    }

    // ✅ Print report handler
    function printReport() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) {
            alert('Cannot find report content to print.');
            return;
        }

        const reportHtml = printableArea.innerHTML;
        const printWindow = window.open('', '_blank');

        // ✅ Include minimal styles and Bootstrap
        printWindow.document.write('<html><head><title>Transfer Profit Report</title>');
        printWindow.document.write('<link rel="stylesheet" href="/bootstrap/css/bootstrap.min.css" type="text/css" />');
        printWindow.document.write(`
            <style>
                body { padding: 20px; }
                table { font-size: 11px; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                h4, h5 { margin: 5px 0; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(reportHtml);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();

        // ✅ Trigger print
        setTimeout(function () {
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    // ✅ Print button click binding
    $(document).on('click', '#print-report-button', function (e) {
        e.preventDefault();
        printReport();
    });
});
