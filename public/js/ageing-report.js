$(document).ready(function () {
    const currencyMap = {
        '1': 'AED', '2': 'OMR', '3': 'QAR',
        '5': 'KWD', '6': 'BHD', '7': 'SAR'
    };

     $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
    $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

    function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // Initialize Country dropdown (AJAX)
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', params => ({
        term: params.term,
    }));

    $('#report-filters').on('submit', async function (e) {
        e.preventDefault();

        const duration = $('input[name="duration"]').val();
      
        if (!duration) {
            alert('Please select Duration.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/ageingReport?${formData}`);
            if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Unknown error.');
            }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function printTableData() {
        const printableArea = document.getElementById('printable-order-report-area');
        if (!printableArea) return alert('No report found to print.');

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Report</title>');
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(printableArea.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

   function renderReport(data) {
  const { data: purchaseProducts = [], duration = 'N/A' ,filters } = data;
  const currencySymbol = currencyMap[filters.country] || 'AED';
  const today = new Date();

  let reportHtml = `
  <div class="box box-primary">
    <div class="box-body">
      <div id="printable-order-report-area">
        <div class="row">
          <div class="col-xs-6">
            <img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;">
          </div>
          <div class="col-xs-6 text-right">
            <h5>Date: ${today.toLocaleDateString('en-GB')}</h5>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12 text-center">
            <h4>AGEING REPORT - ${duration}</h4>
          </div>
        </div>
        <hr/>
        <div class="row">
          <div class="col-xs-12 table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Product</th>
                  <th>Supplier</th>
                  <th>Purchase Date</th>
                  <th>Order Date</th>
                  <th>Qty</th>
                  <th>Cost</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>`;

  let grandTotal = 0;
  let totalQuantity = 0;

  if (purchaseProducts.length) {
    purchaseProducts.forEach((product, i) => {
      const total = product.quantity * product.cost;
      grandTotal += total;
      totalQuantity += product.quantity;

      reportHtml += `
        <tr>
          <td style="width:40%">${product.product_name} <strong>[ ${product.product_code} ]</strong></td>
          <td><strong>${product.supplier_name || product.buyer_name || 'N/A'}</strong></td>
          <td>${product.order_date ? new Date(product.order_date).toLocaleDateString() : ''}</td>
          <td>
            ${product.latest_order_date || ''}<br/>
            ${
              product.latest_orderid
                ? `[<a><b>${product.latest_orderid}</b> - ${product.latest_qty || 0}</a>]`
                : ''
            }
          </td>
          <td>${product.quantity}</td>
          <td>${parseFloat(product.cost).toFixed(2)}</td>
          <td>${total.toFixed(2)}</td>
        </tr>`;
    });
  } else {
    reportHtml += `<tr><td colspan="7" class="text-center">Zero record found.</td></tr>`;
  }

  reportHtml += `
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-6">&nbsp;</div>
          <div class="col-xs-6">
            <div class="table-responsive" style="float:right;">
              <table class="table">
                <tr><th>Total Items</th><td>:</td><td>${purchaseProducts.length}</td></tr>
                <tr><th>Total Quantity</th><td>:</td><td>${totalQuantity}</td></tr>
                <tr><th>Total Amount</th><td>:</td><td>${currencySymbol} ${grandTotal.toFixed(2)}</td></tr>
              </table>
            </div>
          </div>
        </div>
      </div> <!-- End of printable area -->
    </div>
    <div class="box-footer">
      <div class="report-actions">
        <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
        <a href="/api/reports/ageingReport/export?${$('#report-filters').serialize()}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
      </div>
    </div>
  </div>`;

  $('#report-results-container').html(reportHtml);

  $('#print-report-button').on('click', e => {
    e.preventDefault();
    printTableData();
  });
}


});