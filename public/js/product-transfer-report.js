$(document).ready(function () {
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // Initialize Store dropdown
    $('#store').select2({
        placeholder: 'Select Store',
        allowClear: true,
        width: '100%',
        ajax: {
            url: '/api/lookups/stores', // Assuming you have this lookup endpoint
            dataType: 'json',
            delay: 250,
            processResults: data => ({ results: data }),
            cache: true
        }
    });

    $('#reset-button').on('click', function () {
        $('#product-transfer-filters').get(0).reset();
        $('.select2-ajax').val(null).trigger('change');
    });

    $('#product-transfer-filters').on('submit', async function (e) {
        e.preventDefault();
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }

        $('#report-results-container').hide();
        $('#loading-spinner').show();
        const formData = $(this).serialize();

        try {
            const response = await fetch(`/api/reports/product-transfer?${formData}`);
            const result = await response.json();
            if (result.success) {
                renderReport(result.data); // Pass the whole data object
                $('#report-results-container').show();
            } else { throw new Error(result.error || 'Failed to fetch report data.'); }
        } catch (err) {
            console.error('FETCH ERROR:', err);
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();
        }
    });

    function renderReport(data) {
        const { reportData, totals } = data; // Destructure here
        const today = new Date().toLocaleDateString('en-GB');
        const exportParams = $('#product-transfer-filters').serialize();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body" id="printable-area">
                <div class="row">
                    <div class="col-xs-6"><img src="${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
                    <div class="col-xs-6 text-right"><h5>Date: ${today}</h5></div>
                </div>
                <div class="row"><div class="col-xs-12 text-center"><h4>PRODUCT TRANSFER REPORT</h4></div></div><hr/>
                <div class="table-container">
                    <table class="table table-striped table-bordered report-table">
                        <thead>
                            <tr>
                                <th>No</th><th>Bill No</th><th>Send Date</th><th>SKU</th><th>Title</th><th>Qty</th>
                                <th>Price</th><th>VAT</th><th>Pur. Cost</th><th>Charge</th><th>D. Mode</th><th>Buyer</th>
                            </tr>
                        </thead>
                        <tbody>`;

        if (reportData && reportData.length) {
            reportData.forEach((row, index) => {
                reportHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${row.bill_no || ''}</td>
                        <td>${new Date(row.order_date).toLocaleString('en-GB', { timeZone: 'Asia/Dubai' })}</td>
                        <td>${row.product_code || ''}</td>
                        <td>${(row.product_name || '').substring(0, 40)}</td>
                        <td>${row.transfer_qty || 0}</td>
                        <td>${parseFloat(row.price_exclusive || 0).toFixed(2)}</td>
                        <td>${parseFloat(row.vat || 0).toFixed(2)}</td>
                        <td>${parseFloat(row.cost || 0).toFixed(2)}</td>
                        <td>${parseFloat(row.charge || 0).toFixed(2)}</td>
                        <td>${(() => {
                        switch (Number(row.transfer_media)) {
                            case 1: return 'Road';
                            case 2: return 'Air';
                            case 3: return 'Sea';
                            default: return '';
                        }
                    })()
                    }</td>
                        <td>${row.buyer_name || ''}</td>
                    </tr>`;
            });
            reportHtml += `<tr style="font-weight:bold;"><td colspan="5" class="text-right">Total Quantity:</td><td>${totals.total_qty}</td><td colspan="6"></td></tr>`;
        } else {
            reportHtml += `<tr><td colspan="12" class="text-center">No Records Found</td></tr>`;
        }

        reportHtml += `</tbody></table></div></div>
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/product-transfer/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;
        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => { e.preventDefault(); printTableData(); });
    }

    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return;
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Product Transfer Report</title><style>body{font-family:Arial,sans-serif;padding:20px}table{width:100%;border-collapse:collapse;margin-bottom:1rem;font-size:10px}th,td{padding:5px;border:1px solid #dee2e6;text-align:left}thead th{background-color:#f2f2f2}.text-center{text-align:center!important}hr{border-top:1px solid #dee2e6}</style></head><body>' + printableArea.innerHTML + '</body></html>');
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => { printWindow.print(); printWindow.close(); }, 250);
    }
});