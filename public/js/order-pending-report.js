$(document).ready(function() {
    // ✅ Initialize flatpickr date picker
    $('.date-picker').flatpickr({ dateFormat: "Y-m-d" });

    // ✅ Helper function to initialize Select2 dropdowns with AJAX
    function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
        $(elementId).select2({
            placeholder,
            allowClear: true,
            width: '100%',
            ajax: {
                url: apiUrl,
                dataType: 'json',
                delay: 250,
                data: dataCallback,
                processResults: data => ({ results: data }),
                cache: true
            }
        });
    }

    // ✅ Initialize dropdowns
    initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
    initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
    initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');
    initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', params => ({
        term: params.term,
        categoryId: $('#category').val(),
        country: $('#country').val()
    }));

    // ✅ Clear dependent dropdowns when country changes
    $('#country').on('change', () => {
        $('#buyer, #category, #product').val(null).trigger('change');
    });

    // ✅ Handle form submission for Order Pending Report
    $('#order-pending-filters').on('submit', async function(e) {
        e.preventDefault();

        // --- Validate country selection ---
       // if (!$('#country').val()) {
           // alert('Please select a Country to generate the report.');
           // return;
       // }

        // ⚠️ Validate required fields
        const fromDate = $('input[name="fromDate"]').val();
        const toDate = $('input[name="toDate"]').val();
        if (!fromDate || !toDate) {
            alert('Please select both a "From Date" and a "To Date" to generate the report.');
            return;
        }
        
        $('#report-results-container').hide();
        $('#loading-spinner').show();

        try {
            // --- Fetch data from API ---
            const response = await fetch(`/api/reports/order-pending?${$(this).serialize()}`);
            const result = await response.json();

            if (result.success) {
                renderReport(result.data);             // ✅ Render table on success
                $('#report-results-container').show();
            } else {
                throw new Error(result.error || 'Failed to fetch report.');
            }
        } catch (err) {
            alert(`An error occurred: ${err.message}`);
        } finally {
            $('#loading-spinner').hide();              // ✅ Always hide loader
        }
    });

    // ✅ Print function for Order Pending Report
    function printTableData() {
        const printableArea = document.getElementById('printable-area');
        if (!printableArea) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Order Pending Report</title>');
        printWindow.document.write('<style>body{font-family:Arial,sans-serif;padding:20px}table{width:100%;border-collapse:collapse;font-size:10px}th,td{padding:5px;border:1px solid #ccc;text-align:left}thead th{background-color:#f2f2f2}tbody tr:nth-of-type(odd){background-color:#f9f9f9}</style>');
        printWindow.document.write('</head><body>' + printableArea.innerHTML + '</body></html>');
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }

    // ✅ Render Order Pending Report Table
    function renderReport(data) {
        const exportParams = $('#order-pending-filters').serialize();

        let reportHtml = `
        <div class="box box-primary">
            <div class="box-body">
                <div id="printable-area">
                    <div class="row">
                        <div class="col-xs-6">
                            <img src="/images/logo.png" style="height:40px;">
                        </div>
                        <div class="col-xs-6 text-right">
                            <h5>Date: ${new Date().toLocaleDateString('en-GB')}</h5>
                        </div>
                    </div>
                    <hr/>
                    <div class="table-container">
                        <table class="table table-bordered table-striped report-table">
                            <thead>
                                <tr>
                                    <th>Sl No</th>
                                    <th>Country</th>
                                    <th>Order Date</th>
                                    <th>Order Id</th>
                                    <th>Category</th>
                                    <th>Buyer</th>
                                    <th>SKU</th>
                                    <th>Product</th>
                                    <th>Order Qty</th>
                                    <th>In Transit</th>
                                    <th>Stock Qty</th>
                                    <th>Selling Price/Pc</th>
                                    <th>Cost</th>
                                </tr>
                            </thead>
                            <tbody>`;

        // ✅ Loop through data and render rows
        if (data.length) {
            let slNo = 0;
            data.forEach(row => {
                slNo += 1;
                reportHtml += `<tr>
                    <td>${slNo || '0'}</td>
                     <td>${row.country_name || '-'}</td>
                    <td>${row.order_date || '-'}</td>
                    <td>${row.order_ref_code || '-'}</td>
                    <td>${row.category_name || '-'}</td>
                    <td>${row.buyer_name || '-'}</td>
                    <td>${row.sku || ''}</td>
                    <td>${row.product_name || ''}</td>
                    <td>${row.order_qty}</td>
                    <td>${row.trans_qty || 0}</td>
                    <td>${row.stock_qty}</td>
                    <td>${parseFloat(row.selling_price || 0).toFixed(2)}</td>
                    <td>${parseFloat(row.cost || 0).toFixed(2)}</td>
                </tr>`;
            });
        } else {
            // ✅ No records fallback
            reportHtml += `<tr><td colspan="11" class="text-center">No Pending Purchase Records Found</td></tr>`;
        }

        // ✅ Close table and render footer
        reportHtml += `</tbody></table></div></div></div>
            <div class="box-footer">
                <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
                <a href="/api/reports/order-pending/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
        </div>`;

        // ✅ Inject report into DOM and bind print button
        $('#report-results-container').html(reportHtml);
        $('#print-report-button').on('click', e => {
            e.preventDefault();
            printTableData();
        });
    }
});
