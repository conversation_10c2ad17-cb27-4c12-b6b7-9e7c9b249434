$(document).ready(function () {
  const currencyMap = {
    '1': 'AED', '2': 'OMR', '3': 'QAR',
    '5': 'KWD', '6': 'BHD', '7': 'SAR'
  };

  $('.date-picker').flatpickr({ enableTime: true, dateFormat: "Y-m-d H:i" });
  $('.select2').select2({ placeholder: "Select One", allowClear: true, width: '100%' });
  $('.select2-multi').select2({ placeholder: "Select One or More", width: '100%' });

  function initializeAjaxSelect2(elementId, apiUrl, placeholder) {
    $(elementId).select2({
      placeholder,
      allowClear: true,
      width: '100%',
      ajax: {
        url: apiUrl,
        dataType: 'json',
        delay: 250,
        processResults: data => ({ results: data }),
        cache: true
      }
    });
  }


  // ✅ Initialize Select2 Dropdowns with AJAX
  function initializeAjaxSelect2(elementId, apiUrl, placeholder, dataCallback) {
    $(elementId).select2({
      placeholder,
      allowClear: true,
      width: '100%',
      ajax: {
        url: apiUrl,
        dataType: 'json',
        delay: 250,
        data: dataCallback, // ✅ Dynamic parameters (if any)
        processResults: data => ({ results: data }),
        cache: true
      }
    });
  }

  // ✅ Initialize dropdowns for filters
  initializeAjaxSelect2('#country', '/api/lookups/countries', 'Select Country');
  initializeAjaxSelect2('#buyer', '/api/lookups/buyers', 'Select Buyer');
  initializeAjaxSelect2('#category', '/api/lookups/categories', 'Select Category');
  initializeAjaxSelect2('#orderFrom', '/api/lookups/order-from', 'Select Order From');

  // ✅ Product dropdown depends on selected country + category
  initializeAjaxSelect2('#product', '/api/lookups/products', 'Search for a product...', function (params) {
    return {
      term: params.term,
      categoryId: $('#category').val(),
      country: $('#country').val()
    };
  });

  // ✅ Clear product if category or country changes
  $('#category, #country').on('change', function () {
    $('#product').val(null).trigger('change');
  });

  // ✅ Reset all filters
  $('#reset-button').on('click', function () {
    $('.select2-ajax').val(null).trigger('change');
  });

  $('#report-filters').on('submit', async function (e) {
    e.preventDefault();

    const fromDate = $('input[name="fromDate"]').val();
    //const toDate = $('input[name="toDate"]').val();
    if (!fromDate) {
      alert('Please select "From Date"')
      return;
    }

    const based_on = $('select[name="based_on"]').val();
    if (!based_on) {
      alert('Please Select Based On');
      return;
    }

    $('#report-results-container').hide();
    $('#loading-spinner').show();
    const formData = $(this).serialize();

    try {
      const response = await fetch(`/api/reports/profitReport?${formData}`);
      if (!response.ok) throw new Error((await response.json()).error || `Status: ${response.status}`);
      const result = await response.json();

      if (result.success) {
        renderReport(result.data);
        $('#report-results-container').show();
      } else {
        throw new Error(result.error || 'Unknown error.');
      }
    } catch (err) {
      console.error('FETCH ERROR:', err);
      alert(`An error occurred: ${err.message}`);
    } finally {
      $('#loading-spinner').hide();
    }
  });

  function printTableData() {
    const printableArea = document.getElementById('printable-order-report-area');
    if (!printableArea) return alert('No report found to print.');

    const printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>Order Report</title>');
    printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
                th, td { padding: 8px; border: 1px solid #dee2e6; text-align: left; }
                thead th { background-color: #f2f2f2; }
                tbody tr:nth-of-type(odd) { background-color: #f9f9f9; } /* This is the highlight */
                .row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px; }
                .col-xs-6 { -webkit-box-flex: 0; -ms-flex: 0 0 50%; flex: 0 0 50%; max-width: 50%; position: relative; width: 100%; padding-right: 15px; padding-left: 15px; }
                .col-xs-12 { -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
                .text-right { text-align: right !important; }
                .text-center { text-align: center !important; }
                hr { border-top: 1px solid #dee2e6; }
                .summary-table th, .summary-table td { border: none !important; padding: 4px 8px; }
                img { max-width: 150px; }
            </style>
        `);
    printWindow.document.write('</head><body>');
    printWindow.document.write(printableArea.innerHTML);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  }


  function renderReport(data) {
    const {
      categoryData = [],
      orderSummary = {},
      filters = {},
      grandTotals = {}
    } = data;

    const currencySymbol = currencyMap[filters.country] || 'AED';
    const today = new Date();
    const formattedDate = today.toLocaleDateString('en-GB');

    const formatNum = (val) => {
      const num = Number(val);
      return isNaN(num) ? '0.00' : num.toFixed(2);
    };
    const num = (v) => Number(v) || 0;

    // Use API totals directly in the summary (no client-side recomputation)
    const totalOrders = num(orderSummary.totalorder);
    const totalQtySum = num(orderSummary.totalquantity);
    const subtotal = num(orderSummary.subtotal);          // already includes VAT per backend
    const discount = num(orderSummary.discount);
    const shipping = num(orderSummary.shippingcharge);
    const pfee = num(orderSummary.pfee);
    const donation = num(orderSummary.donation);
    const totvat = num(orderSummary.totvat);
    const grandTotal = num(orderSummary.total);           // backend-computed grand total
    const grandCost = num(grandTotals?.totalCost);

    // Display-only values (shown exactly like the reference version)
    const netSubAfterDiscount = grandTotal;               // kept to mirror the simplified reference logic
    const profit = grandTotals.totalProfit;               // item-level profit from backend
    const profitPercent = grandTotals.totalProfitPercent; // item-level GP% from backend

    const exportParams = new URLSearchParams(filters).toString();

    let html = `
    <div id="printable-order-report-area">
      <div class="row">
        <div class="col-xs-6"><img src="\${window.BASE_URL || ''}/images/logo.png" style="height:40px;"></div>
        <div class="col-xs-6 text-right"><h5>Date: ${formattedDate}</h5></div>
      </div>
      <div class="row"><div class="col-xs-12 text-center"><h4>PROFIT REPORT FROM ${filters.fromDate || ''} TO ${filters.toDate || ''}</h4></div></div><hr/>

      <div class="table-responsive">
        <section class="invoice">
          <div class="row">
            <div class="col-xs-12 table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr style="font-weight:bold;">
                    <td style="width:50%">Product</td>
                    <td align="center">Qty</td>
                    <td align="right" width="10%">Cost</td>
                    <td align="right" width="10%">Selling Price</td>
                    <td align="right" width="10%">Profit</td>
                    <td align="right" width="10%">GP%</td>
                  </tr>
                </thead>
                <tbody>`;

    if (categoryData.length === 0) {
      html += `<tr><td colspan="6" class="text-center">No Records Found</td></tr>`;
    } else {
      categoryData.forEach(({ catname, products = [], totals = {} }) => {
        const {
          totalQuantity = 0,
          totalSales = 0,
          totalCost = 0,
          totalProfit = 0,
          totalProfitPercent = 0,
        } = totals;

        html += `
        <tr style="background-color:#ddd;font-weight:bolder">
          <td colspan="6">${catname}</td>
        </tr>`;

        if (products.length === 0) {
          html += `<tr><td colspan="6" class="text-center">No products found in this category</td></tr>`;
        } else {
          products.forEach(p => {
            const profitStyle = p.profit < 0
              ? 'background-color:rgba(255,0,0,0.2);'
              : p.profit === 0
                ? 'background-color:rgba(255,255,0,0.5);'
                : '';

            html += `
            <tr style="${profitStyle}">
              <td>${p.productName}</td>
              <td align="center">${p.quantity}</td>
              <td align="right">${formatNum(p.totalCost)}</td>
              <td align="right">${formatNum(p.totalPrice)}</td>
              <td align="right">${formatNum(p.profit)}</td>
              <td align="right">${formatNum(p.profitPercent)}%</td>
            </tr>`;
          });
        }

        html += `
        <tr style="background-color:#eee;font-weight:bolder">
          <td><b>Total</b></td>
          <td align="center"><b>${totalQuantity}</b></td>
          <td align="right"><b>${formatNum(totalCost)}</b></td>
          <td align="right"><b>${formatNum(totalSales)}</b></td>
          <td align="right"><b>${formatNum(totalProfit)}</b></td>
          <td align="right">${formatNum(totalProfitPercent)}%</td>
        </tr>`;
      });
    }

    html += `
                </tbody>
              </table>
            </div>
          </div>

          <!-- Profit Summary Section (uses API values) -->
          <div class="row">
            <div class="col-xs-6">&nbsp;</div>
            <div class="col-xs-6">
              <div class="table" style="float:right;">
                <table class="table table-sm table-bordered" style="border-width: 2px; font-size: 13px;">
                  <tr><th>Total Orders</th><td>:</td><td style="text-align:right;">${formatNum(totalOrders)}</td></tr>
                  <tr><th>Total Quantity</th><td>:</td><td style="text-align:right;">${formatNum(totalQtySum)}</td></tr>

                  <tr><th>Subtotal (incl. VAT)</th><td>:</td><td style="text-align:right;">${formatNum(subtotal)}</td></tr>
                  <tr><th>Shipping Charge (incl. VAT)</th><td>:</td><td style="text-align:right;">${formatNum(shipping)}</td></tr>
                  <tr><th>Processing Fee (incl. VAT)</th><td>:</td><td style="text-align:right;">${formatNum(pfee)}</td></tr>
                  <tr><th>VAT</th><td>:</td><td style="text-align:right;">${formatNum(totvat)}</td></tr>
                  <tr><th>Discount</th><td>:</td><td style="text-align:right;">-${formatNum(discount)}</td></tr>
                  <tr><th>Donation</th><td>:</td><td style="text-align:right;">${formatNum(donation)}</td></tr>
                  <tr><th><strong>Grand Total</strong></th><td>:</td><td style="text-align:right;"><strong>${formatNum(grandTotal)} ${currencySymbol}</strong></td></tr>

                  <tr><th>Total Cost</th><td>:</td><td style="text-align:right;">${formatNum(grandCost)} ${currencySymbol}</td></tr>
                  <tr><th>Total Profit</th><td>:</td><td style="text-align:right;">${formatNum(profit)} ${currencySymbol} [ ${formatNum(profitPercent)}% ]</td></tr>
                </table>
              </div>
            </div>
          </div>

          <div class="box-footer">
            <div class="report-actions">
              <a id="print-report-button" href="#" class="btn btn-default"><i class="fa fa-print"></i> Print</a>
              <a href="/api/reports/profitReport/export?${exportParams}" class="btn btn-default"><i class="fa fa-file-excel-o"></i> Excel Download</a>
            </div>
          </div>
        </section>
      </div>
    </div>
  `;

    document.getElementById('report-results-container').innerHTML = html;

    $('#print-report-button').on('click', e => {
      e.preventDefault();
      printTableData();
    });
  }


});