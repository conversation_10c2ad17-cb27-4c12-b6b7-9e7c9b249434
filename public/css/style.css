/* =============================================== */
/* ✅ Typography & Global Fonts */
/* =============================================== */
body, h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6,
.main-header .logo .logo-lg, .sidebar-menu>li>a, .user-panel>.info>p,
.content-header>h1, .box-title, .breadcrumb>li>a, .form-control, .btn {
    font-family: 'Open Sans', sans-serif;
}

/* =============================================== */
/* ✅ Table Styling */
/* =============================================== */
.table>thead>tr>th {
    background-color: #f4f4f4;
    font-weight: 600;
}

/* =============================================== */
/* ✅ Report Action Links */
/* =============================================== */
.report-actions a {
    color: #333;
    margin-left: 15px;
    font-size: 14px;
}
.report-actions a:hover {
    color: #0073b7;
    text-decoration: none;
}

/* =============================================== */
/* ✅ Form Label Styling */
/* =============================================== */
.form-group label {
    font-weight: 700 !important;
}

/* =============================================== */
/* ✅ Box Styling */
/* =============================================== */
.box {
    border-top: 3px solid #3c8dbc !important;
}

/* =============================================== */
/* ✅ Custom AdminLTE Theme Edits */
/* =============================================== */
.skin-blue .main-header .logo {
    background-color: #ffffff;
    border-bottom: 1px solid #f4f4f4;
}
.skin-blue .main-header .logo:hover {
    background-color: #ffffff;
}

/* =============================================== */
/* ✅ Avatar Image Size Fix */
/* =============================================== */
.img-circle {
    width: 40px !important;
    height: 35px !important;
}

/* =============================================== */
/* ✅ Table Scroll Container */
/* =============================================== */
/* .table-container {
    position: relative;
    width: 100%;
    overflow-x: auto;
    border: 1px solid #ddd;
} */

.table-container {
    max-height: 500px;     /* You can adjust height as needed */
    overflow-y: auto;      /* Adds vertical scroll */
    overflow-x: auto;      /* Adds horizontal scroll */
    border: 1px solid #ccc;
}

.report-table {
    width: 100%;
    min-width: 1000px;     /* Ensure it scrolls horizontally when content is wide */
    border-collapse: collapse;
}

/* =============================================== */
/* ✅ Table Cell Behavior */
/* =============================================== */
.report-table {
    border-collapse: collapse;
}
.report-table th,
.report-table td {
    white-space: nowrap; /* Prevents wrapping */
    vertical-align: middle !important;
}

.table-bordered>thead>tr>th, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>tbody>tr>td, .table-bordered>tfoot>tr>td {
    border: 1px solid #898484 !important;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
    border-top: 1px solid #898484 !important;
}

/* =============================================== */
/* ✅ Sticky Column Styles */
/* =============================================== */
.sticky-col {
    position: -webkit-sticky;
    position: sticky;
    background-color: #f9f9f9; /* Covers text during scroll */
    z-index: 2;
}
.sticky-header {
    top: 0;
    z-index: 3;
    background-color: #f4f4f4;
}

/* Sticky Column Positioning */
.col-1 { left: 0px; }
.col-2 { left: 45px; }
.col-3 { left: 180px; }
.col-4 { left: 580px; }

/* =============================================== */
/* ✅ Special Column: Product Title */
/* =============================================== */
.col-title {
    white-space: normal !important; /* Allow line wrap */
    min-width: 400px;
    max-width: 400px;
}

/* =============================================== */
/* ✅ Country-Based Color Highlights */
/* =============================================== */
.uae-clr     { background-color: #ffeded !important; }
.oman-clr    { background-color: #e8f4e8 !important; }
.qatar-clr   { background-color: #e6f7ff !important; }
.kuwait-clr  { background-color: #fff2e8 !important; }
.bahrain-clr { background-color: #feefea !important; }
.saudi-clr   { background-color: #f6ffed !important; }

/* =============================================== */
/* ✅ Quantity Color Highlights */
/* =============================================== */
.low-qty {
    background-color: #f56954 !important;
    color: white !important;
    font-weight: 600;
}
.avg-qty {
    background-color: #f39c12 !important;
    color: white !important;
    font-weight: 600;
}

.highlight-active {
    background-color: #d4fcdf !important; /* A light green color */
}

.highlight-inactive {
    background-color: #fcd4d4 !important; /* A light green color */
}

.select2-selection__choice{
    background-color: #3c8dbc !important;
    border-color: #367fa9 !important;
    padding: 1px 10px !important;
    color: #fff;
}


    .thin-bordered {
        border-collapse: collapse;
        width: 100%;
    }

    .thin-bordered th,
    .thin-bordered td {
        border: 1px solid #000;   /* thin black border */
        padding: 6px;
        text-align: center;
    }

    .thin-bordered th {
        background: #f2f2f2; /* optional */
        font-weight: bold;
    }

.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: auto !important;
    user-select: none;
    -webkit-user-select: none;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 0;
    padding-right: 0;
    height: auto;
    margin-top: 0px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 1px;
    width: 20px;
}

.select2-container--default .select2-selection--single, .select2-selection .select2-selection--single {
    padding: 6px 20px 6px 12px;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    background-color: #ffffff;
    opacity: 1;
    height: 35px;
    border-radius: 5px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: normal !important;
}

