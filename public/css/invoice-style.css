/*
	 CSS-Tricks Example
	 by <PERSON>
	 http://css-tricks.com
*/
html, body {
    height: 100%;
    font-family: 'Open Sans';font-size: 13px;
}
* { margin: 0; padding: 0; }
body {  font-family: 'Open Sans';font-size: 13px; }
#page-wrap { width: 800px; margin: 0 auto;  }

textarea { border: 0; font-family: 'Open Sans';  overflow: hidden; resize: none;  }
table { border-collapse: collapse; }
table td, table th { border: 1px solid black; padding: 5px; }

#header { height: 15px; width: 100%; margin: 20px 0;  text-align: center; color: #fff; font: bold 14px 'Open Sans', Sans-Serif; text-decoration: uppercase; letter-spacing: 20px; padding: 8px 0px; }

#address { width: 250px; height: 100px; float: left; }
#customer { overflow: hidden; }

#logo { text-align: right; float: right; position: relative; margin-top: 25px; border: 1px solid #fff; max-width: 540px; max-height: 100px; overflow: hidden; }
#logo:hover, #logo.edit { border: 1px solid #000; margin-top: 0px; max-height: 125px; }
#logoctr { display: none; }
#logo:hover #logoctr, #logo.edit #logoctr { display: block; text-align: right; line-height: 25px; background: #eee; padding: 0 5px; }
#logohelp { text-align: left; display: none; font-style: italic; padding: 10px 5px;}
#logohelp input { margin-bottom: 5px; }
.edit #logohelp { display: block; }
.edit #save-logo, .edit #cancel-logo { display: inline; }
.edit #image, #save-logo, #cancel-logo, .edit #change-logo, .edit #delete-logo { display: none; }
#customer-title { font-size: 20px; font-weight: bold; float: left; }

#meta { margin-top: -20px; width: 300px; float: right; }
#meta td { text-align: right;  }
#meta td.meta-head { text-align: left; background: #eee; color:#333; }
#meta td textarea { width: 100%; height: 20px; text-align: right; }

#items { clear: both; width: 100%; margin: 30px 0 0 0; border: 1px solid black; }
#items th { background: #eee; color:#333; }
#items textarea { width: 80px; height: 50px; text-align:center; }
#items tr.item-row td { border: 0; vertical-align: top; }
#items td.description { width: 300px; }
#items th.item-name { width: 300px;  }
#items td.description textarea, #items td.item-name textarea { width: 100%; }
#items td.total-line { border-right: 0; text-align: right; }
#items td.total-value { border-left: 0; padding: 10px; }
#items td.total-value textarea { height: 20px; background: none; }
#items td.balance { background: #eee; color:#333;}
#items td.blank { border: 0; }

#terms1 { text-align: center; margin: 0;  }
#terms1 h5 { text-transform: uppercase; font: 14px  letter-spacing: 10px; border-bottom: 1px solid black; padding: 0 0 8px 0; margin: 0 0 8px 0; }
#terms1 textarea { width: 100%; text-align: center;}
#items1 tr.item-row  { border: 0; vertical-align: top; }

#items1 tr.item-row td { border: 0; vertical-align: top; }
#items1 tr.item-row1 td { border: 0; vertical-align: top; }
#items1 td.description { width: 300px; }
#items1 td.item-name { width: 425px; float:left; }
#items1 td.item-price { width: 75px; float:left; }
#items1 td.item-qty { width: 100px; float:left; }
#items1 td.item-total { width: 100px; float:left; }
#items1 td.description textarea, #items td.item-name textarea { width: 100%; }
#items1 td.total-line { border-right: 0; text-align: right; }
#items1 td.total-value { border-left: 0; padding: 10px; }
#items1 td.total-value textarea { height: 20px; background: none; }
#items1 td.balance { background: #eee; color:#333;}
#items1 td.blank { border: 0; }

#terms { text-align: center; margin: 20px 0 0 0; }
#terms h5 { text-transform: uppercase; font: 14px  letter-spacing: 10px; border-bottom: 1px solid black; padding: 0 0 8px 0; margin: 0 0 8px 0; }
#terms textarea { width: 100%; text-align: center;}

textarea:hover, textarea:focus, #items td.total-value textarea:hover, #items td.total-value textarea:focus, .delete:hover { background-color:#EEFF88; }

.delete-wpr { position: relative; }
.delete { display: block; color: #333; text-decoration: none; position: absolute; background: #eee;  font-weight: bold; padding: 0px 3px; border: 1px solid; top: 0px; left: -21.5px; font-family: Open Sans; font-size: 14px; border:1px solid #333;}