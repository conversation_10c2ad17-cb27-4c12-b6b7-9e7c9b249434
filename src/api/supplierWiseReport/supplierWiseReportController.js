import SupplierWiseReport from '../supplierWiseReport/supplierWiseReportModel.js';
import exceljs from 'exceljs';

export const renderSupplierWiseListPage = (req, res) => {
    res.render('reports/supplier-wise-list', {
        user: req.user,
        pageTitle: "Supplier Product Download"
    });
};

export const handleSupplierWiseListExcel = async (req, res) => {
    try {
        // Get supplier IDs from the request body or query string
        const supplierIds = req.body.supplier || req.query.supplier;
        if (!supplierIds) {
            return res.status(400).send("No suppliers selected.");
        }

        const reportData = await SupplierWiseReport.getReport(Array.isArray(supplierIds) ? supplierIds : [supplierIds]);
        
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Supplier Products');

        worksheet.columns = [
            { header: 'Product Name', key: 'product_name', width: 50 },
            { header: 'SKU', key: 'sku', width: 20 },
            { header: 'Category', key: 'category_name', width: 25 },
            { header: 'Model number', key: 'product_model', width: 20 },
            { header: 'Cost', key: 'cost', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Selling Price', key: 'special_price', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Status', key: 'status', width: 15 },
        ];

        reportData.forEach(supplier => {
            // Add a merged row for the supplier header
            const headerRow = worksheet.addRow([`Supplier Name - ${supplier.company_name} - ${supplier.contact_number1}`]);
            worksheet.mergeCells(`A${headerRow.number}:G${headerRow.number}`);
            headerRow.getCell('A').font = { bold: true, size: 14 };
            headerRow.getCell('A').alignment = { horizontal: 'center' };
            
            // Add column titles for this supplier's section
            worksheet.addRow(worksheet.columns.map(c => c.header)).font = { bold: true };

            // Add the product data for this supplier
            worksheet.addRows(supplier.products);

            // Add a spacer row between suppliers
            worksheet.addRow([]);
        });

        const date = new Date().toISOString().slice(0, 10);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="uae_supplier_products${date}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};