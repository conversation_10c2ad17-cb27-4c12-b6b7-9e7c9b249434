import db from '../../config/db.js';

class SupplierWiseReport {
   
    static async getReport(supplierIds) {
        if (!supplierIds || supplierIds.length === 0) {
            return [];
        }

        const sql = `
            SELECT 
                ads.supplierid as supplier_id,
                cp.rstatus as product_status,
                ads.primary_contact as contact_number1,
                cp.title as product_name,
                cp.sku,
                cp.model_no as product_model,
				c3.category_name AS category_name,
                cpi.cost,
                cpi.mrp,
                cpi.selling_price as special_price,
                ads.company_name
            FROM catalog_product AS cp
            JOIN catalog_product_inventory AS cpi ON cp.productid = cpi.productid
            JOIN admin_supplier AS ads ON cp.supplierid = ads.supplierid
            LEFT JOIN catalog_category AS c1 
				ON cp.categoryid = c1.categoryid AND c1.category_level = 3
			LEFT JOIN catalog_category AS c2 
				ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
				AND c2.category_level = 2
			LEFT JOIN catalog_category AS c3 
				ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
				AND c3.category_level = 1
            WHERE cp.supplierid IN (?)
            ORDER BY ads.company_name, cp.title
        `;
        
        const [results] = await db.query(sql, [supplierIds]);

        // Group the results by supplier for easy processing in the controller
        const reportData = results.reduce((acc, row) => {
            if (!acc[row.supplier_id]) {
                acc[row.supplier_id] = {
                    supplier_id: row.supplier_id,
                    company_name: row.company_name,
                    contact_number1: row.contact_number1,
                    products: []
                };
            }
            acc[row.supplier_id].products.push({
                product_name: row.product_name,
                sku: row.sku,
                category_name: row.category_name,
                product_model: row.product_model,
                cost: row.cost,
                special_price: row.special_price,
                status: row.product_status === 1 ? 'Active' : 'Inactive'
            });
            return acc;
        }, {});

        // Return an array of supplier objects
        return Object.values(reportData);
    }
}

export default SupplierWiseReport;