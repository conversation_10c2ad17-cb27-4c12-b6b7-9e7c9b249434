// src/routes/reportRoutes.js
import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderSupplierWiseListPage,
    handleSupplierWiseListExcel
} from '../supplierWiseReport/supplierWiseReportController.js';

const router = express.Router();

router.get('/supplier-wise-list', verifyToken, (req, res) => {
    res.render('supplier-wise-list', {
        pageTitle: "SUPPLIER WISE PRODUCT LIST REPORT",
        user: req.user
    });
});

// ===============================================
// SUPPLIER WISE PRODUCT LIST ROUTES
// ===============================================
router.get('/reports/supplier-wise-list', verifyToken, renderSupplierWiseListPage);
router.post('/api/reports/supplier-wise-list/export', verifyToken, handleSupplierWiseListExcel);

export default router;
