import express from 'express'; // Import express
import { verifyToken } from '../../middleware/authMiddleware.js'; // Auth middleware
import {
    renderOrderReportPage,     // Controller to render EJS view
    handleOrderReportApi,      // Controller to handle JSON API
    handleOrderReportExcel     // Controller to export Excel report
} from '../orderReport/orderReportController.js';

const router = express.Router(); // Create router instance

// ==============================
// Protected Routes (with Token)
// ==============================

// Dashboard route
router.get('/dashboard', verifyToken, (req, res) => {
    res.render('dashboard', {
        pageTitle: "Dashboard",
        user: req.user // Pass logged-in user info to template
    });
});

// Order report EJS route (can be removed, since handled below)
router.get('/order-report', verifyToken, (req, res) => {
    res.render('order-report', {
        pageTitle: "ORDER REPORT",
        user: req.user
    });
});

// ==============================
// Order Report Routes
// ==============================

// Render order report page with filters/user info
router.get('/reports/order', verifyToken, renderOrderReportPage);

// Get order report data as JSON (API call)
router.get('/api/reports/order', verifyToken, handleOrderReportApi);

// Export order report data as Excel file
router.get('/api/reports/order/export', verifyToken, handleOrderReportExcel);

export default router; // Export the configured router
