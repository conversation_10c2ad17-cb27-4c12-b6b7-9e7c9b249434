import db from '../../config/db.js';
import {orderFromId,orderStatus} from '../../utils/commons.js';

class Order {
    // Get order report with filters
    static async getOrderReport(filters) {
        
        const needsJoin = !!filters.sender;

        let conditions = [];
        const params = [];

        if (filters.country) {
            conditions.push(`oo.country_id = ?`);
            params.push(filters.country);
        }

        if (needsJoin) {
            conditions.push(`oo.driver_id = ?`);
            params.push(filters.sender);
        }

        // Payment, staff, nationality, gender filters
        if (filters.paymode) {
            if (filters.paymode == 1) {
                // Only Cash
                conditions.push(`oo.payment_methodid = 1`);
            } else {
                // Any non-cash method
                conditions.push(`oo.payment_methodid != 1`);
            }  
        }

        //if (filters.paymentGatewayType) { conditions.push(`oo.payment_methodid = ?`); params.push(filters.paymentGatewayType); }
        if (filters.staffName) { conditions.push(`oo.staff_id = ?`); params.push(filters.staffName); }
        if (filters.nationality) { conditions.push(`an.id = ?`); params.push(filters.nationality); }
        if (filters.gender) { conditions.push(`cc.gender = ?`); params.push(filters.gender); }
        
        if (filters.voucherType === '0') { conditions.push(`(oo.discount_amount IS NULL OR oo.discount_amount = 0)`); }
        if (filters.voucherType === '1') { conditions.push(`oo.discount_amount > 0`); }

        if (filters.orderFrom) {
            const orderFromValues = Array.isArray(filters.orderFrom) ? filters.orderFrom : [filters.orderFrom];
            if (orderFromValues.length > 0) {
                const placeholders = orderFromValues.map(() => '?').join(',');
                conditions.push(`oo.order_from_id IN (${placeholders})`);
                params.push(...orderFromValues);
            }
        }

        let dateField = `oo.order_date`;

        
            if (filters.status === 'Invoiced') {
                conditions.push(`oo.invoice_no IS NOT NULL AND oo.invoice_no != ''`);
            } else {
                switch (filters.status) {
                    case 'Pending':
                        conditions.push(`oo.order_statusid = 1 AND oo.order_status_type = "order_status"`)
                    case 'Despatch':
                        conditions.push(`ooh.order_statusid = 3 AND ooh.order_status_type = "order_status" AND ooh.status like "%dispatch%"`);
                        break;
                    case 'Delivered':
                        conditions.push(`ooh.order_statusid = 6`);
                        break;
                    case 'Cancelled':
                        conditions.push(`ooh.order_statusid IN (7, 11)`);
                        break;
                }
            }

            if (filters.status === 'Invoiced') {
                dateField = `oo.invoice_date`;
            } else if (filters.basedOn === 'status_date') {
                dateField = `ooh.statusdate`;
            }
        

        if (filters.fromDate && filters.toDate) {
            conditions.push(`DATE(${dateField}) BETWEEN DATE(?) AND DATE(?)`);
            params.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            conditions.push(`DATE(${dateField}) = DATE(?)`);
            params.push(filters.fromDate);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        const dataQuery = `
        SELECT 
            DISTINCT(oo.orderid),
            ac.name,
            oo.order_ref_code as orderid,
            oo.order_date,
            oo.total_amount as totalamount,
            oo.shipping_charges as charge,
            oo.donation_fee,
            oo.processing_fee,
            oo.tax_amount as vat,
            oo.discount_amount as discount,
            oo.customer_contact AS contact,
            oo.customer_details, -- retrieve raw JSON
            an.country_name as nationality_name,
            oo.order_statusid AS statusid,
            oo.order_from_id,
            oo.payment_method as payment_mode,
            oo.payment_methodid,
            oo.gway_paymentmethod,
            opm.name,
            oo.gway_transaction_id as fort_id,
            (total_amount) as display_amount 
        FROM oms_orders as oo
        LEFT JOIN oms_order_history as ooh on oo.orderid = ooh.orderid
        LEFT JOIN crm_customer as cc on oo.customerid = cc.id
        LEFT JOIN admin_nationalities as an on cc.nationality = an.id
        LEFT JOIN admin_country as ac on oo.country_id = ac.country_id
        LEFT JOIN oms_payment_method as opm on oo.gway_paymentmethod = opm.id
        ${whereClause} AND oo.order_statusid > 0 and oo.type = "order"
        AND (
            JSON_VALID(oo.customer_details) = 0 
            OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
        )
        ORDER BY oo.order_date ASC
    `;


        const [orders] = await db.query(dataQuery, params);

        // Map order_from_id and status
        const mappedOrders = orders.map(order => ({
            ...order,
            orderfrom: orderFromId[order.order_from_id] || null,
            status: orderStatus[order.statusid] || order.status // fallback to original if mapping missing
        }));

        const totals = {
            totalOrders: orders.length,
            rawTotalAmount: 0,
            shippingCharge: 0,
            processingFees: 0,
            vat: 0,
            discountAmount: 0,
            totalAmount: 0,
            grandTotal: 0,
            donation_fee:0
        };

        for (const order of orders) {
            totals.rawTotalAmount += parseFloat(order.totalamount || 0);
            totals.shippingCharge += parseFloat(order.charge || 0);
            totals.processingFees += parseFloat(order.processing_fee || 0);
            totals.vat += parseFloat(order.vat || 0);
            totals.discountAmount += parseFloat(order.discount || 0);
            totals.donation_fee += parseFloat(order.donation_fee || 0);
        }
        
        totals.totalAmount = totals.rawTotalAmount + totals.discountAmount - (totals.processingFees + totals.vat  + totals.donation_fee);
      
        // totals.totalAmount = totals.rawTotalAmount - (totals.processingFees + totals.discountAmount);
       
        totals.grandTotal = totals.rawTotalAmount;

        return { orders: mappedOrders , totals };
    }
}




export default Order;