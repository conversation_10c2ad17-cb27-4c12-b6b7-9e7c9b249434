import Order from './orderReportModel.js'; // Import order report data model
import exceljs from 'exceljs'; // Excel file generation library

// Render the order report page with user session info
export const renderOrderReportPage = (req, res) => {
    res.render('order-report', { user: req.user });
};

// Handle API request to get filtered order report data
export const handleOrderReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Validate 'country' filter
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country parameter is required." });
        }

        // Fetch report data using filters
        const data = await Order.getOrderReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

// Handle API request to export order report as Excel
export const handleOrderReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch report data and summary totals
        const { orders, totals } = await Order.getOrderReport(filters);

        // Create Excel workbook and worksheet
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Order Report');

        // Define Excel sheet columns
        worksheet.columns = [
            { header: 'No', key: 'no', width: 5 },
            { header: 'Order ID', key: 'orderid', width: 20 },
            { header: 'Order Date', key: 'order_date', width: 25 },
             { header: 'Order From', key: 'orderfrom', width: 25 },
            { header: 'Delivery Date', key: 'delivery_date', width: 25 },
            { header: 'Mobile', key: 'contact', width: 15 },
            { header: 'Customer', key: 'customer', width: 30 },
            { header: 'Status', key: 'status', width: 15 },
            { header: 'Pay Mode', key: 'payment_mode', width: 15 },
            { header: 'Transaction Id', key: 'fort_id', width: 15 },
            { header: 'Amount', key: 'display_amount', width: 15, style: { numFmt: '#,##0.00' } },
        ];
        worksheet.getRow(1).font = { bold: true }; // Bold header row

        // Add order data rows to sheet
       orders.forEach((order, index) => {
        let customerName = '';
        let gender = '';
        let emirateName = '';

        // Safely parse customer_details JSON
        try {
            if (order.customer_details) {
                const details = JSON.parse(order.customer_details);
                customerName = details.name || '';
                gender = details.gender || '';
                emirateName = details.emirate_name || '';
            }
        } catch (err) {
            // Invalid JSON, fallback to empty strings
        }

        // Skip test customers if needed
        if (customerName.toLowerCase().includes('test')) return;

       worksheet.addRow({
            no: index + 1,
            orderid: order.orderid,
            order_date: order.order_date ? new Date(order.order_date).toLocaleString('en-GB') : '',
            delivery_date: order.delivery_date ? new Date(order.delivery_date).toLocaleString('en-GB') : '',
            contact: order.contact || '',
            customer: `${customerName}`,
            status: order.status || '',        
            orderfrom: order.orderfrom || '',  
            payment_mode: order.payment_methodid == 1 ? 'CreditCardPayments' : 'CashOnDelivery', 
            fort_id: order.fort_id,
            display_amount: parseFloat(order.display_amount || 0)
        });
    });


        // Append totals summary rows
        worksheet.addRows([
            {},
            { orderid: 'Total Orders', display_amount: totals.totalOrders || 0 },
            { orderid: 'Total Amount', display_amount: totals.totalAmount || 0 },
            { orderid: 'Shipping Charge', display_amount: totals.shippingCharge || 0 },
            { orderid: 'Processing Fees', display_amount: totals.processingFees || 0 },
            { orderid: 'VAT', display_amount: totals.vat || 0 },
            { orderid: 'Discount Amount', display_amount: totals.discountAmount || 0 }
        ]);

        // Add final Grand Total row with bold styling
        const grandTotalRow = worksheet.addRow({ orderid: 'Grand Total', display_amount: totals.grandTotal || 0 });
        grandTotalRow.font = { bold: true };

        // Set headers for Excel file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="OrderReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        // Stream Excel file to response
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
