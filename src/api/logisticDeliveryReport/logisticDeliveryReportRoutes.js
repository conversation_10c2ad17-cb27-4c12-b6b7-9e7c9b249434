import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderLogisticDeliveryReportPage,
    handleLogisticDeliveryReportApi,
    handleLogisticDeliveryReportExcel
} from '../logisticDeliveryReport/logisticDeliveryReportController.js';

const router = express.Router();

router.get('/logistic-delivery-report', verifyToken, renderLogisticDeliveryReportPage);
router.get('/api/reports/logistic-delivery', verifyToken, handleLogisticDeliveryReportApi);
router.get('/api/reports/logistic-delivery/export', verifyToken, handleLogisticDeliveryReportExcel);

export default router;