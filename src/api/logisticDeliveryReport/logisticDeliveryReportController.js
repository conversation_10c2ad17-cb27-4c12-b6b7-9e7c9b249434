import LogisticDeliveryReport from '../logisticDeliveryReport/logisticDeliveryReportModel.js';
import exceljs from 'exceljs';

export const renderLogisticDeliveryReportPage = (req, res) => {
    res.render('logistic-delivery-report', { user: req.user, pageTitle: "Logistics Delivery Report" });
};

export const handleLogisticDeliveryReportApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.country || !filters.month || !filters.year) {
            return res.status(400).json({ success: false, error: "Country, Year, and Month are required." });
        }
        const data = await LogisticDeliveryReport.getReport(filters);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching logistic delivery report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

export const handleLogisticDeliveryReportExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { reportData, totals } = await LogisticDeliveryReport.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('LogisticDeliveryReport');

        worksheet.columns = [
            { header: 'Sender', key: 'sender_name', width: 25 },
            { header: 'Opening Count', key: 'opening_count_display', width: 20 },
            { header: 'Despatch (CM)', key: 'despatch_cm', width: 18 },
            { header: 'Delivered (CM)', key: 'delivered_cm', width: 18 },
            { header: 'Delivered (%)', key: 'delivery_percentage', width: 15, style: { numFmt: '0"%"' } },
            { header: 'Return (CM)', key: 'return_cm', width: 15 },
            { header: 'Return (%)', key: 'return_percentage', width: 15, style: { numFmt: '0"%"' } },
            { header: 'Pending (%)', key: 'pending_display', width: 20 }
        ];
        worksheet.getRow(1).font = { bold: true };

        reportData.forEach(row => {
            worksheet.addRow({
                sender_name: row.sender_name,
                opening_count_display: `${row.total_shipments} - ${row.opening_count}`,
                despatch_cm: row.despatch_cm,
                delivered_cm: row.delivered_cm,
                delivery_percentage: Math.round(row.delivery_percentage),
                return_cm: row.return_cm,
                return_percentage: Math.round(row.return_percentage),
                pending_display: `${row.pending_cm} (${Math.round(row.pending_percentage)}%)`
            });
        });
        
        worksheet.addRow({});
        const totalRow = worksheet.addRow({
            sender_name: 'Total',
            opening_count_display: totals.total_opening,
            despatch_cm: totals.total_despatch,
            delivered_cm: totals.total_delivered,
            delivery_percentage: Math.round(totals.total_dp),
            return_cm: totals.total_return,
            return_percentage: Math.round(totals.total_rp),
            pending_display: `(${Math.round(totals.total_pp)}%)`
        });
        totalRow.font = { bold: true };


        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="LogisticDeliveryReport-${filters.year}-${filters.month}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};