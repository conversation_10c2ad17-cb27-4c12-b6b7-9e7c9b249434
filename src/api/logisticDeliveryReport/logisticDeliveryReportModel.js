import db from '../../config/db.js';

class LogisticDeliveryReport {
    static async getReport(filters) {
        const { country, month, year } = filters;
        const sMonth = parseInt(month, 10);
        const sYear = parseInt(year, 10);

        const startDate = new Date(sYear, sMonth - 1, 1);
        const endDate = new Date(sYear, sMonth, 0);

        const startDateString = startDate.toISOString().slice(0, 10) + ' 00:00:00';
        const endDateString = endDate.toISOString().slice(0, 10) + ' 23:59:59';
        
        const params = [];
        let countryClause = "";
        if (country) {
            countryClause = "AND o.country_id = ?";
            params.push(country);
        }

        const finalParams = [
            startDateString,                // For LastStatusBeforeMonth CTE
            startDateString,                // For opening_count date check
            startDateString, endDateString, // For Despatched
            startDateString, endDateString, // For Delivered
            startDateString, endDateString, // For Returned
            ...params
        ];

        const reportQuery = `
            -- <<< OPTIMIZATION: Pre-calculate the last status of all relevant orders ONCE.
            WITH LastStatusBeforeMonth AS (
                SELECT 
                    orderid,
                    order_statusid
                FROM (
                    SELECT 
                        orderid, 
                        order_statusid,
                        -- This assigns a rank to each history entry per order, with 1 being the latest.
                        ROW_NUMBER() OVER(PARTITION BY orderid ORDER BY statusdate DESC) as rn
                    FROM oms_order_history
                    WHERE statusdate < ?
                ) AS ranked_history
                WHERE rn = 1
            )
            SELECT
                u.id AS sender_id,
                u.firstname,
                u.lastname,
                COALESCE(metrics.opening_count, 0) AS opening_count,
                COALESCE(metrics.despatch_cm, 0) AS despatch_cm,
                COALESCE(metrics.delivered_cm, 0) AS delivered_cm,
                COALESCE(metrics.return_cm, 0) AS return_cm
            FROM aauth_users u
            INNER JOIN (
                SELECT
                    o.driver_id,
                    -- <<< OPTIMIZATION: Replaced the slow subquery with a fast JOIN.
                    COUNT(DISTINCT CASE 
                        WHEN o.order_date < ? AND ls.order_statusid NOT IN (6, 8) THEN o.orderid 
                    END) AS opening_count,

                    COUNT(DISTINCT CASE WHEN o.order_date BETWEEN ? AND ? THEN o.orderid END) AS despatch_cm,
                    COUNT(DISTINCT CASE WHEN h.order_statusid = 6 AND h.statusdate BETWEEN ? AND ? THEN o.orderid END) AS delivered_cm,
                    COUNT(DISTINCT CASE WHEN h.order_statusid = 8 AND h.statusdate BETWEEN ? AND ? THEN o.orderid END) AS return_cm
                FROM oms_orders o
                LEFT JOIN oms_order_history h ON o.orderid = h.orderid
                -- Join the pre-calculated last status data. This is extremely fast.
                LEFT JOIN LastStatusBeforeMonth ls ON o.orderid = ls.orderid
                WHERE o.driver_id IS NOT NULL ${countryClause}
                GROUP BY o.driver_id
            ) AS metrics ON u.id = metrics.driver_id
            WHERE 
                metrics.opening_count > 0 
                OR metrics.despatch_cm > 0 
                OR metrics.delivered_cm > 0 
                OR metrics.return_cm > 0
        `;
        
        const [reportDataRaw] = await db.query(reportQuery, finalParams);

        const reportData = reportDataRaw.map(row => {
            const opening_count = parseInt(row.opening_count, 10) || 0;
            const despatch_cm = parseInt(row.despatch_cm, 10) || 0;
            const delivered_cm = parseInt(row.delivered_cm, 10) || 0;
            const return_cm = parseInt(row.return_cm, 10) || 0;
            
            const total_shipments = opening_count + despatch_cm;
            const pending_cm = Math.max(0, total_shipments - delivered_cm - return_cm);

            return {
                sender_name: `${row.firstname} ${row.lastname}`,
                total_shipments_display: total_shipments,
                opening_count,
                despatch_cm,
                delivered_cm,
                return_cm,
                pending_cm,
                delivery_percentage: total_shipments > 0 ? ((delivered_cm / total_shipments) * 100).toFixed(2) : "0.00",
                return_percentage: total_shipments > 0 ? ((return_cm / total_shipments) * 100).toFixed(2) : "0.00",
                pending_percentage: total_shipments > 0 ? ((pending_cm / total_shipments) * 100).toFixed(2) : "0.00",
            };
        });

        reportData.sort((a, b) => a.sender_name.localeCompare(b.sender_name));

        const totals = reportData.reduce((acc, row) => {
            acc.total_opening += row.opening_count;
            acc.total_despatch += row.despatch_cm;
            acc.total_delivered += row.delivered_cm;
            acc.total_return += row.return_cm;
            acc.total_pending += row.pending_cm;
            acc.total_shipments += row.total_shipments_display;
            return acc;
        }, { total_opening: 0, total_despatch: 0, total_delivered: 0, total_return: 0, total_pending: 0, total_shipments: 0 });

        totals.total_dp = totals.total_shipments > 0 ? ((totals.total_delivered / totals.total_shipments) * 100).toFixed(2) : "0.00";
        totals.total_rp = totals.total_shipments > 0 ? ((totals.total_return / totals.total_shipments) * 100).toFixed(2) : "0.00";
        totals.total_pp = totals.total_shipments > 0 ? ((totals.total_pending / totals.total_shipments) * 100).toFixed(2) : "0.00";

        return { reportData, totals };
    }
}

export default LogisticDeliveryReport;