import QuickReport from '../quickReport/quickReportModel.js';
import exceljs from 'exceljs';

export const renderQuickReportPage = (req, res) => {
    res.render('quick-report', { user: req.user, pageTitle: "Quick Report" });
};

export const handleQuickReportApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required filter." });
        }
        const reportData = await QuickReport.getReport(filters);
        const currencyCode = await QuickReport.getCurrencyByCountryId(filters.country);

        res.json({ success: true, data: { orders: reportData, currencyCode } }); 
    } catch (error) {
        console.error("API Error fetching quick report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};


export const handleQuickReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        const reportData = await QuickReport.getReport(filters);

        if (!reportData || reportData.length === 0) {
            return res.status(404).send("No data found for the selected filters to generate an Excel file.");
        }

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('QuickReport');

        worksheet.columns = [
            { header: 'No', key: 'sl_no', width: 8 },
            { header: 'Order Ref', key: 'order_ref_code', width: 20 },
            { header: 'Mobile No', key: 'customer_contact', width: 18 },
            { header: 'Customer Name', key: 'customer_name', width: 30 },
            { header: 'Product Name', key: 'product_name', width: 60 },
            { header: 'Order Status', key: 'order_status', width: 15 },
            { header: 'PayMode', key: 'payment_method_name', width: 20 },
            { header: 'Amount', key: 'total_amount', width: 15, style: { numFmt: '#,##0.00' } }
        ];
        worksheet.getRow(1).font = { bold: true };

        reportData.forEach((row, index) => {
            worksheet.addRow({
                sl_no: index + 1,
                ...row 
            });
        });

        worksheet.getColumn('product_name').alignment = { wrapText: true, vertical: 'top' };

        const totals = {
            totalOrders: 0,
            totalAmount: 0,
            shippingCharge: 0,
            grandTotal: 0
        };
        const uniqueOrders = new Map();
        reportData.forEach(row => {
            if (!uniqueOrders.has(row.orderid)) {
                uniqueOrders.set(row.orderid, {
                    total_amount: parseFloat(row.total_amount || 0)
                });
            }
        });
        
        totals.totalOrders = uniqueOrders.size;
        uniqueOrders.forEach(order => {
            totals.grandTotal += order.total_amount;
        });
        totals.totalAmount = totals.grandTotal; 


        const startRow = reportData.length + 3;
        worksheet.getCell(`G${startRow}`).value = 'Total Orders:';
        worksheet.getCell(`H${startRow}`).value = totals.totalOrders;
        worksheet.getCell(`G${startRow+1}`).value = 'Total Amount:';
        worksheet.getCell(`H${startRow+1}`).value = totals.totalAmount;
        worksheet.getCell(`H${startRow+1}`).numFmt = '#,##0.00';
        worksheet.getCell(`G${startRow+2}`).value = 'Shipping Charge:';
        worksheet.getCell(`H${startRow+2}`).value = totals.shippingCharge;
        worksheet.getCell(`H${startRow+2}`).numFmt = '#,##0.00';
        worksheet.getCell(`G${startRow+3}`).value = 'Grand Total:';
        worksheet.getCell(`H${startRow+3}`).value = totals.grandTotal;
        worksheet.getCell(`H${startRow+3}`).numFmt = '#,##0.00';
        worksheet.getCell(`G${startRow+3}`).font = { bold: true };
        worksheet.getCell(`H${startRow+3}`).font = { bold: true };

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="QuickReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};