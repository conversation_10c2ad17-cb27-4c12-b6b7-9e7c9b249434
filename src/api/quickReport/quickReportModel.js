import db from '../../config/db.js';

class QuickReport {

    static async getReport(filters) {
        return this.getStandardReport(filters);
    }

    static async getStandardReport(filters) {
        const { country, status, paymode, fromDate, toDate } = filters;

        const conditions = [];
        const params = [];


        if (country) {
            conditions.push('oms_orders.country_id = ?');
            params.push(country);
        }

        if (paymode) {
            conditions.push('oms_orders.payment_method = ?');
            params.push(paymode);
        }

        if (status) {
            conditions.push('oms_orders.order_status = ?');
            params.push(status);
        }

        if (fromDate && toDate) {
            conditions.push('DATE(oms_orders.order_date) BETWEEN ? AND ?');
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions.push('DATE(oms_orders.order_date) = ?');
            params.push(fromDate);
        } else if (toDate) {
            conditions.push('DATE(oms_orders.order_date) = ?');
            params.push(toDate);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        const sql = `
            SELECT
                oms_orders.order_date AS order_date,
                ac.name AS country,
                oms_orders.order_ref_code,
                oms_orders.orderid,
                opm.name AS payment_method_name,
                oms_orders.customer_contact,
                JSON_UNQUOTE(JSON_EXTRACT(oms_orders.customer_details, '$.name')) AS customer_name,
                cp.title AS product_name,
                oms_orders.order_status,
                oms_orders.payment_method AS pay_mode,
                oms_orders.shipping_charges,
                CASE
                    WHEN oms_orders.country_id = 1 THEN oms_orders.total_amount
                    ELSE (oms_orders.total_amount)
                END AS total_amount
            FROM oms_orders
            LEFT JOIN admin_country AS ac
                ON oms_orders.country_id = ac.country_id
            LEFT JOIN oms_payment_method AS opm
                ON oms_orders.payment_methodid = opm.payment_type
            LEFT JOIN oms_order_detail AS ood
                ON oms_orders.orderid = ood.orderid
            LEFT JOIN catalog_product AS cp
                ON ood.product_sku = cp.sku
            ${whereClause}
            ORDER BY oms_orders.order_date DESC
        `;
        try {
            const [orders] = await db.query(sql, params);
            return orders;
        } catch (error) {
            console.error("Error fetching standard report:", error);
            return [];
        }
    }

    static async getCurrencyByCountryId(countryId) {
        const defaultCurrency = 'AED';
        if (!countryId) return defaultCurrency;

        try {
            const query = 'SELECT currency FROM admin_country WHERE country_id = ?';
            const [rows] = await db.query(query, [countryId]);

            return (rows.length > 0 && rows[0].currency) ? rows[0].currency : defaultCurrency;
        } catch (error) {
            console.error("Error fetching currency by country ID:", error);
            return defaultCurrency;
        }
    };

}

export default QuickReport;