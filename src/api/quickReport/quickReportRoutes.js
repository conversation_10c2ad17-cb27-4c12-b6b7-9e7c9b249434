import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderQuickReportPage,
    handleQuickReportApi,
    handleQuickReportExcel
} from '../quickReport/quickReportController.js';

const router = express.Router();

router.get('/quick-report', verifyToken, (req, res) => {
    res.render('quick-report', {
        pageTitle: "Quick Report",
        user: req.user
    });
});

router.get('/quick-report', verifyToken, renderQuickReportPage);

router.get('/api/reports/quick', verifyToken, handleQuickReportApi);

router.get('/api/reports/quick/export', verifyToken, handleQuickReportExcel);

export default router;