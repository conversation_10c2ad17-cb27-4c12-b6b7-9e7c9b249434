import ProcurementReport from '../procurementReport/procurementReportModel.js';
import exceljs from 'exceljs';

// Render procurement report EJS page
export const renderProcurementReportPage = (req, res) => {
    res.render('reports/procurement-report', { user: req.user, pageTitle: "Procurement Report" });
};

// API: Fetch procurement report data (JSON)
export const handleProcurementReportApi = async (req, res) => {
    try {
        const data = await ProcurementReport.getMainReportData(req.query);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching procurement report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// --- API: Update product cost ---
export const updateProductCost = async (req, res) => {
    try {
        const { productId, cost, countryId } = req.body;
        if (!productId || cost === undefined || !countryId) {
            return res.status(400).json({ success: false, message: 'Missing required fields.' });
        }
        await ProcurementReport.updateProductCost(productId, cost, countryId);
        res.json({ success: true, message: 'Cost Updated Successfully' });
    } catch (error) {
        console.error('Error updating product cost:', error);
        res.status(500).json({ success: false, message: 'Failed to update cost.' });
    }
};

// --- API: Update supplier paymode ---
export const updateSupplierPaymode = async (req, res) => {
    try {
        const { supplierId, paymode } = req.body;
        if (!supplierId || !paymode) {
            return res.status(400).json({ success: false, message: 'Missing required fields.' });
        }
        await ProcurementReport.updateSupplierPaymode(supplierId, paymode);
        res.json({ success: true, message: 'Paymode updated.' });
    } catch (error) {
        console.error('Error updating supplier paymode:', error);
        res.status(500).json({ success: false, message: 'Failed to update paymode.' });
    }
};

// Format row for standard Excel export
const formatRowForStandardExport = (row) => ({
    orderId: row.orderid || '',
    orderDate: row.order_date,
    buyer: row.buyer_name || '',
    product: row.name || '',
    sku: row.product_code || '',
    orderQty: row.pqty || 0,
    paymentType: row.payment_type || '',
    supplier: row.supplier?.company_name || '',
    supplierPaymode: row.supplier?.paymode || '',
    supplierContact: row.supplier?.contact_number1 || '',
    totalOrderQty: row.orderqty || 0,
    stockQty: row.stockQty || 0,
    buyQty: row.buyQty || 0,
    cost: row.actualCost || 0,
    sellingPrice: row.price || 0
});

// API: Export procurement report to Excel
export const handleProcurementReportExcel = async (req, res) => {
    try {
        const { countryId, type } = req.query;
        if (!type) return res.status(400).send("Missing report type.");

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Report');
        let columns, dataToExport;

        if (type === 'grouped') {
            // --- Grouped Excel Export (for procurement report download) ---
            const results = await ProcurementReport.getGroupedExportData({ countryId });
            worksheet.title = 'Procurement Report (Grouped)';
            columns = [
                { header: 'Buyer Name', key: 'buyer', width: 25 },
                { header: 'Product Name', key: 'product', width: 50 },
                { header: 'SKU', key: 'sku', width: 20 },
                { header: 'Order Qty', key: 'orderQty', width: 15 },
                { header: 'PaymentType', key: 'paymentType', width: 20 },
                { header: 'Supplier Name', key: 'supplierName', width: 30 },
                { header: 'Supplier Paymode', key: 'supplierPaymode', width: 15 },
                { header: 'Supplier Contact', key: 'supplierContact', width: 20 },
                { header: 'Safari ID', key: 'safariId', width: 15 },
                { header: 'Safari Category', key: 'safariCategory', width: 15 },
                { header: 'Total Order Qty', key: 'totalOrderQty', width: 15 },
                { header: 'Stock Qty', key: 'stockQty', width: 15 },
                { header: 'Total Buyer Qty[OQ-SQ]', key: 'buyQty', width: 25 },
                { header: 'Product Cost', key: 'cost', width: 15 },
                { header: 'Total Cost/Pc', key: 'totalCost', width: 15 }
            ];
            dataToExport = results.map(row => ({
                buyer: row.bname?.name || '',
                product: row.name,
                sku: row.product_code,
                orderQty: row.pqty,
                paymentType: row.paymentType,
                supplierName: row.supplier_details?.company_name || '',
                supplierPaymode: row.supplier_details?.paymode || '',
                supplierContact: row.supplier_details?.contact_number1 || '',
                safariId: row.safari_id,
                safariCategory: row.safari_catagory,
                totalOrderQty: row.orderqty,
                stockQty: row.cqty,
                buyQty: row.buyqty,
                cost: row.actualCost,
                totalCost: (row.actualCost || 0) * (row.buyqty || 0)
            }));
        } else {
            // --- Standard Excel Export ---
            const { reportData } = await ProcurementReport.getMainReportData({ countryId });
            worksheet.title = 'Procurement Report (Standard)';
            columns = [
                { header: 'Order Id', key: 'orderId', width: 20 },
                { header: 'Order Date', key: 'orderDate', width: 25 },
                { header: 'Buyer Name', key: 'buyer', width: 25 },
                { header: 'Product Name', key: 'product', width: 50 },
                { header: 'SKU', key: 'sku', width: 20 },
                { header: 'Order Qty', key: 'orderQty', width: 15 },
                { header: 'Payment Type', key: 'paymentType', width: 20 },
                { header: 'Supplier Name', key: 'supplierName', width: 30 },
                { header: 'Supplier Paymode', key: 'supplierPaymode', width: 15 },
                { header: 'Supplier Contact', key: 'supplierContact', width: 20 },
                { header: 'Safari ID', key: 'safariId', width: 15 },
                { header: 'Safari Category', key: 'safariCategory', width: 15 },
                { header: 'Total Order Qty', key: 'totalOrderQty', width: 15 },
                { header: 'Stock Qty', key: 'stockQty', width: 15 },
                { header: 'Total Buyer Qty[OQ-SQ]', key: 'buyQty', width: 25 },
                { header: 'Product Cost/Pc', key: 'cost', width: 15 },
                { header: 'Selling Price', key: 'sellingPrice', width: 15 }
            ];
            dataToExport = reportData.map(row => ({
                orderId: row.orderid,
                orderDate: row.order_date,
                buyer: row.buyer_name || '',
                product: row.name,
                sku: row.product_code,
                orderQty: row.pqty,
                paymentType: row.payment_type || '',
                supplierName: row.supplier?.company_name || '',
                supplierPaymode: row.supplier?.paymode || '',
                supplierContact: row.supplier?.contact_number1 || '',
                safariId: row.safari_id,
                safariCategory: row.safari_catagory,
                totalOrderQty: row.orderqty,
                stockQty: row.stockQty,
                buyQty: row.buyQty,
                cost: row.actualCost,
                sellingPrice: row.price
            }));
        }

        worksheet.columns = columns;
        worksheet.getRow(1).font = { bold: true };
        worksheet.addRows(dataToExport);

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="ProcurementReport-${type}-${new Date().toISOString().slice(0,10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
