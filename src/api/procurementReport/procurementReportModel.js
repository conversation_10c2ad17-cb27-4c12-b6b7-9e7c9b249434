import db from '../../config/db.js';

class ProcurementReport {
    // Update cost of a product
    static async updateProductCost(productId, newCost, countryId) {
        await db.query(`UPDATE catalog_product_inventory SET cost = ? WHERE productid = ? AND country_id = ?`, [newCost, productId, countryId]);
        return true;
    }

    // Update supplier's paymode
    static async updateSupplierPaymode(supplierId, newPaymode) {
        await db.query(`UPDATE admin_supplier SET paymode = ? WHERE supplierid = ?`, [newPaymode, supplierId]);
        return true;
    }

    // Get procurement data for standard view/export
    static async getMainReportData(filters) {
        const { countryId } = filters;
        if (!countryId) return { reportData: [], paymentSummary: {} };

        // SQL for detailed procurement report
        const sql = `
            WITH LatestPurchaseSupplier AS (
                SELECT prod.productid as product_id, s.supplierid as sid, s.company_name,s.supplier_name, s.primary_contact AS contact_number1, s.paymode,
                ROW_NUMBER() OVER(PARTITION BY prod.title ORDER BY p.purchase_id DESC) as rn
                FROM procurement_purchase_products pp
                JOIN catalog_product prod ON prod.productid = pp.productid
                JOIN procurement_purchase p ON p.purchase_id = pp.purchase_id
                JOIN admin_supplier s ON p.supplierid = s.supplierid
                WHERE p.supplierid != 8
            )
            SELECT
                ord.orderid as id,ord.order_ref_code as orderid, ord.order_date, odet.quantity as pqty, prod.productid as pid, prod.title AS name, prod.sku AS product_code, 
                inv.inventory as cqty, inv.selling_price as price, inv.cost as pcost, 
                prod.buyer_id as buyer_id,
                buyer.firstname as buyer_name, pm.name as payment_type,
                lps.sid as supplier_id,
                lps.company_name,
                lps.supplier_name,
                lps.contact_number1 as supplier_contact,
                lps.paymode as supplier_paymode
            FROM oms_orders ord  
            LEFT JOIN oms_order_detail odet ON ord.orderid = odet.orderid AND ord.country_id = ${countryId}
            LEFT JOIN catalog_product prod ON odet.productid = prod.productid
            LEFT JOIN catalog_product_inventory inv ON prod.productid = inv.productid
            LEFT JOIN aauth_users buyer ON prod.buyer_id = buyer.id
            LEFT JOIN oms_payment_method pm ON ord.payment_methodid = pm.id
            LEFT JOIN LatestPurchaseSupplier lps ON lps.product_id = odet.productid AND lps.rn = 1
            WHERE inv.inventory > 0 AND ord.order_statusid=1 AND inv.country_id = ${countryId}
            Group by ord.orderid
            ORDER BY ord.order_date ASC
        `;

        const [results] = await db.query(sql);

        // Get fallback costs from ourshopee_products
        const ourshopeeCosts = {};
        const productCodes = results.map(r => r.product_code).filter(c => c);
        if (productCodes.length > 0) {
            const costSql = `SELECT prod.sku, inv.cost FROM catalog_product prod
            INNER JOIN catalog_product_inventory inv ON prod.productid = inv.productid AND country_id = ${countryId}
            WHERE prod.sku IN (?)`;
            const [costs] = await db.query(costSql, [productCodes]);
            costs.forEach(c => { ourshopeeCosts[c.sku] = c.cost; });
        }

        const reportData = [];
        const paymentSummary = { Cash: { count: 0, value: 0 }, Credit: { count: 0, value: 0 }, Other: { count: 0, value: 0 } };

        // Process each row
        for (const row of results) {
            const stockQty = (row.cqty || 0) + (row.orderqty || 0);
            const difference = (row.orderqty || 0) - stockQty;

            if (difference > 0) {
                let actualCost = row.pcost || 0;
                if (actualCost < 1) {
                    actualCost = ourshopeeCosts[row.product_code] || (countryId === '1' ? 1 : 0);
                }

                const supplier = {
                    sid: row.supplier_id,
                    company_name: row.supplier_name,
                    contact_number1: row.supplier_contact,
                    paymode: row.supplier_paymode
                };

                const paymode = supplier.paymode || 'Other';
                paymentSummary[paymode] = paymentSummary[paymode] || { count: 0, value: 0 };
                paymentSummary[paymode].count++;
                paymentSummary[paymode].value += parseFloat(actualCost || 0);

                reportData.push({ ...row, stockQty, buyQty: difference, actualCost, supplier });
            }
        }
        return { reportData, paymentSummary };
    }

    // --- Grouped report data for Excel export ---
    static async getGroupedExportData(filters) {
        const { countryId } = filters;
        if (!countryId) return [];

        // Hardcoded UAE query for grouped procurement export
        const sql = `
            SELECT ord.orderid, ord.order_date, inv.inventory as pqty, prod.title AS name, prod.sku as product_code, prod.quantity as cqty, 
                   prod.price as price, prod.id as pid, prod.cost as pcost, prod.pending_quantity as orderqty, 
                   prod.buyer as buyer, prod.category as category, prod.web_product_id as webpid, op.name as paymentType,
                   prod.safari_id, prod.safari_catagory
            FROM oms_orders ord
            JOIN oms_order_detail odet ON odet.orderid = ord.orderid
            JOIN catalog_product prod ON prod.sku = odet.product_skuˀ
            JOIN catalog_product_inventory inv ON prod.productid = inv.productid
            WHERE inv.inventory > 0 AND ord.order_statusid='1' 
            AND odet.country_id=${countryId}
            GROUP BY prod.productid
        `;
        const [results] = await db.query(sql);

        const processedResults = [];
        for (const row of results) {
            const bname_res = await db.query("SELECT * FROM aauth_users WHERE id=?", [row.buyer]);
            const pdetails_res = await db.query("SELECT cost FROM catalog_product WHERE sku=?", [row.product_code]);
            const supplier_res1 = await db.query(
                `SELECT c.company_name,c.contact_number1,c.paymode
                 FROM shopee_purchase a, shopee_purchaseproducts b, shopee_supplier c
                 WHERE a.id = b.purchase_id AND b.item_name = ? AND a.supplier_name = c.id
                 AND a.supplier_name != 8 ORDER BY a.id DESC LIMIT 1`, [row.pid]);

            let supplier_details = supplier_res1[0][0];

            if (!supplier_details) {
                const supplier_res2 = await db.query(
                    `SELECT a.company_name,a.contact_number1,a.paymode
                     FROM shopee_supplier a, ourshopee_product_suppliers b
                     WHERE a.id = b.supplier_id AND b.product_id = ?
                     ORDER BY a.id DESC LIMIT 1`, [row.webpid]);
                supplier_details = supplier_res2[0][0];
            }

            let actualCost = row.pcost || 0;
            if (actualCost < 1 && pdetails_res[0].length > 0) {
                actualCost = pdetails_res[0][0].cost;
            }
            if (actualCost === 0) actualCost = 1;

            const cqty = (row.cqty || 0) + (row.orderqty || 0);
            const buyqty = (row.orderqty || 0) > cqty ? (row.orderqty || 0) - cqty : 0;

            processedResults.push({ ...row, bname: bname_res[0][0], supplier_details, actualCost, cqty, buyqty });
        }
// return results
        return processedResults;
    }
}

export default ProcurementReport;
