// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

// Procurement Report Controllers
import {
    renderProcurementReportPage,
    handleProcurementReportApi,
    handleProcurementReportExcel,
    updateProductCost,
    updateSupplierPaymode
} from '../procurementReport/procurementReportController.js';

const router = express.Router();

// ======================================================
// PROCUREMENT REPORT PAGE RENDER
// ======================================================
router.get('/procurement-report', verifyToken, (req, res) => {
    res.render('procurement-report', {
        pageTitle: "PROCUREMENT REPORT",
        user: req.user
    });
});

// ======================================================
// PROCUREMENT REPORT ROUTES
// ======================================================
router.get('/reports/procurement', verifyToken, renderProcurementReportPage);           // Render procurement report page
router.get('/api/reports/procurement', verifyToken, handleProcurementReportApi);       // Fetch procurement data (JSON)
router.get('/api/reports/procurement/export', verifyToken, handleProcurementReportExcel); // Export procurement data (Excel)
router.post('/api/reports/procurement/update-cost', verifyToken, updateProductCost);   // Update product cost
router.post('/api/reports/procurement/update-paymode', verifyToken, updateSupplierPaymode); // Update supplier paymode

export default router;
