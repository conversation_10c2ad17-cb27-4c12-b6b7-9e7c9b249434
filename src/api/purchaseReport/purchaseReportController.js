import purchaseReport from './purchaseReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderPurchaseReportPage = (req, res) => {
    res.render('profit-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handlePurchaseReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Ensure 'country' filter is provided
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country parameter is required." });
        }

        // Get filtered order data from model
        const data = await purchaseReport.getPurchaseReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

// API handler to export order report data as Excel fil

export const handlePurchaseReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    const { productReports = [], grandTotals = {} } = await purchaseReport.getPurchaseReport(filters);

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Purchase Report');

    // Define the columns for the report
    worksheet.columns = [
      { header: 'No', key: 'no', width: 5 },
      { header: 'Purchase Date', key: 'bill_date', width: 10 },
      { header: 'SKU', key: 'sku', width: 10 },
      { header: 'Product', key: 'productName', width: 40 },
      { header: 'Category', key: 'parent_category', width: 40 },
      { header: 'Sub Category', key: 'sub_category', width: 40 },
      { header: 'Qty', key: 'quantity', width: 10 },
      { header: 'Price', key: 'price', width: 15 },
      { header: 'Total', key: 'total', width: 15 },
      { header: 'Supplier', key: 'supplier_name', width: 15 },
      { header: 'Buyer', key: 'buyer_name', width: 15 },
    ];

    worksheet.getRow(1).font = { bold: true };

    // Add data rows
     let grandTotal = 0;
     let totalQuantity = 0;

    productReports.forEach((p, i) => {
      grandTotal += parseFloat(p.totalCost);
      totalQuantity += parseInt(p.quantity);
      
      // const total = parseFloat(item.price * item.quantity).toFixed(2);
      worksheet.addRow({
       

        no: i + 1,
        bill_date: p.bill_date,
        sku: p.sku,
        productName: p.productName ,
        parent_category: p.parent_category ,
        sub_category: p.sub_category ,
        quantity: p.quantity,
        price: parseFloat(p.price).toFixed(2),
        total: p.totalCost,
        supplier_name: p.supplier_name,
        buyer_name: p.buyer_name,
      });
    });

    // Add spacing row
    worksheet.addRow([]);

    // Add summary rows
    worksheet.addRow(['Total Items', productReports.length]);
    worksheet.addRow(['Total Quantity', totalQuantity || 0]);
    worksheet.addRow(['Total Amount', `${(grandTotal || 0).toFixed(2)} AED`]);

    // Set headers and send Excel file
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="PurchaseReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('Excel Export Error:', error);
    res.status(500).send('Could not generate Excel file.');
  }
};


