import db from '../../config/db.js';

class purchaseReport {
  static async getPurchaseReport(filters = {}) {
    try {
      const {
        fromDate,
        toDate,
        country = 1,
        category,
        product,
      } = filters;

      const country_id = parseInt(country);
      
      const conditions = ['ppp.country_id = ?']; 
      const params = [country_id];

      if (fromDate && !toDate) {
        conditions.push(`DATE(pp.bill_date) = ?`);
        params.push(fromDate);
      } else if (fromDate && toDate) {
        conditions.push(`DATE(pp.bill_date) BETWEEN ? AND ?`);
        params.push(fromDate, toDate);
      }

      if (category) {
        conditions.push(`cp.categoryid = ?`);
        params.push(category);
      }

      if (product) {
        conditions.push(`ppp.productid = ?`);
        params.push(product);
      }
      
      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      /*const oldSql = `
            SELECT 
              cp.sku,
              cp.productid AS productId,
              cp.title AS productName,
              cp.sku AS productCode,
              ppp.price,
              ppp.inventory AS quantity,
              (ppp.inventory * ppp.price) AS totalCost
            FROM procurement_purchase_products ppp
            INNER JOIN catalog_product cp 
              ON ppp.productid = cp.productid
            INNER JOIN catalog_category cc 
              ON cp.categoryid = cc.categoryid
            LEFT JOIN catalog_product_inventory cpi 
              ON ppp.productid = cpi.productid
            LEFT JOIN admin_country ac 
              ON cpi.country_id = ac.country_id
            ${whereClause}
            GROUP BY cp.title
            ORDER BY totalCost DESC
      `;*/
     
      const sql = `SELECT 
          ppp.purchase_id,
          cp.productid AS productId,
          cp.title AS productName,
          cp.sku,
          ppp.price,
          ppp.inventory AS quantity,
          (ppp.inventory * ppp.price) AS totalCost,
          (ppp.inventory * ppp.total) AS vatTotalCost,
          DATE_FORMAT(pp.bill_date, '%Y-%m-%d') AS bill_date,
          asp.company_name AS supplier_name,
        concat(au.firstname, ' ', au.lastname) as buyer_name,
        c3.category_name AS parent_category,
            c2.category_name AS sub_category,
            c1.category_name AS subsub_category
        FROM procurement_purchase_products ppp
        JOIN catalog_product cp 
          ON cp.productid = ppp.productid
        LEFT JOIN catalog_product_inventory cpi              
          ON cpi.productid = ppp.productid
        AND cpi.country_id = ppp.country_id                 
        LEFT JOIN procurement_purchase pp 
          ON pp.purchase_id = ppp.purchase_id
        LEFT JOIN admin_supplier asp 
          ON asp.supplierid = pp.supplierid
        LEFT JOIN aauth_users au ON au.id = pp.buyer
        LEFT JOIN catalog_category AS c1 
              ON cp.categoryid = c1.categoryid AND c1.category_level = 3
        LEFT JOIN catalog_category AS c2 
              ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
              AND c2.category_level = 2
        LEFT JOIN catalog_category AS c3 
              ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
              AND c3.category_level = 1
        ${whereClause}
        ORDER BY totalCost DESC`;
     
      // console.log('sql', db.format(sql, params))
      const [productReports] = await db.query(sql, params);

      const grandTotals = productReports.reduce(
        (totals, item) => {
          totals.totalQuantity += Number(item.quantity) || 0;
          totals.totalCost += Number(item.totalCost) || 0;
          return totals;
        },
        { totalQuantity: 0, totalCost: 0 }
      );

      

      return {
        productReports,
        grandTotals,
      };
    } catch (err) {
      console.error('purchaseReport error:', err.message);
      throw err;
    }
  }
}

export default purchaseReport;