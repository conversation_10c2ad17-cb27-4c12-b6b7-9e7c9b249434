import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderPurchaseReportPage,
    handlePurchaseReportApi,
    handlePurchaseReportExcel
} from './purchaseReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/purchaseReport', verifyToken, renderPurchaseReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/purchaseReport', verifyToken, handlePurchaseReportApi);

//FIXED: Corrected function name
router.get('/api/reports/purchaseReport/export', verifyToken, handlePurchaseReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/product-purchase-report', verifyToken, (req, res) => {
    res.render('purchase-report', {
        pageTitle: "Purchase Report",
        user: req.user
    });
});

export default router; // Export the configured router
