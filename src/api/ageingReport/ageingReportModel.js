import db from '../../config/db.js';

class ageingProduct {

  static async getAgeingReport(filters) {
    
    const { country, buyer, product, duration } = filters;
    const id = parseInt(country)
   
    const productCondition = product ? `spp.productid = ${db.escape(product)}` : '1';
    const buyerCondition = buyer ? `spr.buyer_id = ${db.escape(buyer)}` : '1';

    const baseQuery = `
      SELECT 
        spp.productid AS item_name,
        MAX(DATE(spp.order_date)) AS order_date,
        spi.inventory AS quantity,
        spi.cost,
        spr.title AS product_name,
        spr.sku AS product_code,
        spr.buyer_id AS buyer,
        sa.firstname AS buyer_name
      FROM 
        procurement_purchase_products spp
      JOIN procurement_purchase sp ON spp.purchase_id = sp.purchase_id
      JOIN catalog_product spr ON spp.productid = spr.productid
      JOIN catalog_product_inventory spi ON spr.productid = spi.productid 
      JOIN aauth_users sa ON spr.buyer_id = sa.id
      WHERE 
        spi.country_id = ${db.escape(id)}
        AND sp.supplierid <> 8
        AND ${productCondition}
        AND ${buyerCondition}
        AND spi.inventory > 0
        AND spr.productid > 594
      GROUP BY spp.productid
      ORDER BY spp.order_date DESC
    `;
    const [orderDates] = await db.query(baseQuery);
    const today = new Date();
    const range = duration.split('-');
    let startDate, endDate;
    if (duration === '12+') {
      startDate = new Date(today);
      startDate.setMonth(startDate.getMonth() - 12);
    } else {
      endDate = new Date(today);
      endDate.setMonth(endDate.getMonth() - parseInt(range[0]));
      startDate = new Date(today);
      startDate.setMonth(startDate.getMonth() - parseInt(range[1]));
    }

    const filtered = [];

    for (const productRow of orderDates) {
      const orderDate = new Date(productRow.order_date);
      const includeInRange = duration === '12+'
        ? orderDate < startDate
        : orderDate > startDate && orderDate < endDate;

      if (!includeInRange) {continue};

      // ====== Duration Days ======
      const diffTime = Math.abs(today - orderDate);
      productRow.duration_days = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      // ====== Latest Order Detail ======
      let latestOrderQuery = `
        SELECT 
          DATE(po.order_date) AS latest_order_date, 
          pi.inventory AS latest_qty, 
          o.orderid AS latest_orderid
        FROM oms_order_detail po
        LEFT JOIN oms_orders o ON po.orderid = o.orderid
        JOIN catalog_product_inventory pi ON po.productid = pi.productid
        WHERE po.productid = ? AND pi.country_id =${db.escape(id)} `;
      const queryParams = [productRow.item_name];
      if (country === 1) {
        latestOrderQuery += ' AND o.country_id = ?';
        queryParams.push(1);
      }

      latestOrderQuery += ' ORDER BY po.order_date DESC LIMIT 1';
      const [latestOrderRows] = await db.query(latestOrderQuery, queryParams);
      if (latestOrderRows.length) {
        Object.assign(productRow, latestOrderRows[0]);
      }

      // ====== Supplier Info ======
      const [supplierRows] = await db.query(
        `SELECT c.company_name AS sup_name
         FROM admin_supplier c
         LEFT JOIN procurement_purchase b ON c.supplierid = b.supplierid
         LEFT JOIN procurement_purchase_products a ON b.purchase_id = a.purchase_id
         WHERE a.productid = ? AND b.supplierid != 8 AND c.country_id = ${db.escape(id)}
         ORDER BY b.bill_date DESC LIMIT 1`,
        [productRow.item_name]
      );
      if (supplierRows.length) {
        productRow.supplier_name = supplierRows[0].sup_name;
      }

      // ====== Web Price ======
      const sku = productRow.product_code;
      if (sku) {
        const dbQuery =`SELECT pi.*, p.sku FROM catalog_product_inventory pi 
        JOIN catalog_product p ON pi.productid = p.productid
        WHERE p.sku = ?  AND pi.country_id = ${db.escape(id)} LIMIT 1 `
        const [webPriceRows] = await db.query(
         dbQuery,
          [sku]
        );
        productRow.web_price = webPriceRows.length ? parseFloat(webPriceRows[0].selling_price) : null;
      } else {
        productRow.web_price = null;
      }

      filtered.push(productRow);
    }

    const durationLabel = duration === '12+' ? 'More than 12 months' : `${range[0]} to ${range[1]} months`;

    return {
      duration: durationLabel,
      data: filtered,
      filters
    };
  }
  
  static async getConsolidateAgeingReport(filters = {}) {
    const { buyer } = filters;

    const [countries] = await db.query(`SELECT * FROM admin_country WHERE status = 1 AND country_id != 7`);
    const today = new Date();

    // Convert to timestamps for easier comparisons
    const todayTs = today.getTime();
    const firstMonthTs = new Date(today); firstMonthTs.setMonth(today.getMonth() - 1);
    const thirdMonthTs = new Date(today); thirdMonthTs.setMonth(today.getMonth() - 3);
    const sixthMonthTs = new Date(today); sixthMonthTs.setMonth(today.getMonth() - 6);
    const tenthYearTs = new Date(today); tenthYearTs.setFullYear(today.getFullYear() - 10);

    const buyerCondition = buyer ? `AND spr.buyer_id = ${db.escape(buyer)}` : '';
    const buyerConditionTrans = buyer ? `AND c.buyer_id = ${db.escape(buyer)}` : '';

    const resultByCountry = {};
    const totals = {
      firstMonthTotal: 0,
      secondMonthTotal: 0,
      sixthMonthTotal: 0,
      lastYearTotal: 0,
      currentStockTotal: 0,
      totalTransit: 0,
      totalNet: 0,
    };


    for (const country of countries) {
      const currencyValue = country.currency_value || 1;
      const storeId = country.store_id;
      const countryName = country.name;

      resultByCountry[countryName] = {
        firstMonth: 0,
        secondMonth: 0,
        sixthMonth: 0,
        lastYear: 0,
        currentStock: 0,
        trans: 0,
        trans_details: [],
      };

      // Purchase Product Aging Data
      const productQuery = `
        SELECT 
          spp.productid, MAX(DATE(spp.order_date)) AS order_date, spi.inventory, spi.cost,
          spr.title AS product_name, spr.sku AS product_code, spr.buyer_id, sa.firstname AS buyer_name
        FROM procurement_purchase_products spp
        JOIN procurement_purchase sp ON spp.purchase_id = sp.purchase_id
        JOIN catalog_product spr ON spp.productid = spr.productid
         JOIN catalog_product_inventory spi ON spr.productid = spi.productid
        JOIN aauth_users sa ON spr.buyer_id = sa.id
        WHERE sp.supplierid != 8 AND spi.inventory > 0 AND spr.productid > 594 ${buyerCondition}
        GROUP BY spp.productid
        ORDER BY spp.order_date DESC
      `;

      const [orderDetails] = await db.query(productQuery);

      for (const row of orderDetails) {
        const compareDate = new Date(row.order_date).getTime();
        const stockValue = row.quantity * row.cost * currencyValue;


        if (compareDate > firstMonthTs && compareDate < todayTs) {
          resultByCountry[countryName].firstMonth += stockValue;
        } else if (compareDate > thirdMonthTs && compareDate <= firstMonthTs) {
          resultByCountry[countryName].secondMonth += stockValue;
        } else if (compareDate > sixthMonthTs && compareDate <= thirdMonthTs) {
          resultByCountry[countryName].sixthMonth += stockValue;
        } else if (compareDate > tenthYearTs && compareDate <= sixthMonthTs) {
          resultByCountry[countryName].lastYear += stockValue;
        }

        if (compareDate > tenthYearTs && compareDate < todayTs) {
          resultByCountry[countryName].currentStock += stockValue;
        }
      }

      // Transit Stock Calculation
      const transitQuery = `
        SELECT 
          b.inventory, b.price, c.sku AS product_code, c.title AS name, d.currency_value AS transCurValue,
          d.name AS cname, a.bill_no, a.entry_date
        FROM procurement_stockhandover a
        INNER JOIN procurement_stockhandover_products b ON a.handover_id = b.handover_id
        LEFT JOIN catalog_product c ON c.productid = b.handover_id
        LEFT JOIN admin_country d ON a.transfer_from = d.store_id
        WHERE a.transfer_to = ? AND a.status = 'Pending' ${buyerConditionTrans}
      `;
      const [transitRows] = await db.query(transitQuery, [storeId]);

      for (const trans of transitRows) {
        const total = trans.qty * trans.price * (trans.transCurValue || 1);
        resultByCountry[countryName].trans += total;
      }

      // Accumulate totals
      totals.firstMonthTotal += resultByCountry[countryName].firstMonth;
      totals.secondMonthTotal += resultByCountry[countryName].secondMonth;
      totals.sixthMonthTotal += resultByCountry[countryName].sixthMonth;
      totals.lastYearTotal += resultByCountry[countryName].lastYear;
      totals.currentStockTotal += resultByCountry[countryName].currentStock;
      totals.totalTransit += resultByCountry[countryName].trans;
    }

    totals.totalNet = totals.currentStockTotal + totals.totalTransit;

    return {
      countries: resultByCountry,
      totals,
      filters: {
        buyer
      }
    };
  }
}

export default ageingProduct;
