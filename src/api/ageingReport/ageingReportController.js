import ageingProduct from './ageingReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderAgeingReportPage = (req, res) => {
    res.render('ageing-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleAgeingReportApi = async (req, res) => {
    try {
        const filters = req.query;
        // Ensure 'country' filter is provided
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country parameter is required." });
        }

        // Get filtered order data from model
        const data = await ageingProduct.getAgeingReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

//consolidate ageing report
export const renderConsolidateAgeingReportPage = (req, res) => {
    res.render('consolidate-ageing-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleConsolidateAgeingReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Get filtered order data from model
        const data = await ageingProduct.getConsolidateAgeingReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};




export const handleAgeingReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    const { data: ageingData, duration } = await ageingProduct.getAgeingReport(filters);

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Ageing Report');

    worksheet.columns = [
      { header: 'No', key: 'no', width: 5 },
      { header: 'Buyer', key: 'buyer_name', width: 20 },
      { header: 'Product', key: 'product_name', width: 40 },
      { header: 'Supplier', key: 'supplier_name', width: 30 },
      { header: 'SKU', key: 'product_code', width: 20 },
      { header: 'Purchase Date', key: 'order_date', width: 20 },
      { header: 'Order Date', key: 'latest_order_date', width: 20 },
      { header: 'Qty', key: 'quantity', width: 10 },
      { header: 'Cost', key: 'cost', width: 15, style: { numFmt: '#,##0.00' } },
      { header: 'Web Price', key: 'web_price', width: 15, style: { numFmt: '#,##0.00' } },
      { header: 'Total', key: 'total_amount', width: 15, style: { numFmt: '#,##0.00' } },
      { header: 'Duration Label', key: 'duration_label', width: 20 },
      { header: 'Duration (Days)', key: 'duration_days', width: 15 }
    ];

    worksheet.getRow(1).font = { bold: true };

    ageingData.forEach((product, index) => {
      const total = (product.quantity || 0) * (parseFloat(product.cost) || 0);
      worksheet.addRow({
        no: index + 1,
        buyer_name: product.buyer_name || '',
        product_name: product.product_name || '',
        supplier_name: product.supplier_name || '',
        product_code: product.product_code || '',
        order_date: product.order_date ? new Date(product.order_date).toLocaleDateString('en-GB') : '',
        latest_order_date: product.latest_order_date ? new Date(product.latest_order_date).toLocaleDateString('en-GB') : '',
        quantity: product.quantity || 0,
        cost: parseFloat(product.cost || 0),
        web_price: parseFloat(product.web_price || 0),
        total_amount: parseFloat(total.toFixed(2)),
        duration_label: duration,
        duration_days: product.duration_days || ''
      });
    });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="AgeingReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Excel Export Error:", error);
    res.status(500).send("Could not generate Excel file.");
  }
};


