import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderAgeingReportPage,
    handleAgeingReportApi,
    handleAgeingReportExcel,
    renderConsolidateAgeingReportPage,
    handleConsolidateAgeingReportApi
} from './ageingReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/ageingReport', verifyToken, renderAgeingReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/ageingReport', verifyToken, handleAgeingReportApi);

//FIXED: Corrected function name
router.get('/api/reports/ageingReport/export', verifyToken, handleAgeingReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/ageing-report', verifyToken, (req, res) => {
    res.render('ageing-report', {
        pageTitle: "Ageing Report",
        user: req.user
    });
});

// Order report EJS page
router.get('/reports/consolidateAgeingReport', verifyToken, renderConsolidateAgeingReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/consolidateAgeingReport', verifyToken, handleConsolidateAgeingReportApi);

// Order report EJS route (can be removed, since handled below)
router.get('/consolidate-ageing-report', verifyToken, (req, res) => {
    res.render('consolidate-ageing-report', {
        pageTitle: "Consolidate Ageing Report",
        user: req.user
    });
});

export default router; // Export the configured router
