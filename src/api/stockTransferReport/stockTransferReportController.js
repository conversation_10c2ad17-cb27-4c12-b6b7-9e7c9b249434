import StockTransferReport from './stockTransferReportModel.js';
import db from '../../config/db.js';
import exceljs from 'exceljs';

// ===============================================
// STOCK TRANSFER PROFIT/LOSS REPORT CONTROLLER
// ===============================================

// Render the Transfer Profit/Loss Report EJS page
export const renderStockTransferPage = (req, res) => {
    res.render('reports/stock-transfer-report', {
        user: req.user,
        pageTitle: "Transfer Report"
    });
};

// API: Fetch Transfer Profit/Loss Report data
export const handleStockTransferApi = async (req, res) => {
    try {
        const reportData = await StockTransferReport.getReport(req.query);
        res.json({ success: true, data: reportData });
    } catch (error) {
        console.error("API Error fetching stock transfer report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// API: Export Transfer Profit/Loss Report to Excel
export const handleStockTransferExcel = async (req, res) => {
    try {
        // 1) Fetch report data (array of bills as used by the UI)
        const reportData = await StockTransferReport.getReport(req.query);

        // 2) Country map (id -> name) for "From" / "To"
        const [countries] = await db.query(
            'SELECT country_id AS id, name AS text FROM admin_country WHERE status = 1'
        );
        const countryById = new Map((countries || []).map(c => [String(c.id), c.text]));

        // 3) RFC-4180 CSV escaping
        const esc = (val) => {
            if (val === null || val === undefined) return '';
            const s = String(val);
            // If contains comma, quote, or newline, wrap in quotes and double quotes inside
            return /[",\r\n]/.test(s) ? `"${s.replace(/"/g, '""')}"` : s;
        };

        // 4) Build CSV
        const lines = [];
        const today = new Date().toLocaleDateString('en-GB');

        // Optional: title/time
        lines.push(esc('Transfer Profit Report'));
        lines.push(`Date,${esc(today)}`);
        lines.push(''); // spacer

        // Grand totals accumulator
        const grandTotals = { qty: 0, price: 0, cost: 0, profit: 0 };

        // 5) Per-bill rows matching on-screen structure
        for (const bill of reportData) {
            const fromName = countryById.get(String(bill.transfer_from)) ?? String(bill.transfer_from);
            const toId = bill.transfer_to ?? bill._transfer_to;
            const toName = countryById.get(String(toId)) ?? String(toId);

            let transferMedia;
            switch (bill.transfer_media) {
                case "1":
                    transferMedia = "Road"
                    break;
                case "2":
                    transferMedia = "Air"
                    break;
                case "3":
                    transferMedia = "Sea"
                    break;
                default:
                    break;
            }

            // Banner row like UI: put whole banner in first column
            const banner = `From ${fromName} → To ${toName} VIA ${transferMedia} | Bill No. ${bill.bill_no} BY ${bill.logisticName} Billed on ${new Date(bill.bill_date).toLocaleDateString()}`;
            lines.push(esc(banner));

            // Table header
            lines.push(['Product Name', 'SKU', 'Quantity'].map(esc).join(','));

            // Product rows
            for (const p of bill.products) {
                lines.push([esc(p.productName), esc(p.sku), esc(p.qty)].join(','));
            }

            // Bill total row
            lines.push(['Total', '', esc(bill.totals.qty)].join(','));

            // Update grand totals
            grandTotals.qty += bill.totals.qty || 0;
            grandTotals.price += bill.totals.price || 0;
            grandTotals.cost += bill.totals.cost || 0;
            grandTotals.profit += bill.totals.profit || 0;

            lines.push(''); // spacer between bills
        }

        // 6) Grand totals block (same labels as UI footer)
        const grandGP = grandTotals.price > 0
            ? ((grandTotals.profit / grandTotals.price) * 100).toFixed(2)
            : '0.00';

        lines.push(''); // spacer
        lines.push('Grand Totals');
        lines.push(['Total Quantity', '', esc(grandTotals.qty)].join(','));
        lines.push(['Total Price', '', esc(grandTotals.price.toFixed(2))].join(','));
        lines.push(['Total Cost', '', esc(grandTotals.cost.toFixed(2))].join(','));
        lines.push(['Total Profit', '', esc(`${grandTotals.profit.toFixed(2)} AED`)].join(','));
        lines.push(['Total GP %', '', esc(`${grandGP}%`)].join(','));

        // 7) Stream CSV with correct headers
        const csv = lines.join('\r\n'); // CRLF per RFC 4180
        res.set({
            'Content-Type': 'text/csv; charset=utf-8',
            'Content-Disposition': `attachment; filename="StockTransferReport-${new Date().toISOString().slice(0, 10)}.csv"`,
        });
        res.status(200).send(csv);
    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).send('Could not generate CSV file.');
    }
};
