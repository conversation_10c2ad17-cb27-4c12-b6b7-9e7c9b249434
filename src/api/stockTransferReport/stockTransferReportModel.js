import db from '../../config/db.js';
import { getCategoryLineage } from '../../utils/commons.js';

class StockTransferReport {
    static async getReport(filters) {
        const { countryId, fromDate, toDate, store, status, transferMedia, transferFrom, transferTo} = filters;
        // if (!countryId) return [];

        // --- Build dynamic WHERE conditions ---
        let conditions = `
            al.list_type like "%logistic%"
            AND hanp.handover_id = han.handover_id
            AND prod.categoryid IS NOT NULL
            AND prod.categoryid != 0
        `;
        const params = [];

        if (fromDate && toDate) {
            conditions += " AND date(han.bill_date) BETWEEN ? AND ?";
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions += " AND date(han.bill_date) = ?";
            params.push(fromDate);
        }

        if (store) {
            conditions += " AND han.transfer_to = ?";
            params.push(store);
        }

        if (transferMedia) {
            conditions += " AND han.transfer_media = ?";
            params.push(transferMedia);
        }
        if (transferFrom) {
            conditions += " AND han.transfer_from = ?";
            params.push(Number(transferFrom));
        }
        if (transferTo) {
            conditions += " AND han.transfer_to = ?";
            params.push(Number(transferTo));
        }

        if (status) {
            conditions += " AND han.status = ?";
            params.push(status);
        }
        // --- Query to get grouped transfer data ---
        const sql = `
            SELECT 
            han.status,
            han.transfer_from,
            han.transfer_to,
            han.handover_id,
            han.bill_date,
            han.bill_no,
            han.transfer_media,
            han.logistic_id,
            al.list_lable as logisticName,
            prod.categoryid AS category_id,
            hanp.productid AS product_id,
            prod.title AS product_name,
            prod.sku AS sku,
            hanp.cost,
            hanp.price,
            SUM(hanp.inventory) AS total_quantity
            FROM procurement_stockhandover han
            INNER join admin_lists al ON han.logistic_id = al.list_value
            INNER JOIN procurement_stockhandover_products hanp ON han.handover_id = hanp.handover_id
            INNER JOIN catalog_product prod ON hanp.productid = prod.productid
            WHERE ${conditions}
            GROUP BY han.handover_id, prod.categoryid, hanp.productid, prod.title
            ORDER BY han.bill_date desc
        `;
        const [results] = await db.query(sql, params);
        // --- Group data by category with computed totals ---
        const reportData = {};

        await Promise.all(results.map(async (row) => {
            const bill_no = row.bill_no;
            if (!reportData[bill_no]) {
                reportData[bill_no] = {
                    handover_id: row.handover_id,
                    bill_no: bill_no,
                    transfer_from: row.transfer_from,
                    transfer_to: row.transfer_to,
                    transfer_media: row.transfer_media,
                    bill_date: row.bill_date,
                    logisticName: row.logisticName,
                    products: [],
                    logistic_id: row.logistic_id,
                    totals: { qty: 0, price: 0, cost: 0, profit: 0 }
                };
            }

            const totalQuantity = parseInt(row.total_quantity || 0, 10);
            const pricePerPc = parseFloat(row.price || 0);
            const costPerPc = parseFloat(row.cost || 0);

            const totalPrice = pricePerPc * totalQuantity;
            const totalCost = costPerPc * totalQuantity;
            const profit = totalPrice - totalCost;
            const gp = totalPrice > 0 ? (profit / totalPrice) * 100 : 0;

            reportData[bill_no].products.push({
                productName: row.product_name,
                sku: row.sku,
                qty: totalQuantity,
                totalPrice: totalPrice,
                pricePerPc: pricePerPc,
                totalCost: totalCost,
                costPerPc: costPerPc,
                profit: profit,
                gp: gp.toFixed(2)
            });

            reportData[bill_no].totals.qty += totalQuantity;
            reportData[bill_no].totals.price += totalPrice;
            reportData[bill_no].totals.cost += totalCost;
            reportData[bill_no].totals.profit += profit;
        }));

        Object.values(reportData).forEach(cat => {
            cat.totals.gp = cat.totals.price > 0
                ? ((cat.totals.profit / cat.totals.price) * 100).toFixed(2)
                : '0.00';
        });

        return Object.values(reportData);

    }
}

export default StockTransferReport;
