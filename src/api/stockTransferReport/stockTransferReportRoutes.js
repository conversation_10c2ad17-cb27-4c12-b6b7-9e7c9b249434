// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderStockTransferPage,
    handleStockTransferApi,
    handleStockTransferExcel
} from './stockTransferReportController.js';

const router = express.Router();

// ===============================================
// PAGE RENDER ROUTE
// ===============================================
// EJS Render: Load the main Stock Transfer Profit/Loss Report page
router.get('/stock-transfer-report', verifyToken, (req, res) => {
    res.render('stock-transfer-report', {
        pageTitle: "STOCK TRANSFER REPORT",
        user: req.user
    });
});

// ===============================================
// STOCK TRANSFER P/F REPORT ROUTES
// ===============================================

// Route: Render Stock Transfer PF report (server-side HTML render)
router.get('/reports/stock-transfer', verifyToken, renderStockTransferPage);

// API Route: Fetch Stock Transfer PF report data (JSON response)
router.get('/api/reports/stock-transfer', verifyToken, handleStockTransferApi);

// API Route: Export Stock Transfer PF report to Excel
router.get('/api/reports/stock-transfer/export', verifyToken, handleStockTransferExcel);

export default router;
