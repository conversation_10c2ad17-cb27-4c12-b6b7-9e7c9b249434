// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

// Import procurement delay report controllers
import {
    renderProcurementDelayReportPage,
    handleProcurementDelayReportApi,
    handleProcurementDelayReportExcel
} from '../procurementDelayReport/procurementDelayReportController.js';

const router = express.Router(); // Initialize router

// Render EJS page directly (if needed separately)
router.get('/procurement-delay-report', verifyToken, (req, res) => {
    res.render('procurement-delay-report', {
        pageTitle: "PROCUREMNET DELAY REPORT",
        user: req.user
    });
});

// ======================================================
// PROCUREMENT DELAY REPORT ROUTES
// ======================================================

// View route for procurement delay report page
router.get('/reports/procurement-delay', verifyToken, renderProcurementDelayReportPage);

// API route to fetch procurement delay report data
router.get('/api/reports/procurement-delay', verifyToken, handleProcurementDelayReportApi);

// API route to export procurement delay report as Excel
router.get('/api/reports/procurement-delay/export', verifyToken, handleProcurementDelayReportExcel);

export default router; // Export router
