import db from '../../config/db.js';

class ProcurementDelayReport {
    // Main method to generate procurement delay report
    static async getReport(filters) {
        const { countryId } = filters;
        
        // Select fields for main report query
        let selectFields = `ord.orderid AS id, ord.order_date, ord.order_ref_code ,ord.orderid, aut.id as buyer_id, aut.firstname as buyer_name, aut.lastname as buyer_name2, inv.inventory as cqty, odet.quantity AS orderqty, prod.productid as product_id`;

            let mainSql = `
                SELECT ${selectFields}
                FROM oms_orders ord
                INNER JOIN oms_order_detail odet ON odet.orderid = ord.orderid
                INNER JOIN catalog_product prod ON prod.productid = odet.productid
                INNER JOIN catalog_product_inventory inv 
                    ON inv.productid = prod.productid 
                AND inv.country_id = ord.country_id   -- getstock specific to country
                LEFT JOIN aauth_users aut ON aut.id = prod.buyer_id
                WHERE ord.order_statusid = 1
                AND inv.inventory = 0                 -- only orders pending because of no stock
            `;
        // Add condition only if countryId > 0
        if (countryId && countryId > 0) {
            mainSql += ` AND ord.country_id=${countryId}`;
        }

        // Group buyer-wise + order-wise
        mainSql += " GROUP BY ord.country_id, aut.id, ord.orderid ORDER BY ord.country_id, aut.id ASC";



        const [orderResults] = await db.query(mainSql);
        // return orderResults

        
        const today = new Date();
        // Aggregate report data by buyer
        const aggregatedData = orderResults.reduce((acc, order) => {
            //if (!order.buyer_id) return acc;
            // Initialize buyer group
            if (!acc[order.buyer_id]) {
                acc[order.buyer_id] = {
                    buyer_id: order.buyer_id,
                    buyer_name: (order.buyer_name || "") + (order.buyer_name2 ? " " + order.buyer_name2 : ""),
                    zeroToTwo: 0,
                    threeToFive: 0,
                    sixToTen: 0,
                    tenAbove: 0,
                    zeroToTwo_oids: [],
                    threeToFive_oids: [],
                    sixToTen_oids: [],
                    tenAbove_oids: []
                };
            }
            // Compute quantities and procurement need
            const transferQty = 0;
            const currentQty = (order.cqty || 0);
            const procurementNeeded = (order.orderqty || 0) - currentQty;

            // If procurement is needed, categorize by age
            if (procurementNeeded > 0) {
                const orderDate = new Date(order.order_date);
                const dateDiff = Math.round((today - orderDate) / (1000 * 60 * 60 * 24)); // Day difference
                const orderDetails = { id: order.id, orderid: order.order_ref_code, orderRefCode: order.order_ref_code };

                if (dateDiff <= 2) {
                    acc[order.buyer_id].zeroToTwo++;
                    acc[order.buyer_id].zeroToTwo_oids.push(orderDetails);
                } else if (dateDiff <= 5) {
                    acc[order.buyer_id].threeToFive++;
                    acc[order.buyer_id].threeToFive_oids.push(orderDetails);
                } else if (dateDiff <= 10) {
                    acc[order.buyer_id].sixToTen++;
                    acc[order.buyer_id].sixToTen_oids.push(orderDetails);
                } else {
                    acc[order.buyer_id].tenAbove++;
                    acc[order.buyer_id].tenAbove_oids.push(orderDetails);
                }
            }

            return acc;
        }, {});

        // Return report and country info
        return {
            reportData: Object.values(aggregatedData),
        };
    }
}

export default ProcurementDelayReport;
