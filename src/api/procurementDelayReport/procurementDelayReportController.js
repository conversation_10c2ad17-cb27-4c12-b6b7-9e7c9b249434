// src/controllers/procurementDelayReportController.js

import ProcurementDelayReport from '../procurementDelayReport/procurementDelayReportModel.js'; // Report model
import exceljs from 'exceljs'; // For Excel file generation

// Render Procurement Delay Report EJS page
export const renderProcurementDelayReportPage = (req, res) => {
    res.render('reports/procurement-delay-report', {
        user: req.user,
        pageTitle: "Procurement Delay Report"
    });
};

// API: Return report data in JSON format
export const handleProcurementDelayReportApi = async (req, res) => {
    try {
        const data = await ProcurementDelayReport.getReport(req.query); // Fetch filtered data
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching procurement delay report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// API: Generate and export Procurement Delay Report as Excel
export const handleProcurementDelayReportExcel = async (req, res) => {
    try {
        const { reportData, countryDetails } = await ProcurementDelayReport.getReport(req.query);
        const countryName = countryDetails?.name || 'Report';

        // Create Excel workbook and worksheet
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Procurement Delay');

        // Set main header/title row
        worksheet.mergeCells('A1:F1');
        const titleCell = worksheet.getCell('A1');
        titleCell.value = `Procurement Delay - ${countryName} - ${new Date().toLocaleDateString('en-GB')}`;
        titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
        titleCell.alignment = { horizontal: 'center' };
        titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };

        // Set column headers (Day ranges)
        worksheet.getRow(2).values = ['Buyer', '0-2 Days', '3-5 Days', '6-10 Days', 'More than 10 Days', 'Grand Total'];
        worksheet.getRow(2).font = { bold: true };
        worksheet.getRow(2).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFADD8E6' } }; // Light blue

        // Totals initialization
        let ztt_total = 0, ttf_total = 0, stt_total = 0, ta_total = 0, gtotal = 0;

        // Populate data rows from report
        reportData.forEach(details => {
            const bTotal = details.zeroToTwo + details.threeToFive + details.sixToTen + details.tenAbove;
            gtotal += bTotal;
            ztt_total += details.zeroToTwo;
            ttf_total += details.threeToFive;
            stt_total += details.sixToTen;
            ta_total += details.tenAbove;

            worksheet.addRow([
                details.buyer_name,
                details.zeroToTwo,
                details.threeToFive,
                details.sixToTen,
                details.tenAbove,
                bTotal
            ]).font = { bold: true };
        });

        // Add final Grand Total row with percentages
        worksheet.addRow([
            'Grand Total',
            `${ztt_total} - ${gtotal > 0 ? Math.round((ztt_total / gtotal) * 100) : 0}%`,
            `${ttf_total} - ${gtotal > 0 ? Math.round((ttf_total / gtotal) * 100) : 0}%`,
            `${stt_total} - ${gtotal > 0 ? Math.round((stt_total / gtotal) * 100) : 0}%`,
            `${ta_total} - ${gtotal > 0 ? Math.round((ta_total / gtotal) * 100) : 0}%`,
            gtotal
        ]).font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
        worksheet.lastRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } }; // Black bg

        // Set column width
        worksheet.columns.forEach(column => { column.width = 20; });

        // Set download response headers
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="ProcurementDelayReport-${countryName}-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        // Stream Excel file to client
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
