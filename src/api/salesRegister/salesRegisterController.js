import salesRegisterModel from './salesRegisterModel.js';
import exceljs from 'exceljs';

const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d)) return ''; // handle invalid date
    const year = d.getFullYear();
    const month = d.getMonth() + 1;
    const day = d.getDate();
    return `${year}-${month}-${day}`;
};

// Render the Sales Register report page
export const renderSalesRegisterPage = (req, res) => {
    res.render('sales-register', { user: req.user });
};

// API handler to fetch report data
export const handleSalesRegisterApi = async (req, res) => {
    try {
        const filters = req.query;

        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required parameter." });
        }

        const { data, summary } = await salesRegisterModel.getSalesRegister(filters);
        res.json({ success: true, data: { data, summary,filters } });
    } catch (error) {
        console.error("API Error fetching Sales Register:", error);
        res.status(500).json({ success: false, error: "An error occurred while fetching the report." });
    }
};

// Handle Excel export for the report
export const handleSalesRegisterExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { data } = await salesRegisterModel.getSalesRegister(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Sales Register');

        worksheet.columns = [
            { header: 'S.No', key: 's_no', width: 8 },
            { header: 'Region', key: 'country_name', width: 15 },
            { header: 'Currency', key: 'country_currency', width: 15 },
            { header: 'Order ID', key: 'order_id', width: 15 },
            { header: 'Order Date', key: 'date', width: 15 },
            { header: 'Delivery Date', key: 'delivery_date', width: 15 },
            { header: 'Category', key: 'category', width: 20 },
            { header: 'Sub Category', key: 'sub_category', width: 20 },
            { header: 'Product', key: 'product_name', width: 40 },
            { header: 'SKU', key: 'sku', width: 20 },
            { header: 'QTY', key: 'qty', width: 10 },
            { header: 'Selling Price (Before VAT)', key: 'selling_price_before_vat', width: 22, style: { numFmt: '#,##0.00' } },
            { header: 'VAT on Selling Price', key: 'selling_vat', width: 20, style: { numFmt: '#,##0.00' } },
            { header: 'Selling Price', key: 'selling_price', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Payment Mode', key: 'payment_mode', width: 15 },
            { header: 'Payment Type', key: 'payment_type', width: 15 },
            { header: 'Order From', key: 'orderfrom', width: 15 },
            { header: 'Sender', key: 'sender_name', width: 15 },
        ];

        worksheet.getRow(1).font = { bold: true };

        data.forEach((item, index) => {
            const selling_price = parseFloat(item.selling_price) || 0;
            const cost_price = parseFloat(item.cost_price) || 0;
            //const product_margin_value = selling_price - cost_price;
            //const product_margin_percentage = cost_price !== 0
                //? ((selling_price - cost_price) / cost_price) * 100
                //: 0;

            const rowData = {
                s_no: index + 1,
                country_name: item.country_name,
                country_currency: item.country_currency,
                order_id: item.order_id,
                date: formatDate(item.date),
                delivery_date: formatDate(item.delivery_date),
                category: item.category,
                sub_category: item.sub_category,
                product_name: item.product_name,
                sku: item.sku,
                qty: parseFloat(item.qty),
                selling_price_before_vat: parseFloat(item.selling_price_before_vat),
                selling_vat: parseFloat(item.selling_vat),
                selling_price: parseFloat(item.selling_price),
                payment_mode: item.payment_mode ==1 ? 'CreditCard' : 'CashOnDelivery',
                payment_type: item.payment_type,
                orderfrom: item.orderfrom,
                sender_name: item.sender_name,
                //product_margin_value: parseFloat(product_margin_value.toFixed(2)),
               // product_margin_percentage: parseFloat(product_margin_percentage.toFixed(2)),
            };

            worksheet.addRow(rowData);
        });

        // Set headers for Excel download
        res.setHeader('Content-Type', 'application/vnd.ms-excel');
        res.setHeader('Content-Disposition', `attachment; filename="Sales-Register-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};