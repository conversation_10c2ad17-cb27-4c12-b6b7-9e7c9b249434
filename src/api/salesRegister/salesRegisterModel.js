import db from '../../config/db.js';




class SalesRegisterModel {
    static async getSalesRegister(filters) {
        const { country, fromDate, toDate } = filters;

        let whereClauses = [];
        let queryParams = [];

        const countryId = parseInt(country, 10);

        if (countryId) {
            whereClauses.push(`oo.country_id = ?`);
            queryParams.push(countryId);
        }

        // ✅ Use earliest delivered date from order_history instead of order_date
        if (fromDate && toDate) {
            whereClauses.push(`DATE(first_delivery.min_delivery) BETWEEN DATE(?) AND DATE(?)`);
            queryParams.push(fromDate, toDate);
        } else if (fromDate) {
            whereClauses.push(`DATE(first_delivery.min_delivery) = DATE(?)`);
            queryParams.push(fromDate);
        }

        // Always Delivered
        whereClauses.push(`oo.order_statusid = 6`);

        // Exclude test customers
        whereClauses.push(`
            (
                oo.customer_details IS NULL
                OR NOT JSON_VALID(oo.customer_details)
                OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
            )
        `);

        const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

        const query = `
           SELECT 
                ac.name,
                ac.currency,
                oo.order_ref_code AS order_id,
                DATE(oo.order_date) AS order_date,
                DATE(first_delivery.min_delivery) AS date,   
                c3.category_name AS category,
                c2.category_name AS sub_category,
                cp.title AS product_name,
                odd.product_sku AS sku,
                odd.cost AS cost_price,
                (odd.selling_price*odd.quantity) AS selling_price,
                oo.tax_amount,
                oo.total_amount,
                odd.quantity AS qty,
                pm.name AS payment_type,
                fo.list_lable AS orderfrom,
                a_s.firstname AS sender_name,
                ac.name AS country_name,
                ac.currency AS country_currency,
                oo.payment_methodid as payment_mode,
                -- Sales Price Breakdown
               ROUND(
                CASE 
                    WHEN oo.country_id IN (1, 2) THEN ((odd.selling_price - (odd.selling_price * 5 / 105)) * odd.quantity)
                    WHEN oo.country_id = 6 THEN ((odd.selling_price - (odd.selling_price * 10 / 110)) * odd.quantity)
                    ELSE (odd.selling_price * odd.quantity)
                END, 2
            ) AS selling_price_before_vat,

            ROUND(
                CASE 
                    WHEN oo.country_id IN (1, 2) THEN ((odd.selling_price * odd.quantity) * 5.0 / 105.0)
                    WHEN oo.country_id = 6 THEN ((odd.selling_price * odd.quantity) * 10.0 / 110.0)
                    ELSE 0 
                END, 2
            ) AS selling_vat
            FROM oms_orders oo
            JOIN (
                SELECT orderid, MIN(statusdate) AS min_delivery
                FROM oms_order_history
                WHERE order_statusid = 6
                AND order_status_type = 'order_status'
                GROUP BY orderid
            ) first_delivery ON oo.orderid = first_delivery.orderid

            -- Earliest dispatch date per order
            LEFT JOIN (
                SELECT h.orderid, MIN(h.statusdate) AS dispatch_date
                FROM oms_order_history h
                WHERE h.order_status_type = 'order_status'
                AND h.order_statusid = 3
                GROUP BY h.orderid
            ) d ON oo.orderid = d.orderid

            LEFT JOIN admin_country ac ON oo.country_id = ac.country_id
            LEFT JOIN oms_order_detail odd ON oo.orderid = odd.orderid
            LEFT JOIN catalog_product cp ON odd.productid = cp.productid
            LEFT JOIN catalog_category AS c1 
              ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category AS c2 
              ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
              AND c2.category_level = 2
            LEFT JOIN catalog_category AS c3 
              ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
              AND c3.category_level = 1
            LEFT JOIN aauth_users AS a_s ON oo.driver_id = a_s.id
            LEFT JOIN oms_payment_method pm ON oo.gway_paymentmethod = pm.id
            LEFT JOIN admin_lists fo ON fo.list_value = oo.order_from_id AND fo.list_type = 'order_from'
            WHERE oo.country_id = ?
            AND DATE(first_delivery.min_delivery) BETWEEN DATE(?) AND DATE(?)
            AND oo.order_statusid = 6
            AND (
                    oo.customer_details IS NULL
                    OR NOT JSON_VALID(oo.customer_details)
                    OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
                )
            ORDER BY order_id DESC`; 

        console.log(db.format(query, queryParams));  

        const [data] = await db.query(query, queryParams);

        // ✅ Summary also based on earliest delivery date
        let summaryWhere = [];
        let summaryParams = [];

        if (countryId) {
            summaryWhere.push(`oo.country_id = ?`);
            summaryParams.push(countryId);
        }

        if (filters.fromDate && filters.toDate) {
            summaryWhere.push(`DATE(first_delivery.min_delivery) BETWEEN DATE(?) AND DATE(?)`);
            summaryParams.push(filters.fromDate, filters.toDate);
        }

        const summaryWhereSql = summaryWhere.length > 0 ? `WHERE ${summaryWhere.join(' AND ')}` : '';

        const summaryQuery = `
            SELECT
                COUNT(DISTINCT oo.orderid) AS totalorder,
                SUM(oo.shipping_charges)  AS shippingcharge,
                SUM(oo.processing_fee) AS pfee,
                SUM(oo.discount_amount) AS discount,
                SUM(donation_fee) AS donation_fee,
                SUM(balance_amount) AS balance_amount,
                SUM(credit_amount) AS credit_amount,
                SUM(tax_amount) AS tax_amount,
                SUM(total_amount) AS grand_total
            FROM oms_orders oo
            JOIN (
                SELECT orderid, MIN(statusdate) AS min_delivery
                FROM oms_order_history
                WHERE order_statusid = 6
                AND order_status_type = 'order_status'
                GROUP BY orderid
            ) first_delivery ON oo.orderid = first_delivery.orderid
            ${summaryWhereSql}
            AND oo.order_statusid = 6
            AND (
                oo.customer_details IS NULL
                OR NOT JSON_VALID(oo.customer_details)
                OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
            )
        `;

        const [summaryResult] = await db.query(summaryQuery, summaryParams);
        const summary = summaryResult[0] || {};

        return { data, summary };
    }
}


export default SalesRegisterModel;