import express from 'express';
import { renderSalesRegisterPage, handleSalesRegisterApi, handleSalesRegisterExcel } from './salesRegisterController.js';
import { verifyToken } from '../../middleware/authMiddleware.js';

const router = express.Router();


router.get('/sales-register-report', verifyToken, (req, res) => {
    res.render('sales-register', {
        pageTitle: "Sales Register Report",
        user: req.user
    });
});


router.use(verifyToken);

router.get('/sales-register', renderSalesRegisterPage);
router.get('/api/reports/sales-register', handleSalesRegisterApi);
router.get('/api/reports/sales-register/export', handleSalesRegisterExcel);

export default router;