import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderCancelProductReportPage,
    handleCancelProductReportApi,
    handleCancelProductReportExcel
} from './cancelProductReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/cancelProductReport', verifyToken, renderCancelProductReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/cancelProductReport', verifyToken, handleCancelProductReportApi);

//FIXED: Corrected function name
router.get('/api/reports/cancelProductReport/export', verifyToken, handleCancelProductReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/cancel-product-report', verifyToken, (req, res) => {
    res.render('cancel-product-report', {
        pageTitle: "Cancel Product Report",
        user: req.user
    });
});

export default router; // Export the configured router
