import cancelProductOrder from './cancelProductReportModel.js';
import exceljs from 'exceljs';
import { setCancelReportPaymentType } from '../../utils/commons.js'

// Render the order report page with user info
export const renderCancelProductReportPage = (req, res) => {
    res.render('cancel-product-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleCancelProductReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Get filtered order data from model
        const data = await cancelProductOrder.getCancelProductOrderReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

// API handler to export order report data as Excel file
export const handleCancelProductReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Get order data and totals from model
        const { orders, totals } = await cancelProductOrder.getCancelProductOrderReport(filters);

        // Create a new workbook and worksheet
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Cancelled Products');

        // Define Excel columns
        worksheet.columns = [
        { header: 'No', key: 'no', width: 5 },
        { header: 'Country', key: 'country_name', width: 20 },
        { header: 'Order ID', key: 'orderid', width: 20 },
        { header: 'SKU', key: 'sku', width: 15 },
        { header: 'Product Name', key: 'product_name', width: 30 },
         { header: 'Category', key: 'catname', width: 30 },
        { header: 'Sub Category', key: 'subcatname', width: 15 },
        { header: 'Quantity', key: 'qty', width: 15 },
        { header: 'Pay Mode', key: 'payment_mode', width: 15 },
        { header: 'Pay Type', key: 'payment_type', width: 15 },
        { header: 'Confirmed By', key: 'confirmed_by', width: 15 },
        { header: 'Confirmed Date', key: 'order_date', width: 15 },
        { header: 'Order From', key: 'orderfrom', width: 15 },
        { header: 'Dispatch Date', key: 'despatch_date', width: 15 },
        { header: 'Cancel By', key: 'cancelled_by', width: 15 },
         { header: 'Cancel Date', key: 'cancel_date', width: 15 },
        { header: 'Cancel Reason', key: 'cancel_reason', width: 15 },
        { header: 'Remarks', key: 'remarks', width: 25 },
        { header: 'Delivery Agent', key: 'delivery_agent', width: 20 },
        { header: 'Buyer', key: 'buyer', width: 20 },
        { header: 'Duration', key: 'durdays', width: 15 },
        { header: 'Amount', key: 'display_amount', width: 15, style: { numFmt: '#,##0.00' } },
    ];
        worksheet.getRow(1).font = { bold: true }; // Bold header

        // Add each order as a row
        orders.forEach((order, index) => {
            if(order.payment_type) {
                order.payment_type = setCancelReportPaymentType(order.payment_type);
            }
            worksheet.addRow({
                ...order,
                no: index + 1,
                display_amount: parseFloat(order.display_amount || 0)
            });
        });

        worksheet.addRow([]);

        const summaries = [
            ['Total Orders', orders.length || 0],
            [
                'Total Amount', totals.totalAmount
                // 'Total Amount', orders.length ? orders.reduce((acc, order) => {
                //     return acc + (parseFloat(order.price || 0) * parseInt(order.qty || 0));
                // }, 0) : 0
            ]
        ];

        // 3) For each summary, merge columns A–N for the label, and put the number under “Amount” (column O)
        summaries.forEach(([label, value], i) => {
            const row = worksheet.addRow([]);
            const r = row.number;

            // merge A–N for the text label
            worksheet.mergeCells(`A${r}:N${r}`);
            const labelCell = worksheet.getCell(`A${r}`);
            labelCell.value = label;
            labelCell.font = { bold: true };

            // put the numeric value in column O (which is your 'display_amount' column)
            const valueCell = worksheet.getCell(`O${r}`);
            valueCell.value = parseFloat(value).toFixed(2);
            valueCell.numFmt = '#,##0.00';
            if (label === 'Grand Total') {
                valueCell.font = { bold: true };
            }
        });

        // Set response headers for Excel file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="ProductCancel-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        // Write workbook to response and end 
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
