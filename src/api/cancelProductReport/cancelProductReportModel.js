import db from '../../config/db.js';
import { handleCancelStatus } from '../cancelReport/cancelReportModel.js'

class CancelProductOrder {
    static async getCancelProductOrderReport(filters) {
        const conditions = [];
        const { cancel_status } = filters;

        // --- Fixed base conditions ---
        conditions.push(`oo.order_statusid in (${handleCancelStatus(cancel_status)})`);
        conditions.push(`ooh.order_status_type = "order_status"`);
        conditions.push(`(
            JSON_VALID(oo.customer_details) = 0
            OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
        )`);

        // --- Dynamic filters ---
        if (filters.country) conditions.push(`oo.country_id = ${db.escape(filters.country)}`);
        if (filters.countryName) conditions.push(`ac.name = ${db.escape(filters.countryName)}`);
        if (filters.paymode) conditions.push(`oo.payment_method = ${db.escape(filters.paymode)}`);
        if (filters.paymentGatewayType) conditions.push(`oo.payment_methodid = ${db.escape(filters.paymentGatewayType)}`);
        if (filters.staffName) conditions.push(`oo.staff_id = ${db.escape(filters.staffName)}`);
        if (filters.area) conditions.push(`oo.area_id = ${db.escape(filters.area)}`);
        if (filters.sender) conditions.push(`oo.driver_id = ${db.escape(filters.sender)}`);
        if (filters.fromDate && filters.toDate) {
            conditions.push(`DATE(ooh.statusdate) BETWEEN ${db.escape(filters.fromDate.split(" ")[0])} AND ${db.escape(filters.toDate.split(" ")[0])}`);
        }

        const whereClause = conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';

        // --- Data Query ---
        const dataQuery = `
SELECT
    oo.order_ref_code AS orderid,
    oo.country_id,
    ac.name AS country_name,
    oo.staff_id,
    oo.processing_fee,
    oo.order_status,
    oo.notes AS remarks,
    oo.gway_transaction_id AS fort_id,
    MAX(CASE WHEN ooh.order_statusid = 3 AND ooh.order_status_type = "order_status" THEN ooh.statusdate END) AS despatch_date,
    oo.charge,
    MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) AND ooh.order_status_type = "order_status" THEN ooh.status END) AS cancel_status,
    MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) AND ooh.order_status_type = "order_status" THEN ooh.statusdate END) AS cancel_date,
    oo.delivery_date,
    oo.total_amount,
    cp.sku AS sku,
    cp.title AS product_name,
    oo.order_date,
    oo.orderfrom,
    driver.firstname AS delivery_agent,
    oo.payment_methodid AS payment_type,
    DATEDIFF(
        MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) AND ooh.order_status_type = "order_status" THEN ooh.statusdate END),
        oo.order_date
    ) AS durdays,
    oo.total_amount AS display_amount,
    oo.payment_method AS payment_mode,
    ood.quantity AS qty,
    ood.selling_price AS price,
    buyer_user.firstname AS buyer,
    MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) AND ooh.order_status_type = "order_status" THEN ooh.staff_comments END) AS cancel_reason,
    MAX(CASE WHEN oo.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN confirmed_user.firstname END) AS confirmed_by,
    MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN cancel_user.firstname END) AS cancelled_by
FROM oms_orders AS oo
LEFT JOIN oms_order_history AS ooh ON oo.orderid = ooh.orderid
LEFT JOIN aauth_users AS driver ON oo.driver_id = driver.id
LEFT JOIN aauth_users AS confirmed_user ON oo.staff_id = confirmed_user.id
LEFT JOIN aauth_users AS cancel_user ON oo.staff_id = cancel_user.id
LEFT JOIN admin_country AS ac ON oo.country_id = ac.country_id
LEFT JOIN oms_order_detail AS ood ON oo.orderid = ood.orderid
LEFT JOIN catalog_product AS cp ON ood.productid = cp.productid
LEFT JOIN aauth_users AS buyer_user ON cp.buyer_id = buyer_user.id
${whereClause}
GROUP BY oo.orderid, sku
ORDER BY oo.order_date ASC
        `;

        // --- Summary Query ---
        const summaryQuery = `
SELECT
    COUNT(*) AS totalOrders,
    SUM(oo.sub_total) AS rawTotalAmount,
    SUM(oo.tax_amount) AS vat,
    SUM(oo.shipping_charges) AS shippingCharge,
    SUM(oo.processing_fee) AS processingFees,
    SUM(oo.discount_amount) AS discountAmount
FROM oms_orders oo
JOIN (
    SELECT DISTINCT orderid
    FROM oms_order_history
    WHERE order_statusid IN (${handleCancelStatus(cancel_status)})
    AND order_status_type = 'order_status'
    ${filters.fromDate && filters.toDate ? `AND DATE(statusdate) BETWEEN ${db.escape(filters.fromDate.split(" ")[0])} AND ${db.escape(filters.toDate.split(" ")[0])}` : ''}
) oh ON oo.orderid = oh.orderid
WHERE (
    JSON_VALID(oo.customer_details) = 0
    OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
)
${filters.country ? `AND oo.country_id = ${db.escape(filters.country)}` : ''}
        `;

        // console.log("📝 Data Query:\n", dataQuery);
        // console.log("\n📝 Summary Query:\n", summaryQuery);

        // --- Execute queries ---
        const [orders] = await db.query(dataQuery);
        const [summaryRows] = await db.query(summaryQuery);
        // console.log(db.format(dataQuery))
        // console.log(orders[0])

        const totals = summaryRows[0] || {};

        if (totals.rawTotalAmount) {
            const rawTotal = parseFloat(totals.rawTotalAmount);
            const fees = parseFloat(totals.processingFees || 0);
            const totalVat = parseFloat(totals.vat || 0);
            const totalDiscount = parseFloat(totals.discountAmount || 0);
            const totalShipping = parseFloat(totals.shippingCharge || 0);

            totals.totalAmount = rawTotal - (fees + totalVat + totalDiscount);
            totals.grandTotal = rawTotal + totalShipping;
        }

        return { orders, totals };
    }
}

export default CancelProductOrder;
