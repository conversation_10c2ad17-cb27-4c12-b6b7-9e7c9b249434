import MonthlyOrderReport from '../monthlyOrderReport/monthlyOrderReportModel.js';
import exceljs from 'exceljs';

export const renderMonthlyOrderReportPage = (req, res) => {
    res.render('monthly-order-report', { user: req.user, pageTitle: "Monthly Order Report" });
};

export const handleMonthlyOrderReportApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.country || !filters.month || !filters.year) {
            return res.status(400).json({ success: false, error: "Country, Year and Month are required filters." });
        }
        const data = await MonthlyOrderReport.getReport(filters);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching monthly order report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

export const handleMonthlyOrderReportExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { reportData, totals } = await MonthlyOrderReport.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('MonthlyOrderReport');

        worksheet.columns = [
            { header: 'No', key: 'sl_no', width: 8 },
            { header: 'Order Date', key: 'order_date', width: 20 },
            { header: 'Order Count', key: 'orders', width: 15, style: { numFmt: '0' } },
            { header: 'Value', key: 'value', width: 18, style: { numFmt: '#,##0.00' } },
            { header: 'Shipping Charge', key: 'shippingcharge', width: 18, style: { numFmt: '#,##0.00' } },
            { header: 'Total Value', key: 'total_value', width: 18, style: { numFmt: '#,##0.00' } }
        ];
        worksheet.getRow(1).font = { bold: true };

        reportData.forEach((row, index) => {
            worksheet.addRow({
                sl_no: index + 1,
                order_date: new Date(row.order_date).toLocaleDateString('en-GB'),
                orders: row.orders,
                value: row.value,
                shippingcharge: row.shippingcharge,
                total_value: parseFloat(row.value) + parseFloat(row.shippingcharge)
            });
        });

        const startRow = reportData.length + 3;
        worksheet.getCell(`E${startRow}`).value = 'Total Orders:'; worksheet.getCell(`F${startRow}`).value = totals.totalOrders;
        worksheet.getCell(`E${startRow+1}`).value = 'Total Amount:'; worksheet.getCell(`F${startRow+1}`).value = totals.totalAmount;
        worksheet.getCell(`E${startRow+2}`).value = 'Shipping Charge:'; worksheet.getCell(`F${startRow+2}`).value = totals.shippingCharge;
        worksheet.getCell(`E${startRow+3}`).value = 'Grand Total:'; worksheet.getCell(`F${startRow+3}`).value = totals.grandTotal;
        worksheet.getCell(`F${startRow+1}`).numFmt = '#,##0.00';
        worksheet.getCell(`F${startRow+2}`).numFmt = '#,##0.00';
        worksheet.getCell(`F${startRow+3}`).numFmt = '#,##0.00';
        worksheet.getCell(`E${startRow+3}`).font = { bold: true };
        worksheet.getCell(`F${startRow+3}`).font = { bold: true };

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="MonthlyOrderReport-${filters.year}-${filters.month}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};