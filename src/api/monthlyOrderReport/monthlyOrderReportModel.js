import db from '../../config/db.js';

class MonthlyOrderReport {

    static async getReport(filters) {
        const { country, month, year, sender, paymode, status, orderfrom, staff, voucher } = filters;

        const hasSender = !!sender;
        const TBL_PREFIX = hasSender ? 'a.' : '';

        const statusFieldMap = {
            'Pending': [1], 'Invoiced': [3], 'Despatch': [5],
            'Delivered': [6], 'Cancelled': [7, 11], 'Return': [8]
        };

        let conditions = [`${TBL_PREFIX}country_id = ?`];
        const params = [country];

        if (month) { conditions.push(`MONTH(${TBL_PREFIX}order_date) = ?`); params.push(month); }
        if (year) { conditions.push(`YEAR(${TBL_PREFIX}order_date) = ?`); params.push(year); }
        if (paymode) { conditions.push(`${TBL_PREFIX}payment_method = ?`); params.push(paymode); }
        if (orderfrom) { conditions.push(`${TBL_PREFIX}orderfrom = ?`); params.push(orderfrom); }
        if (staff) { conditions.push(`${TBL_PREFIX}staff_id = ?`); params.push(staff); }

        if (voucher === '0') {
            conditions.push(`(${TBL_PREFIX}discount_amount = '' OR ${TBL_PREFIX}discount_amount = 0 OR ${TBL_PREFIX}discount_amount IS NULL)`);
        } else if (voucher === '1') {
            conditions.push(`${TBL_PREFIX}discount_amount > 0`);
        }

        if (status) {
            const statuses = statusFieldMap[status]
            conditions.push(`${TBL_PREFIX}order_statusid IN (${statuses})`);
        }

        let sql;
        if (hasSender) {
            //sender_id => driver_id
            conditions.push('a.driver_id = ?');
            params.push(sender);

            sql = `
                SELECT 
                a.order_ref_code,
                    DATE(${TBL_PREFIX}order_date) as order_date,
                    SUM(${TBL_PREFIX}total_amount) as value,
                    SUM(${TBL_PREFIX}shipping_charges) as shippingcharge,
                    COUNT(${TBL_PREFIX}orderid) as orders
                    FROM oms_orders a
                    INNER JOIN oms_order_history oh
                    ON oh.orderid = a.orderid
                    AND a.order_statusid = oh.order_statusid
                    AND oh.order_status_type = 'order_status'
                    AND oh.statusdate = (
                    SELECT MAX(statusdate)
                    FROM oms_order_history oh2
                    WHERE oh2.orderid = a.orderid
                    AND oh2.order_statusid = a.order_statusid
                    AND oh2.order_status_type = 'order_status'
                    )
                WHERE ${conditions.join(' AND ')}
                GROUP BY DATE(${TBL_PREFIX}order_date)
                ORDER BY DATE(${TBL_PREFIX}order_date) ASC
            `;
        } else {
            sql = `
                SELECT 
                order_ref_code,
                    DATE(order_date) as order_date,
                    SUM(total_amount) as value,
                    SUM(shipping_charges) as shippingcharge,
                    COUNT(orderid) as orders
                FROM oms_orders
                WHERE ${conditions.join(' AND ')}
                GROUP BY DATE(order_date)
                ORDER BY DATE(order_date) ASC
            `;
        }

        const [reportData] = await db.query(sql, params);

        const totals = reportData.reduce((acc, row) => {
            acc.totalOrders += parseInt(row.orders, 10) || 0;
            acc.totalAmount += parseFloat(row.value) || 0;
            acc.shippingCharge += parseFloat(row.shippingcharge) || 0;
            return acc;
        }, { totalOrders: 0, totalAmount: 0, shippingCharge: 0 });

        totals.grandTotal = totals.totalAmount + totals.shippingCharge;

        return { reportData, totals };
    }
}

export default MonthlyOrderReport;