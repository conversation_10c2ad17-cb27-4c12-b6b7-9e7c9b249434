import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderMonthlyOrderReportPage,
    handleMonthlyOrderReportApi,
    handleMonthlyOrderReportExcel
} from '../monthlyOrderReport/monthlyOrderReportController.js';

const router = express.Router();

router.get('/monthly-order-report', verifyToken, renderMonthlyOrderReportPage);
router.get('/api/reports/monthly-order', verifyToken, handleMonthlyOrderReportApi);
router.get('/api/reports/monthly-order/export', verifyToken, handleMonthlyOrderReportExcel);

export default router;