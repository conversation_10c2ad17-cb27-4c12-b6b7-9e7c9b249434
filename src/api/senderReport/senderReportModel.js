import db from '../../config/db.js';
import {orderFromId,orderStatus} from '../../utils/commons.js'



class senderOrder {
    static async getSenderDetails(filters) {
        const {
            status, fromDate, toDate, sender, paymode,paymentGatewayType,
            emirate, area, orderFrom, country = 1
        } = filters;

        const conditions = [];
        const params = [];

        conditions.push('so.country_id = ?');
        params.push(country);

        if (sender) {
            conditions.push('so.driver_id = ?');
            params.push(sender);
        }

        //if (area) {
           // conditions.push('so.area_id = ?');
           // params.push(area);
       // }

        if (paymode) {
            if(paymode == 1){
                 conditions.push('so.payment_methodid = 1');
            }else{
                 conditions.push('so.payment_methodid != 1');  
            }
        }

        if(paymentGatewayType){
            conditions.push('so.gway_paymentmethod = ?');
            params.push(paymentGatewayType);

        }

        if (orderFrom && orderFrom.length > 0) {
            const placeholders = Array.isArray(orderFrom) ? orderFrom.map(() => '?').join(',') : '?';
            conditions.push(`so.orderfrom IN (${placeholders})`);
            params.push(...(Array.isArray(orderFrom) ? orderFrom : [orderFrom]));
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // Determine status filter for earliest-status join
        let statusCondition = '';
        let statusConditionP = '';
        if (status) {
            if (status == 5) {
                // Pending
                statusConditionP = 'AND so.order_statusid = 5';
            } else {
                statusCondition = ` AND h.order_statusid = ${status}`;
            }
        } else {
            // Default dispatch
            statusCondition = 'AND h.order_statusid = 3'; 
        }

        // Build query
       const query = `WITH safe_orders AS (
                SELECT
                    oo.*,
                    CASE 
                        WHEN JSON_VALID(oo.shippingaddress) THEN 
                            CAST(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.shippingaddress, '$.emirate'))) AS UNSIGNED)
                        ELSE NULL
                    END AS parsed_emirate_id,
                    CASE 
                        WHEN JSON_VALID(oo.shippingaddress) THEN 
                            CAST(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.shippingaddress, '$.area'))) AS UNSIGNED)
                        ELSE NULL
                    END AS parsed_area_id,
                    CASE 
                        WHEN JSON_VALID(oo.customer_details) THEN 
                            LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))))
                        ELSE NULL
                    END AS customer_name
                FROM oms_orders oo
            )
            SELECT
                ac.name AS country_name,
                so.order_ref_code AS order_id,
                au.firstname AS sender_name,
                cb.firstname AS confirmed_by,
                so.customer_details,
                so.customer_contact AS contact,
                so.customer_contact AS sender_contact,
                so.order_date,
                so.shipping_charges AS charge,
                so.total_amount AS calculated_amount,
                so.payment_methodid,
                so.order_statusid AS statusid,
                so.orderfrom,
                so.order_from_id,
                so.gway_transaction_id AS fort_id,
                d.dispatch_date,
                del.delivery_date,
                so.processing_fee,
                so.donation_fee,
                so.discount_amount,
                so.shippingaddress,
                ae.emirate AS emirate_name,
                ar.name AS area_name
            FROM safe_orders so
            LEFT JOIN aauth_users au ON so.driver_id = au.id
            LEFT JOIN aauth_users cb ON so.staff_id = cb.id
            LEFT JOIN admin_country ac ON so.country_id = ac.country_id

            LEFT JOIN admin_country_emirates ae 
                ON ae.emirateid = so.parsed_emirate_id 
                AND ae.country_id = so.country_id

            LEFT JOIN admin_country_area ar 
                ON ar.area_id = so.parsed_area_id

            JOIN (
                SELECT h.orderid, MIN(h.statusdate) AS first_status_date
                FROM oms_order_history h
                WHERE h.order_status_type = 'order_status'
                ${statusCondition}
                GROUP BY h.orderid
            ) first_status ON so.orderid = first_status.orderid

            LEFT JOIN (
                SELECT h.orderid, MIN(h.statusdate) AS dispatch_date
                FROM oms_order_history h
                WHERE h.order_status_type = 'order_status'
                AND h.order_statusid = 3
                GROUP BY h.orderid
            ) d ON so.orderid = d.orderid

            LEFT JOIN (
                SELECT h.orderid, MIN(h.statusdate) AS delivery_date
                FROM oms_order_history h
                WHERE h.order_status_type = 'order_status'
                AND h.order_statusid = 6
                GROUP BY h.orderid
            ) del ON so.orderid = del.orderid

            ${whereClause} ${statusConditionP}
            AND DATE(first_status.first_status_date) BETWEEN DATE(?) AND DATE(?)
            AND (
                so.customer_details IS NULL
                OR NOT JSON_VALID(so.customer_details)
                OR so.customer_name NOT LIKE '%test%'
            )
            ORDER BY so.order_date ASC
            `;

        // Push date filter params
        params.push(fromDate, toDate);

        // console.log(db.format(query)); 

     

        try {
            let partialAmount = 0;

            const [rows] = await db.execute(query, params);

            const transformedRows = rows.map(row => ({
                ...row,
                order_from: orderFromId[row.order_from_id], // utility mapping
                status:  orderStatus[row.statusid], // utility mapping
                partialAmount: partialAmount
            }));

            return transformedRows;
        } catch (error) {
            console.error("Database query failed in getSenderDetails:", error);
            return [];
        }
    }
}



export default senderOrder;