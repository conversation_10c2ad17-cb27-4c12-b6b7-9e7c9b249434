import senderOrder from './senderReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderSenderReportPage = (req, res) => {
    res.render('sender-report', { user: req.user });
};

export const handleSenderReportApi = async (req, res) => {
  try {
    const filters = req.query;

    if (!filters.country) {
      return res.status(400).json({ success: false, error: "Country parameter is required." });
    }

    const delivery_details = await senderOrder.getSenderDetails(filters);

    if (!Array.isArray(delivery_details)) {
      return res.status(200).json({
        success: true,
        data: {
          orders: [],
          totals: {},
          count: 0,
          filters
        }
      });
    }

    const enriched = delivery_details.map((od) => {
      const splitStatus = od.split_id != 0 ? 'Splitted' : 'Regular';
      return {
        ...od,
        splitStatus
      };
    });

    // Optional: compute totals here if needed
    const totals = {
      totalOrders: enriched.length,
      totalAmount: enriched.reduce((acc, od) => acc + (parseFloat(od.totalamount) || 0), 0),
      shippingCharge: enriched.reduce((acc, od) => acc + (parseFloat(od.charge) || 0), 0),
      processingFees: enriched.reduce((acc, od) => acc + (parseFloat(od.processing_fee) || 0), 0),
      vat: enriched.reduce((acc, od) => acc + (parseFloat(od.vat) || 0), 0),
      discountAmount: enriched.reduce((acc, od) => acc + (parseFloat(od.discount) || 0), 0),
      grandTotal: enriched.reduce((acc, od) => acc + (parseFloat(od.display_amount) || 0), 0)
    };

    res.json({
      success: true,
      data: {
        orders: enriched,
        totals,
        count: enriched.length,
        filters
      }
    });

  } catch (error) {
    console.error("API Error fetching order report:", error);
    res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
  }
};




// API handler to export order report data as Excel file
export const handleSenderReportExcelOld = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch sender details using getSenderDetails
        const orders = await senderOrder.getSenderDetails(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Sender Report');

        worksheet.columns = [
            { header: 'No', key: 'no', width: 5 },
            { header: 'Order Id', key: 'order_id', width: 15 },
            { header: 'Customer Name', key: 'customer_name', width: 25 },
            { header: 'Order From', key: 'orderfrom', width: 15 },
            { header: 'Emirates', key: 'emirate_name', width: 15 },
            { header: 'Area', key: 'area_name', width: 20 },
            { header: 'Order Date', key: 'order_date', width: 20 },
            { header: 'Sent Date', key: 'dispatch_date', width: 20 },
            { header: 'Amount', key: 'calculated_amount', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Delivery Date', key: 'delivery_date', width: 20 },
            { header: 'Delivery Status', key: 'status', width: 15 },
            { header: 'Delivery Days', key: 'durdays', width: 15 },
            { header: 'Return Days', key: 'return_days', width: 15 },
            { header: 'Sender Name', key: 'sender_name', width: 20 },
            { header: 'Paymode', key: 'payment_mode', width: 15 },
            { header: 'Payment Type', key: 'payment_method_name', width: 20 },
            { header: 'Staff Name', key: 'confirmed_by', width: 20 },
            { header: 'Delivery Charge', key: 'charge', width: 15, style: { numFmt: '#,##0.00' } }
        ];

        worksheet.getRow(1).font = { bold: true };

        orders.forEach((order, index) => {
            worksheet.addRow({
                no: index + 1,
                order_id: order.order_id,
                customer_name: order.customer_name || '',
                orderfrom: order.orderfrom || '',
                emirate_name: order.emirate_name || '',
                area_name: order.area_name || '',
                order_date: order.order_date || '',
                dispatch_date: order.dispatch_date || '',
                calculated_amount: parseFloat(order.calculated_amount || 0),
                delivery_date: order.delivery_date || '',
                status: order.status || '',
                durdays: order.durdays || '',
                after_despatch: order.after_despatch || '',
                return_days: order.return_days || '',
                sender_name: order.sender_name || '',
                payment_mode: order.payment_mode || '',
                payment_method_name: order.payment_method_name || '',
                confirmed_by: order.confirmed_by || '',
                charge: parseFloat(order.charge || 0)
            });
        });

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="SenderReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};

export const handleSenderReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch sender details using getSenderDetails
        const orders = await senderOrder.getSenderDetails(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Sender Report');

        worksheet.columns = [
            { header: 'No', key: 'no', width: 5 },
            { header: 'Order Id', key: 'order_id', width: 15 },
            { header: 'Customer Name', key: 'customer_name', width: 25 },
            { header: 'Order From', key: 'orderfrom', width: 15 },
            { header: 'Emirates', key: 'emirate_name', width: 15 },
            { header: 'Area', key: 'area_name', width: 20 },
            { header: 'Order Date', key: 'order_date', width: 20 },
            { header: 'Sent Date', key: 'dispatch_date', width: 20 },
            { header: 'Amount', key: 'calculated_amount', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Delivery Date', key: 'delivery_date', width: 20 },
            { header: 'Delivery Status', key: 'status', width: 15 },
            { header: 'Delivery Days', key: 'durdays', width: 15 },
            { header: 'Return Days', key: 'return_days', width: 15 },
            { header: 'Sender Name', key: 'sender_name', width: 20 },
            { header: 'Paymode', key: 'payment_mode', width: 15 },
            { header: 'Transcation Id', key: 'fort_id', width: 20 },
            { header: 'Staff Name', key: 'confirmed_by', width: 20 },
            { header: 'Delivery Charge', key: 'charge', width: 15, style: { numFmt: '#,##0.00' } }
        ];

        worksheet.getRow(1).font = { bold: true };

        orders.forEach((order, index) => {
            let customerName = "";
            let emirateName = "";
            let areaName = "";

            // ✅ Parse customer_details safely
            if (order.customer_details) {
                try {
                    const details = JSON.parse(order.customer_details);
                    customerName = details.name || "";
                    emirateName = details.emirate_name || "";
                    areaName = details.area_name || "";
                } catch (e) {
                    console.warn(`Invalid JSON for order ${order.order_id}:`, order.customer_details);
                }
            }

            worksheet.addRow({
                no: index + 1,
                order_id: order.order_id,
                customer_name: customerName,
                orderfrom: order.order_from || '',
                emirate_name: order.emirate_name,
                area_name: order.area_name,
                order_date: order.order_date || '',
                dispatch_date: order.dispatch_date || '',
                calculated_amount: parseFloat(order.calculated_amount || 0), 
                delivery_date: order.delivery_date || '',
                status: order.status || '',
                durdays: order.durdays || '',
                after_despatch: order.after_despatch || '',
                return_days: order.return_days || '',
                sender_name: order.sender_name || '',
                payment_mode: order.payment_methodid == 1 ? 'CreditCardPayments' : 'CashOnDelivery',
                fort_id: order.fort_id,
                confirmed_by: order.confirmed_by || '',
                charge: parseFloat(order.charge || 0)
            });
        });

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="SenderReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};

