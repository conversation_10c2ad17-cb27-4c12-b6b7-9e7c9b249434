import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderSenderReportPage,
    handleSenderReportApi,
    handleSenderReportExcel
} from './senderReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/senderReport', verifyToken, renderSenderReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/senderReport', verifyToken, handleSenderReportApi);

//FIXED: Corrected function name
router.get('/api/reports/senderReport/export', verifyToken, handleSenderReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/sender-report', verifyToken, (req, res) => {
    res.render('sender-report', {
        pageTitle: "Sender Report",
        user: req.user
    });
});

export default router; // Export the configured router