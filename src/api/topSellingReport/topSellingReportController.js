// src/controllers/topSellingReportController.js

import TopSellingReport from '../topSellingReport/topSellingReportModel.js';

// ===============================================
// PAGE RENDER FUNCTION
// ===============================================
// Render EJS page: Top Selling Pending Products
export const renderTopSellingPage = (req, res) => {
    res.render('reports/top-selling-report', { user: req.user, pageTitle: "Top Selling Pending Products" });
};

// ===============================================
// API HANDLER: Fetch Top Selling Pending Products
// ===============================================
export const handleTopSellingApi = async (req, res) => {
    try {
        const data = await TopSellingReport.getReport(req.query);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching top selling report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// ===============================================
// API HANDLER: Get Product Suggestions
// ===============================================
export const getSuggestions = async (req, res) => {
    try {
        const suggestions = await TopSellingReport.getSuggestions(req.query);
        res.json({ success: true, suggestions });
    } catch (error) {
        console.error("API Error fetching suggestions:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// ===============================================
// API HANDLER: Add a New Suggestion
// ===============================================
export const addSuggestion = async (req, res) => {
    try {
        await TopSellingReport.addSuggestion({ ...req.body, post_by: req.user.id });
        const suggestions = await TopSellingReport.getSuggestions(req.body);
        res.json({ success: true, suggestions });
    } catch (error) {
        console.error("API Error adding suggestion:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// ===============================================
// API HANDLER: Delete Suggestion by ID
// ===============================================
export const deleteSuggestion = async (req, res) => {
    try {
        await TopSellingReport.deleteSuggestion(req.body.id);
        const suggestions = await TopSellingReport.getSuggestions(req.body);
        res.json({ success: true, suggestions });
    } catch (error) {
        console.error("API Error deleting suggestion:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};
