import db from '../../config/db.js';

class TopSellingReport {

    static async getReport(filters) {
        const { buyer, product: productSkusFilter } = filters;

        // --- 1. Build product query dynamically ---
        const conditions = ["cp.type_id = 1"];
        const queryParams = [];

        if (productSkusFilter?.length) {
            conditions.push(`cp.sku IN (?)`);
            queryParams.push(productSkusFilter);
        }
        if (buyer) {
            conditions.push(`cp.buyer_id = ?`);
            queryParams.push(buyer);
        }
        const limitClause = (!buyer && !productSkusFilter?.length) ? "LIMIT 100" : "";

        const baseProductQuery = `
            SELECT 
                cp.productid AS id, 
                cp.sku AS product_code, 
                cp.title AS name, 
                CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS buyer 
            FROM ourshopee.catalog_product cp 
            LEFT JOIN ourshopee.aauth_users au ON cp.buyer_id = au.id 
            WHERE ${conditions.join(" AND ")} 
            ${limitClause}
        `; //report_staus = 1 AND

        const [products] = await db.query(baseProductQuery, queryParams);

        if (!products.length) return { reportData: [], countries: [] };

        const productSkus = products.map(p => p.product_code);
        const productIds = products.map(p => p.id);

        // --- 2. Fetch related datasets in parallel ---
        const [[countries], [allTransferDetails]] = await Promise.all([
            db.query(`SELECT * FROM admin_country WHERE status = 1 ORDER BY country_id ASC`),
            db.query(`
                SELECT psp.productid AS product_id, SUM(psp.inventory) AS ontransfer , psp.total
                FROM procurement_stockhandover ps
                JOIN procurement_stockhandover_products psp ON ps.handover_id = psp.handover_id
                WHERE psp.productid IN (?) AND ps.status = 'Pending'
                GROUP BY psp.productid
            `, [productIds])
        ]); // ps.store_name as store_id, , ps.store_name

        // --- 3. Build UNION query for stock data ---
        const stockUnionQueries = countries.map(() => `
            (SELECT cp.sku AS product_code, cpi.inventory AS quantity, cpi.cost, 
                    cpi.selling_price AS price, ? AS country_id
             FROM catalog_product cp
             LEFT JOIN catalog_product_inventory cpi ON cp.productid = cpi.productid
             WHERE cp.sku IN (?))
        `);

        const stockUnionParams = countries.flatMap(c => [c.id, productSkus]);

        const [allStockDetails] = await db.query(stockUnionQueries.join(' UNION ALL '), stockUnionParams);

        // --- 4. Create fast lookup maps ---
        const stockDataMap = new Map(
            allStockDetails.map(d => [`${d.product_code}-${d.country_id}`, d])
        );
        const transferDataMap = new Map(
            allTransferDetails.map(d => [d.product_id, d.total])
        );

        // --- 5. Build final report ---
        const reportData = products.map(product => {
            const productData = {
                sku: product.product_code,
                name: product.name,
                buyer: product.buyer || 'N/A',
                countries: {}
            };

            for (const country of countries) {
                const stockDetails = stockDataMap.get(`${product.product_code}-${country.id}`);
                const price = stockDetails?.price ? Number(stockDetails.price) : 0;
                const cost = stockDetails?.cost ? Number(stockDetails.cost) : 0;

                productData.countries[country.id] = {
                    cqty: stockDetails?.quantity || 0,
                    cost,
                    price,
                    profit: price - cost,
                    tqty: transferDataMap.get(product.id) || 0,
                    currency_value: country.currency_value,
                };
            }

            return productData;
        });

        return { reportData, countries };
    }

    // --- Suggestion Methods ---
    static async getSuggestions(filters) {
        let sql = `
            SELECT css.id, css.product_title, css.post_date, 
                   CONCAT(bau.firstname, ' ', COALESCE(bau.lastname, '')) AS buyer_name,
                   CONCAT(pau.firstname, ' ', COALESCE(pau.lastname, '')) AS post_by 
            FROM crm_sku_suggestion css
            LEFT JOIN aauth_users bau ON css.buyer = bau.id 
            LEFT JOIN aauth_users pau ON css.post_by = pau.id
        `;
        const params = [];

        if (filters.buyer) {
            sql += ` WHERE css.buyer = ?`;
            params.push(filters.buyer);
        }

        const [suggestions] = await db.query(sql, params);
        return suggestions;
    }

    static async addSuggestion({ sug_buyer, sug_title, post_by }) {
        await db.query(`
            INSERT INTO crm_sku_suggestion (product_title, buyer, post_by, post_date)
            VALUES (?, ?, ?, NOW())
        `, [sug_title, sug_buyer, post_by]);
    }

    static async deleteSuggestion(id) {
        await db.query(`DELETE FROM crm_sku_suggestion WHERE id = ?`, [id]);
    }
}

export default TopSellingReport;
