// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderTopSellingPage,
    handleTopSellingApi,
    getSuggestions,
    addSuggestion,
    deleteSuggestion
} from '../topSellingReport/topSellingReportController.js';

const router = express.Router();

// ===============================================
// EJS PAGE ROUTE: Top Selling Pending Products
// ===============================================
router.get('/top-selling-pending-products', verifyToken, (req, res) => {
    res.render('top-selling-report', {
        pageTitle: "TOP SELLING PENDING PRODUCTS REPORT",
        user: req.user
    });
});

// ===============================================
// TOP SELLING PENDING PRODUCTS REPORT ROUTES
// ===============================================
router.get('/reports/top-selling-pending', verifyToken, renderTopSellingPage);             // Render Top Selling Report Page
router.get('/api/reports/top-selling-pending', verifyToken, handleTopSellingApi);         // API: Fetch Top Selling Report Data

// ===============================================
// SUGGESTIONS ROUTES
// ===============================================
router.get('/api/reports/suggestions', verifyToken, getSuggestions);                      // API: Fetch Product Suggestions
router.post('/api/reports/suggestions/add', verifyToken, addSuggestion);                  // API: Add Suggestion
router.post('/api/reports/suggestions/delete', verifyToken, deleteSuggestion);            // API: Delete Suggestion

export default router;
