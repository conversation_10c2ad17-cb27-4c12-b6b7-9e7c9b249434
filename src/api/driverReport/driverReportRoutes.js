import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderDriverReportPage,
    handleDriverReportApi
} from './driverReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/driverReport', verifyToken, renderDriverReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/driverReport', verifyToken, handleDriverReportApi);


// Order report EJS route (can be removed, since handled below)
router.get('/driver-report', verifyToken, (req, res) => {
    res.render('driver-report', {
        pageTitle: "Driver Report",
        user: req.user
    });
});



export default router; // Export the configured router
