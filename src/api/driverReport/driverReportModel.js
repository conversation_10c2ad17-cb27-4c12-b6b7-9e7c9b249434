import db from '../../config/db.js';

class driverReport {

  static async getDriverReport(filters = {}) {
    try {
      const { fromDate, toDate, country_id } = filters;
      const params = [];
      const whereClauses = [];

      if (!fromDate || !toDate) {
        throw new Error("fromDate and toDate are required filters.");
      }

      // Single window based on history date for all metrics
      const startDateString = `${fromDate}`;
      const endDateString = `${toDate}`;

      // History window: DATE(h.statusdate) BETWEEN DATE(?) AND DATE(?)
      params.push(startDateString, endDateString);

      if (country_id) {
        whereClauses.push("ci.country_id = ?");
        params.push(country_id);
      }
      const whereCondition = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      const reportQuery = `
      WITH country_info AS (
        SELECT ac.country_id, ac.name AS country_name, ac.currency_value, ac.web_flag_png
        FROM admin_country ac
        WHERE ac.status = 1
      ),
      -- Base: only qualifying statuses inside the requested h.statusdate window, with JSON filter
      filtered_h AS (
        SELECT
          o.country_id, o.orderid, o.total_amount, o.shipping_charges,
          IFNULL(o.credit_amount, 0) AS credit_amount, o.payment_methodid,
          h.order_statusid, h.order_status_type, h.status, h.statusdate
        FROM oms_order_history h
        JOIN oms_orders o ON h.orderid = o.orderid
        WHERE h.order_statusid IN (1, 2, 6, 7, 11)
          AND (
            (h.order_statusid = 1 AND h.order_status_type = 'shipping_status')
            OR (h.order_statusid = 2 AND h.order_status_type = 'shipping_status')
            OR (h.order_statusid = 6 AND h.order_status_type = 'order_status')
            OR (h.order_statusid = 7 AND h.order_status_type = 'order_status')
            OR (h.order_statusid = 11 AND h.order_status_type = 'order_status')
          )
          AND DATE(h.statusdate) BETWEEN DATE(?) AND DATE(?)
          AND (
            JSON_VALID(o.customer_details) = 0
            OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(o.customer_details, '$.name')))) NOT LIKE '%test%'
          )
      ),
      -- First occurrence per order per status category (dedup inside window)
      first_status AS (
        SELECT
          country_id, orderid, order_statusid, order_status_type, status,
          MIN(statusdate) AS first_status_date
        FROM filtered_h
        GROUP BY country_id, orderid, order_statusid, order_status_type, status
      ),
      -- Distinct orders seen in the window, with one monetary row per order
      orders_dedup AS (
        SELECT
          fh.country_id,
          fh.orderid,
          fh.payment_methodid,
          (fh.total_amount + fh.credit_amount) AS order_total
        FROM (
          SELECT DISTINCT orderid FROM filtered_h
        ) w
        JOIN filtered_h fh ON fh.orderid = w.orderid
        GROUP BY fh.country_id, fh.orderid, fh.payment_methodid, fh.total_amount, fh.shipping_charges, fh.credit_amount
      ),
      -- "ordered" metrics driven by the same history window, deduped per order
      ordered AS (
        SELECT
          country_id,
          COUNT(*) AS order_count,
          SUM(order_total) AS ordertotal,
          SUM(CASE WHEN payment_methodid = 2 THEN order_total ELSE 0 END) AS order_cash,
          SUM(CASE WHEN payment_methodid != 2 THEN order_total ELSE 0 END) AS order_card,
          SUM(CASE WHEN payment_methodid = 2 THEN 1 ELSE 0 END) AS ordercash_count,
          SUM(CASE WHEN payment_methodid != 2 THEN 1 ELSE 0 END) AS ordercard_count
        FROM orders_dedup
        GROUP BY country_id
      ),
      -- Attach amounts once per order to first-status rows
      status_with_amounts AS (
        SELECT
          fs.country_id,
          fs.orderid,
          fs.order_statusid,
          fs.order_status_type,
          fs.status,
          fs.first_status_date,
          od.order_total AS total_amt,
          CASE WHEN od.payment_methodid = 2 THEN od.order_total ELSE 0 END AS total_cash,
          CASE WHEN od.payment_methodid != 2 THEN od.order_total ELSE 0 END AS total_card,
          CASE WHEN od.payment_methodid = 2 THEN 1 ELSE 0 END AS cash_count,
          CASE WHEN od.payment_methodid != 2 THEN 1 ELSE 0 END AS card_count,
          1 AS total_count
        FROM first_status fs
        JOIN orders_dedup od ON od.orderid = fs.orderid
      )
      SELECT
        ci.country_name,
        ci.country_id,
        ci.currency_value,
        ci.web_flag_png,

        IFNULL(o.order_count, 0) AS order_count,
        IFNULL(o.ordertotal, 0) AS ordertotal,
        IFNULL(o.order_cash, 0) AS order_cash,
        IFNULL(o.order_card, 0) AS order_card,
        IFNULL(o.ordercash_count, 0) AS ordercash_count,
        IFNULL(o.ordercard_count, 0) AS ordercard_count,

        -- In Queue (Dispatch): statusid 1, shipping_status
        IFNULL(SUM(CASE WHEN swa.order_statusid = 1 AND swa.order_status_type = 'shipping_status' THEN swa.total_count ELSE 0 END), 0) AS inq_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 1 AND swa.order_status_type = 'shipping_status' THEN swa.total_amt ELSE 0 END), 0) AS inqtotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 1 AND swa.order_status_type = 'shipping_status' THEN swa.total_cash ELSE 0 END), 0) AS inq_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 1 AND swa.order_status_type = 'shipping_status' THEN swa.total_card ELSE 0 END), 0) AS inq_card,

        -- In Hand (Driver Accepted): statusid 2, shipping_status
        IFNULL(SUM(CASE WHEN swa.order_statusid = 2 AND swa.order_status_type = 'shipping_status' THEN swa.total_count ELSE 0 END), 0) AS inhand_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 2 AND swa.order_status_type = 'shipping_status' THEN swa.total_amt ELSE 0 END), 0) AS inhandtotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 2 AND swa.order_status_type = 'shipping_status' THEN swa.total_cash ELSE 0 END), 0) AS inhand_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 2 AND swa.order_status_type = 'shipping_status' THEN swa.total_card ELSE 0 END), 0) AS inhand_card,

        -- Delivered: statusid 6, order_status
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_count ELSE 0 END), 0) AS done_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_amt ELSE 0 END), 0) AS donetotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_cash ELSE 0 END), 0) AS done_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_card ELSE 0 END), 0) AS done_card,

        -- Legacy delivered mirrors
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_count ELSE 0 END), 0) AS del_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_amt ELSE 0 END), 0) AS deltotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_cash ELSE 0 END), 0) AS del_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 6 AND swa.order_status_type = 'order_status' THEN swa.total_card ELSE 0 END), 0) AS del_card,

        -- Cancel Before Dispatch -> return_*
        IFNULL(SUM(CASE WHEN swa.order_statusid = 7 AND swa.order_status_type = 'order_status' THEN swa.total_count ELSE 0 END), 0) AS return_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 7 AND swa.order_status_type = 'order_status' THEN swa.total_amt   ELSE 0 END), 0) AS returntotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 7 AND swa.order_status_type = 'order_status' THEN swa.total_cash ELSE 0 END), 0) AS return_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 7 AND swa.order_status_type = 'order_status' THEN swa.total_card ELSE 0 END), 0) AS return_card,

        -- Cancel After Dispatch -> cancel_*
        IFNULL(SUM(CASE WHEN swa.order_statusid = 11 AND swa.order_status_type = 'order_status' THEN swa.total_count ELSE 0 END), 0) AS cancel_count,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 11 AND swa.order_status_type = 'order_status' THEN swa.total_amt   ELSE 0 END), 0) AS canceltotal,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 11 AND swa.order_status_type = 'order_status' THEN swa.total_cash ELSE 0 END), 0) AS cancel_cash,
        IFNULL(SUM(CASE WHEN swa.order_statusid = 11 AND swa.order_status_type = 'order_status' THEN swa.total_card ELSE 0 END), 0) AS cancel_card

      FROM country_info ci
      LEFT JOIN ordered o              ON ci.country_id = o.country_id
      LEFT JOIN status_with_amounts swa ON ci.country_id = swa.country_id
      ${whereCondition}
      GROUP BY ci.country_id, ci.country_name, ci.currency_value, ci.web_flag_png,
               o.order_count, o.ordertotal, o.order_cash, o.order_card, o.ordercash_count, o.ordercard_count
      ORDER BY ci.country_id;
    `;


      const [rows] = await db.query(reportQuery, params);

      const ordersData = rows.reduce((acc, row) => {
        const cvalue = parseFloat(row.currency_value) > 0 ? parseFloat(row.currency_value) : 1;
        
        const doneData = {
            count: Number(row.done_count),
            total: Number(row.donetotal),
            cash: Number(row.done_cash),
            card: Number(row.done_card),
        };

        acc[row.country_name] = {
          flag: row.web_flag_png,
          cid: row.country_id,
          cvalue: cvalue,
          
          order_count: Number(row.order_count),
          ordertotal: Number(row.ordertotal),
          order_cash: Number(row.order_cash),
          order_card: Number(row.order_card),
          ordercash_count: Number(row.ordercash_count),
          ordercard_count: Number(row.ordercard_count),

          Inq_count: Number(row.inq_count),
          Inqtotal: Number(row.inqtotal),
          Inq_cash: Number(row.inq_cash),
          Inq_card: Number(row.inq_card),
          Inqcash_count: Number(row.inqcash_count),
          Inqcard_count: Number(row.inqcard_count),

          Inhand_count: Number(row.inhand_count),
          Inhandtotal: Number(row.inhandtotal),
          Inhand_cash: Number(row.inhand_cash),
          Inhand_card: Number(row.inhand_card),

          del_count: doneData.count,
          deltotal: doneData.total,
          del_cash: doneData.cash,
          del_card: doneData.card,

          done_count: doneData.count,
          donetotal: doneData.total,
          done_cash: doneData.cash,
          done_card: doneData.card,

          return_count: Number(row.return_count),
          returntotal: Number(row.returntotal),
          return_cash: Number(row.return_cash),
          return_card: Number(row.return_card),
          
          cancel_count: Number(row.cancel_count),
          canceltotal: Number(row.canceltotal),
          cancel_cash: Number(row.cancel_cash),
          cancel_card: Number(row.cancel_card),
        };
        return acc;
      }, {});
      
      ordersData.filters = filters;

      return ordersData;

    } catch (error) {
      console.error('Error in getDriverReport:', error.message);
      throw error;
    }
  }
}

export default driverReport;