import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderPurchaseReportPage,
    handlePurchaseReportApi,
    handlePurchaseReportExcel,
    handlePurchaseReportInvoiceApi,
    handleReturnReportInvoiceApi
} from './purchaseReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/productPurchaseReport', verifyToken, renderPurchaseReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/productPurchaseReport', verifyToken, handlePurchaseReportApi);
router.get('/api/reports/product-purchase-invoice/:id', verifyToken, handlePurchaseReportInvoiceApi);
router.get('/api/reports/product-return-invoice/:id', verifyToken, handleReturnReportInvoiceApi);

//FIXED: Corrected function name
router.get('/api/reports/productPurchaseReport/export', verifyToken, handlePurchaseReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/purchase-report', verifyToken, (req, res) => {
    res.render('product-purchase-report', {
        pageTitle: "Product Purchase Report",
        user: req.user
    });
});


router.get('/product-purchase-invoice', verifyToken, (req, res) => {
    res.render('product-purchase-report-invoice', {
        pageTitle: "Product Purchase Invoice",
        user: req.user
    });
});

router.get('/product-return-invoice', verifyToken, (req, res) => {
    res.render('product-return-report-invoice', {
        pageTitle: "Product Purchase Invoice",
        user: req.user
    });
});
export default router; // Export the configured router
