import purchaseReport from './purchaseReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderPurchaseReportPage = (req, res) => {
    res.render('profit-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handlePurchaseReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Ensure 'country' filter is provided
        // if (!filters.country) {
        //     return res.status(400).json({ success: false, error: "Country parameter is required." });
        // }

        // Get filtered order data from model
        const data = await purchaseReport.getPurchaseReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

export const handleReturnReportInvoiceApi = async (req, res) => {
    try {
        const id = req.params.id;

        // Ensure 'country' filter is provided
        // if (!filters.country) {
        //     return res.status(400).json({ success: false, error: "Country parameter is required." });
        // }

        // Get filtered order data from model
        const data = await purchaseReport.getReturnReportInvoice(id);
        res.json({ success: true, data: { ...data, id } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};

// API handler to export order report data as Excel fil

export const handlePurchaseReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    const { productReports = [], productReturnReports = [] } = await purchaseReport.getPurchaseReport(filters);

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Purchase Report');

    // Define the columns for the report
    worksheet.columns = [
      { header: 'Supplier Name', key: 'supplier_name', width: 40 },
      { header: 'Bill Date & No', key: 'bill_date', width: 30 },
      { header: 'Sub Total', key: 'subtotal', width: 10 },
      { header: 'Round Off', key: 'roundof', width: 15 },
      { header: 'VAT', key: 'vat', width: 15 },
      { header: 'DiscountTotal', key: 'discount', width: 15 },
    ];

    worksheet.getRow(1).font = { bold: true };

    // Add data rows
    let totalSubTotal = 0;
    let totalRoundof = 0;
    let totalVat = 0;
    let totalDiscount = 0;
    let grandTotals = 0;

    productReports.forEach((item, index) => {
      // const total = parseFloat(item.price * item.quantity).toFixed(2);
       totalSubTotal += parseFloat(item.subtotal);
       totalRoundof += parseFloat(item.roundof);
       totalVat += parseFloat(item.vat);
       grandTotals += parseFloat(item.total);
       totalDiscount += parseFloat(item.discount);
       
      worksheet.addRow({
        supplier_name: item.supplier_name,
        bill_date: item.bill_date,
        subtotal: item.subtotal,
        roundof: item.roundof,
        vat: item.vat,
        discount: item.discount,
        
      });
      
    });

    let returnVat = 0;
    let returnTotal = 0;
    let returnSubTotal = 0;

    productReturnReports.forEach((report, i) => {
      returnVat += parseFloat(report.vat); 
      returnTotal  += parseFloat(report.total);
      returnSubTotal += returnTotal - returnVat;
    });

      

    // Add spacing row
    worksheet.addRow([]);
    worksheet.addRow([]);

    // Add summary rows
    worksheet.addRow(['Round Off', totalRoundof]);
    worksheet.addRow(['Vat', totalVat || 0]);
    worksheet.addRow(['Discount', `${(totalDiscount || 0).toFixed(2)}`]);
    worksheet.addRow(['Purchase Sub Total', `${(totalSubTotal || 0).toFixed(2)}`]);
    worksheet.addRow(['Purchase Total', `${(grandTotals || 0).toFixed(2)}`]);
    worksheet.addRow(['Return Total', `${(returnTotal || 0).toFixed(2)}`]);
    worksheet.addRow(['Net Total', `${((grandTotals - returnTotal) || 0).toFixed(2)}`]);



    

    // Set headers and send Excel file
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="PurchaseReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('Excel Export Error:', error);
    res.status(500).send('Could not generate Excel file.');
  }
};



export const handlePurchaseReportInvoiceApi = async (req, res) => {
   try {
        const id = req.params.id;
       
        // Get filtered order data from model
        const data = await purchaseReport.getPurchaseReportInvoice(id);
        res.json({ success: true, data: { ...data, id } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
}