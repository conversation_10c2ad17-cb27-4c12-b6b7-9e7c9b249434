import db from '../../config/db.js';

class purchaseReport {
  static async getPurchaseReport(filters = {}) {
    try {
      const {
        fromDate,
        toDate,
        country,
        buyer,
        supplier,
        payment_methodid
      } = filters;

      const country_id = parseInt(country);
      let conditions = [];
      let params = []

      let returnconditions = [];
      let returnparams = []
      if(country_id){
       conditions = ['pp.country_id = ?']; 
       params = [country_id];
       
       returnconditions = ['ppp.country_id = ?']; 
       returnparams = [country_id];
      }
      

      if (fromDate && !toDate) {
        conditions.push(`DATE(pp.bill_date) = ?`);
        params.push(fromDate);

        returnconditions.push(`DATE(ppp.return_date) = ?`);
        returnparams.push(fromDate);
      } else if (fromDate && toDate) {
        conditions.push(`DATE(pp.bill_date) BETWEEN ? AND ?`);
        params.push(fromDate, toDate);
        
        returnconditions.push(`DATE(ppp.return_date) BETWEEN ? AND ?`);
        returnparams.push(fromDate, toDate);
      }

       // buyer (number/string but not empty)
    if (buyer !== undefined && buyer !== null && String(buyer).trim() !== '') {
      conditions.push('pp.buyer = ?');
      params.push(buyer);

      
    }

    if (payment_methodid > 0) {
      conditions.push('pp.payment_methodid = ?');
      params.push(payment_methodid);

    }

    // supplier (number/string but not empty)
    if (supplier !== undefined && supplier !== null && String(supplier).trim() !== '') {
      conditions.push('pp.supplierid = ?');
      params.push(supplier);
      
      returnconditions.push('ppp.supplierid = ?');
      returnparams.push(supplier);
    }
      
      // always exclude ourshopee
      // conditions.push("asp.company_name NOT LIKE ?");
      // params.push("%ourshopee%");
      
      // returnconditions.push("asp.company_name NOT LIKE ?");
      // returnparams.push("%ourshopee%");
     
      // conditions.push("pp.virtual_store= ?");
      // params.push(0);

      // final WHERE clause
      const whereClause = conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';
      const returnWhereClause = returnconditions.length ? `WHERE ${returnconditions.join(' AND ')}` : '';


      const sql = `
            SELECT 
                pp.purchase_id,
                asp.company_name AS supplier_name,
                concat(au.firstname, ' ', au.lastname) as auth_name,
                pp.vat,
                DATE_FORMAT(pp.entry_date, '%Y-%m-%d') AS entry_date,
                DATE_FORMAT(pp.bill_date, '%Y-%m-%d') AS bill_date,
                pp.subtotal,
                pp.discount,
                pp.total,
                pp.bill_no,
                pp.roundof    
              FROM 
              procurement_purchase pp 
              LEFT JOIN admin_supplier asp ON pp.supplierid = asp.supplierid 
              LEFT JOIN aauth_users au ON au.id = pp.enterd_by
              ${whereClause} ORDER BY pp.purchase_id`;
    //  console.log('sql d',  db.format(sql, params))
      const [productReports] = await db.query(sql, params);


       const sqlReturn = `SELECT 
                ppp.id,
                ppp.purchase_id,
                asp.company_name AS supplier_name,
                concat(au.firstname, ' ', au.lastname) as auth_name,
                ppp.vat,
                DATE_FORMAT(ppp.entry_date, '%Y-%m-%d') AS entry_date,
                DATE_FORMAT(ppp.return_date, '%Y-%m-%d') AS return_date,
                ppp.subtotal,
                ppp.discount,
                ppp.total,
                ppp.bill_no
              FROM 
              procurement_purchasereturn_parent ppp 
               LEFT JOIN admin_supplier asp ON ppp.supplier_id = asp.supplierid 
              LEFT JOIN aauth_users au ON au.id = ppp.enterd_by ${returnWhereClause} `;
    // console.log('sql',  db.format(sqlReturn, returnparams))
      
      const [productReturnReports] = await db.query(sqlReturn, returnparams);
      // console.log('productReturnReports',productReturnReports)
     

      return {
        productReports,
        productReturnReports
      };
    } catch (err) {
      throw err;
    }
  }

  static async getPurchaseReportInvoice(id) {
    try {
      
      
      const sql = `
            SELECT 
              pp.purchase_id,
              pp.country_id,
              cp.title,
              ppp.price, ppp.inventory, ppp.total as order_total,
              asp.company_name AS supplier_name,
              asp.emailid,
              asp.primary_contact,
              CONCAT(au.firstname, ' ', au.lastname) AS auth_name,
              pp.vat,
              DATE_FORMAT(pp.bill_date, '%Y-%m-%d') AS bill_date,
              pp.subtotal,
              pp.discount,
              pp.total,
              pp.bill_no,
              pp.roundof    
          FROM procurement_purchase pp
          LEFT JOIN admin_supplier asp 
              ON pp.supplierid = asp.supplierid 
          JOIN procurement_purchase_products ppp 
              ON pp.purchase_id = ppp.purchase_id
          JOIN catalog_product cp 
              ON cp.productid = ppp.productid
          JOIN aauth_users au 
              ON au.id = pp.enterd_by
          WHERE pp.purchase_id = ?;`;

          const [productReports] = await db.query(sql, [id]); 
             


      return {
        productReports,
      };
    } catch (err) {
      console.error('purchaseReport error:', err.message);
      throw err;
    }
  }

  static async getReturnReportInvoice(id) {
    try {
     
      const sql = `
            SELECT
            pr.purchase_id,
            p.title,
            pr.price,
            pr.quantity as inventory,
            s.company_name       AS supplier_name,
            s.emailid            AS supplier_email,
            s.primary_contact    AS supplier_primary_contact,
            CONCAT_WS(' ', u.firstname, u.lastname) AS auth_name,
            prp.vat,
            DATE_FORMAT(prp.return_date, '%Y-%m-%d') AS bill_date,
            prp.subtotal,
            prp.discount,
            prp.total,
            prp.bill_no
          FROM procurement_purchasereturn_parent AS prp
          JOIN procurement_purchasereturn AS pr
            ON prp.id = pr.purchasereturn_id
          JOIN catalog_product AS p
            ON p.productid = pr.product_id
          LEFT JOIN admin_supplier AS s
            ON s.supplierid = pr.supplier_id
          LEFT JOIN aauth_users AS u
            ON u.id = prp.enterd_by
          WHERE prp.id = ?`;
     
          
          const [productReports] = await db.query(sql, [id]); 
             


      return {
        productReports,
      };
    } catch (err) {
      console.error('purchaseReport error:', err.message);
      throw err;
    }
  }
  
}

export default purchaseReport;