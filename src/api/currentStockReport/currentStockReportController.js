import CurrentStock from '../currentStockReport/currentStockReportModel.js';
import exceljs from 'exceljs';

// Render current stock report page
export const renderCurrentStockPage = (req, res) => {
    res.render('current-stock-report', { user: req.user, pageTitle: "Current Stock Report" });
};

// Handle API for current stock report
export const handleCurrentStockApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required filter." });
        }
        const data = await CurrentStock.getCurrentStockReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching current stock report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// Export current stock report to Excel
export const handleCurrentStockExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { products, totalStockValue } = await CurrentStock.getCurrentStockReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Current Stock Report');

        // Define Excel columns
        worksheet.columns = [
            { header: 'Product Name', key: 'name', width: 60 },
            { header: 'SKU', key: 'sku', width: 20 },
            { header: 'Safari ID', key: 'safari_id', width: 20 },
            { header: 'Category', key: 'category_name', width: 30 }, 
            { header: 'Sub Category', key: 'subcategory_name', width: 30 }, 
            { header: 'Brand', key: 'brand_name', width: 25 }, 
            { header: 'Status', key: 'status', width: 15 }, 
            { header: 'Stock Qty', key: 'stock_qty', width: 15 },
            { header: 'Order Qty', key: 'order_qty', width: 15 },
            { header: 'Actual Qty', key: 'actual_qty', width: 15 },
            { header: 'Cost', key: 'cost', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Stock Value', key: 'stock_value', width: 20, style: { numFmt: '#,##0.00' } },
            { header: 'Selling Price', key: 'selling_price', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Buyer Name', key: 'buyer_name', width: 25 },
            { header: 'Supplier Name', key: 'supplier_name', width: 30 },
            { header: 'Latest Purchase Date', key: 'latest_purchase_date', width: 30 },
        ];
        worksheet.getRow(1).font = { bold: true };

        // Add product rows
        products.forEach(product => {
            worksheet.addRow(product);
        });

        worksheet.addRow({}); // Spacer row

        // Add total row
        const totalRow = worksheet.addRow({ name: 'Total Stock Value:', stock_value: totalStockValue });
        totalRow.font = { bold: true };

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="CurrentStockReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};
