import db from '../../config/db.js';

class CurrentStock {
    static async getCurrentStockReport({ country, category, product, buyer, supplier, type }) {
        //  + p.pending_quantity
        const conds = ['cp.type_id = 1', 'spi.inventory > 0'];
        const params = [];

        if (country) {
            conds.push('spi.country_id = ?');
            params.push(country);
        }
        if (category) { conds.push('c3.categoryid = ?'); params.push(category); }
        if (product) { conds.push('cp.productid = ?'); params.push(product); }
        if (buyer) { conds.push('cp.buyer_id = ?'); params.push(buyer); }
        if (supplier) { conds.push('cp.supplierid = ?'); params.push(supplier); } 

        //  if (type === 'safari') { conds.push("p.safari_id IS NOT NULL AND p.safari_id != ''"); }
        //  if ([5, 6].includes(Number(country))) conds.push('cp.productid > 594');
        //  p.safari_id p.pending_quantity

        const sql = `
            SELECT 
                cp.title as name, cp.sku as product_code,spi.inventory as quantity, spi.cost,
                spi.selling_price as special_price, spi.rstatus as web_status,
                c3.category_name AS category_name,
                c2.category_name AS subcategory_name,
                cb.brand_name AS brand_name,
                CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS buyer_name,
                latest_purchase.supplier_name,
                latest_purchase.latest_purchase_date
            FROM 
                catalog_product cp
            LEFT JOIN catalog_product_inventory spi ON spi.productid = cp.productid
            LEFT JOIN catalog_category AS c1 
              ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category AS c2 
              ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
              AND c2.category_level = 2
            LEFT JOIN catalog_category AS c3 
              ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
              AND c3.category_level = 1
            LEFT JOIN 
                catalog_brand cb ON cb.brandid = cp.brandid
            LEFT JOIN aauth_users as au on cp.buyer_id = au.id
            LEFT JOIN (
                SELECT 
                    pp.productid,
                    pur.entry_date AS latest_purchase_date,
                    asu.company_name AS supplier_name,
                    ROW_NUMBER() OVER(
                        PARTITION BY pp.productid 
                        ORDER BY pur.entry_date DESC
                    ) AS rn
                FROM procurement_purchase_products pp
                JOIN procurement_purchase pur 
                    ON pp.purchase_id = pur.purchase_id
                JOIN admin_supplier asu 
                    ON pur.supplierid = asu.supplierid
                WHERE pur.supplierid != 8
                AND pur.country_id = ${country}
            ) AS latest_purchase 
                ON cp.productid = latest_purchase.productid
            AND latest_purchase.rn = 1

            WHERE 
                ${conds.join(' AND ')}
            ORDER BY 
                cp.productid DESC
        `;

        //console.log(sql);

        
        const [rows] = await db.query(sql, params);
        if (!rows || rows.length === 0) return { products: [], totalStockValue: 0 };

        const products = [];
        let totalStockValue = 0;

        for (const p of rows) {
            const cqty = (Number(p.quantity) || 0) + (Number(p.pending_quantity) || 0);
            if (cqty <= 0) continue;

            const selling_price = p.special_price > 0 ? p.special_price : (p.price || 0);

            let stockValue = 0;
            if (Number(country) === 6) {
                stockValue = (p.quantity || 0) * (p.cost || 0);
            } else {
                stockValue = cqty * (p.cost || 0);
            }

            totalStockValue += stockValue;

            let status_text = 'Inactive';
            if (p.web_status == 1) {
                status_text = 'Active';
            }else{
                status_text = 'Inactive';
            }

            products.push({
                name: p.name,
                sku: p.product_code || p.sku || 'N/A',
                safari_id: p.safari_id,
                stock_qty: p.quantity,
                order_qty: p.pending_quantity,
                actual_qty: cqty,
                cost: p.cost,
                stock_value: stockValue,
                selling_price,
                buyer_name: p.buyer_name || 'N/A',
                supplier_name: p.supplier_name || 'N/A',
                category_name: p.category_name, 
                subcategory_name: p.subcategory_name, 
                brand_name: p.brand_name, 
                status: p.web_status, 
                latest_purchase_date: p.latest_purchase_date
            });
        }

        return { products, totalStockValue };
    }
}

export default CurrentStock;
