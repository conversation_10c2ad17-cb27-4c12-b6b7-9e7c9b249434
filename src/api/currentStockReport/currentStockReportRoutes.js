import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderCurrentStockPage,
    handleCurrentStockApi,
    handleCurrentStockExcel
} from '../currentStockReport/currentStockReportController.js';

const router = express.Router();

// -----------------------------------------------------------------------------
// CURRENT STOCK REPORT ROUTES
// -----------------------------------------------------------------------------

// Main page render (legacy route if still used)
router.get('/current-stock-report', verifyToken, (req, res) => {
    res.render('current-stock-report', {
        pageTitle: "CURRENT STOCK REPORT",
        user: req.user
    });
});

// Render report view
router.get('/reports/current-stock', verifyToken, renderCurrentStockPage);

// API: Get report data (JSON)
router.get('/api/reports/current-stock', verifyToken, handleCurrentStockApi);

// API: Export report to Excel
router.get('/api/reports/current-stock/export', verifyToken, handleCurrentStockExcel);

export default router;
