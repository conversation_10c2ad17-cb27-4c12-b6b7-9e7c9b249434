// models/lookupModel.js

import db from '../config/db.js'; // Import DB connection

// Reusable function to execute SQL queries
export const runQuery = async (query, params = []) => {
    return db.query(query, params);
};

// ==============================
// Lookup query generator methods
// ==============================

// Get active stores
export const getStoresQuery = () =>
  //  "SELECT id, name AS text FROM shopee_store WHERE status = 1 ORDER BY name ASC";
"SELECT country_id AS id, name as text FROM admin_country WHERE status = 1 ORDER BY name ASC";

// Get all delivery team members (senders)
export const getSendersQuery = () =>
    "SELECT id, name as text FROM shopee_deliveryteam ORDER BY name ASC";

// Get active staff (admins with status 0)
export const getStaffQuery = () =>
    "SELECT au.id, CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS text FROM aauth_user_to_group ag LEFT JOIN aauth_users au ON au.id = ag.user_id WHERE ag.group_id = 2 AND au.rstatus = 1 ORDER BY au.firstname ASC";

// Get all nationalities
export const getNationalitiesQuery = () =>
    "SELECT id, country_name as text FROM admin_nationalities ORDER BY country_name ASC";

// Get all valid payment gateways
export const getPaymentGatewaysQuery = (countryId) => `
  SELECT DISTINCT 
    id AS id, 
    name AS text 
  FROM oms_payment_method 
  WHERE country_id = '${countryId}'
  ORDER BY name ASC
`;

// Get active countries
export const getCountriesQuery = () =>
    "SELECT country_id AS id, name as text FROM admin_country WHERE status = 1 ORDER BY name ASC";

// Get buyers (admins from department_id = 2)
export const getBuyersQuery = () =>
"SELECT au.id, CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS text FROM aauth_user_to_group ag LEFT JOIN aauth_users au ON au.id = ag.user_id WHERE au.departmentid = 2 AND ag.group_id = 7 AND au.rstatus = 1 ORDER BY au.firstname ASC";

// Get all product categories
export const getCategoriesQuery = () =>
    "SELECT categoryid AS id, category_name as text FROM catalog_category where category_level = 1 ORDER BY category_name ASC";


export const getSubCategoriesQuery = () =>
    "SELECT categoryid AS id, category_name as text FROM catalog_category where category_level = 2 ORDER BY category_name ASC";

export const getSubSubCategoriesQuery = () =>
    "SELECT categoryid AS id, category_name as text FROM catalog_category where category_level = 3 ORDER BY category_name ASC";

export const getBrandQuery = () =>
    "SELECT brandid AS id, brand_name as text FROM catalog_brand ORDER BY brand_name ASC";

export const getCatelogueAgentQuery = () =>
"SELECT au.id, CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS text FROM aauth_user_to_group ag LEFT JOIN aauth_users au ON au.id = ag.user_id WHERE au.departmentid = 2 AND (ag.group_id = 12 OR ag.group_id = 14) AND au.rstatus = 1 ORDER BY au.firstname ASC";

// ==============================
// Helper & Search Query Builders
// ==============================

// Return product table name based on country code
export const getProductTableName = (countryCode) => {
    const code = String(countryCode);
    const tableMap = {
        '1': 'shopee_products',
        '2': 'oman_smproducts',
        '3': 'qatar_smproducts',
        '5': 'kuwait_smproducts',
        '6': 'bahrain_smproducts',
        '7': 'saudi_smproducts'
    };
    return tableMap[code] || 'shopee_products';
};

// Build product search query with optional category filter
export const getSearchProductsQuery = (table, hasCategory) => {
    const baseQuery = `
        SELECT productid as id, CONCAT(title, ' - ', sku) as text 
        FROM catalog_product p 
        WHERE p.title LIKE ? AND p.rstatus = 1
    `;
    const categoryCondition = hasCategory ? ' AND p.categoryid = ?' : '';
    return `${baseQuery}${categoryCondition} LIMIT 50`;
};

// Build SKU search query with optional buyer filter
export const getSearchSkusQuery = (hasBuyer) => {
    let query = `
        SELECT sku as id, sku as text 
        FROM catalog_product 
        WHERE sku LIKE ?
    `;
    if (hasBuyer) {
        query += ' AND buyer_id = ?';
    }
    return query + ' LIMIT 50';
};
