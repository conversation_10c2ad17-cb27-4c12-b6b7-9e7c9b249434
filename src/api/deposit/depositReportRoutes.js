import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderSenderReportPage,
    handleSenderReportApi,
    handleSenderReportExcel
} from './depositReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/depositReport', verifyToken, renderSenderReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/depositReport', verifyToken, handleSenderReportApi);

//FIXED: Corrected function name
router.get('/api/reports/depositReport/export', verifyToken, handleSenderReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/deposit-report', verifyToken, (req, res) => {
    res.render('deposit-report', {
        pageTitle: "Deposit Report",
        user: req.user
    });
});

export default router; // Export the configured router