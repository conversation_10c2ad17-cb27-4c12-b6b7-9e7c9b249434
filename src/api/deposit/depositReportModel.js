import db from '../../config/db.js';


class senderOrder {
  static async getSenderDetails(filters) {
    const { fromDate, toDate, country = 1, status } = filters;

    let extraCondition = '';
    if (status === "done") {
      extraCondition = 'AND oo.is_done = 1';
    } else if (status === "delivered") {
      extraCondition = 'AND oo.is_done = 0';
    }

    const query = `
      SELECT
          oo.orderid,
          oo.order_ref_code AS order_id,
          au.firstname AS sender_name,
          oo.customer_details,
          oo.customer_contact,
          oo.order_date,
          oo.total_amount,
          oo.payment_methodid,
          oo.shipping_charges,
          oo.processing_fee,
          oo.donation_fee,
          oo.discount_amount,
          oo.is_done,
          d.dispatch_date,
          del.delivery_date,
          oo.done_date
      FROM oms_orders AS oo
      LEFT JOIN aauth_users AS au ON oo.driver_id = au.id
      LEFT JOIN admin_country AS ac ON oo.country_id = ac.country_id
      JOIN (
          SELECT h.orderid, MIN(h.statusdate) AS first_status_date
          FROM oms_order_history h
          WHERE h.order_status_type = 'order_status'
          AND h.order_statusid = 6
          GROUP BY h.orderid
      ) first_status ON oo.orderid = first_status.orderid
      LEFT JOIN (
          SELECT h.orderid, MIN(h.statusdate) AS dispatch_date
          FROM oms_order_history h
          WHERE h.order_status_type = 'order_status'
          AND h.order_statusid = 3
          GROUP BY h.orderid
      ) d ON oo.orderid = d.orderid
      LEFT JOIN (
          SELECT h.orderid, MIN(h.statusdate) AS delivery_date
          FROM oms_order_history h
          WHERE h.order_status_type = 'order_status'
          AND h.order_statusid = 6
          GROUP BY h.orderid
      ) del ON oo.orderid = del.orderid
      WHERE oo.country_id=${country}
        AND oo.order_statusid = 6
        AND DATE(first_status.first_status_date) BETWEEN DATE('${fromDate}') AND DATE('${toDate}')
        ${extraCondition}
        AND (
          oo.customer_details IS NULL
          OR NOT JSON_VALID(oo.customer_details)
          OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
        )
      ORDER BY oo.order_date ASC
    `;
// console.log(db.format(query))
   

    try {
      const [rows] = await db.execute(query);
      

      const formatted = rows.map(r => {
        let customerName = null;

        if (r.customer_details) {
          try {
            // Clean up common JSON issues before parsing
           const details = JSON.parse(r.customer_details || '{}');
           customerName = details.name || '';
    
          } catch {
            customerName = null;
          }
        }

        const collectedAmount = Number(r.total_amount || 0);
        const paidAmount = r.is_done ? collectedAmount : 0;
        //const balance = collectedAmount - paidAmount;

        return {
          orderid: r.orderid,
          order_id: r.order_id,
          sender_name: r.sender_name,
          customerName: customerName,
          mobile_no: r.customer_contact,
          order_date: r.order_date,
          dispatch_date: r.dispatch_date,
          delivery_date: r.delivery_date,
          done_date: r.done_date,
          calculated_amount: collectedAmount,
          payment_method: r.payment_methodid,
          payment_status: r.is_done ? "Deposited" : "Pending",
          //balance: balance
        };
      });

      if (formatted.length > 0) {
        console.log("📊 Delivered Sample:", formatted[0]);
      }
      return formatted;
    } catch (error) {
      console.error("❌ DB Error in getSenderDetails:", error);
      return [];
    }
  }
}


export default senderOrder;
