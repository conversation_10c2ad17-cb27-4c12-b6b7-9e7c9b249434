import senderOrder from './depositReportModel.js';
import exceljs from 'exceljs';



// Render the order report page with user info
export const renderSenderReportPage = (req, res) => {
    res.render('deposit-report', { user: req.user });
};

export const handleSenderReportApi = async (req, res) => {
  try {
    const filters = req.query;

    if (!filters.country) {
      return res.status(400).json({ success: false, error: "Country parameter is required." });
    }

    const delivery_details = await senderOrder.getSenderDetails(filters);

    if (!Array.isArray(delivery_details)) {
      return res.status(200).json({
        success: true,
        data: {
          orders: [],
          totals: {},
          count: 0,
          filters
        }
      });
    }

    const enriched = delivery_details.map((od) => {
      const splitStatus = od.split_id != 0 ? 'Splitted' : 'Regular';
      return {
        ...od,
        splitStatus
      };
    });

    // Optional: compute totals here if needed
    const totals = {
      totalOrders: enriched.length,
      totalAmount: enriched.reduce((acc, od) => acc + (parseFloat(od.totalamount) || 0), 0),
      shippingCharge: enriched.reduce((acc, od) => acc + (parseFloat(od.charge) || 0), 0),
      processingFees: enriched.reduce((acc, od) => acc + (parseFloat(od.processing_fee) || 0), 0),
      vat: enriched.reduce((acc, od) => acc + (parseFloat(od.vat) || 0), 0),
      discountAmount: enriched.reduce((acc, od) => acc + (parseFloat(od.discount) || 0), 0),
      grandTotal: enriched.reduce((acc, od) => acc + (parseFloat(od.display_amount) || 0), 0)
    };

    res.json({
      success: true,
      data: {
        orders: enriched,
        totals,
        count: enriched.length,
        filters
      }
    });

  } catch (error) {
    console.error("API Error fetching order report:", error);
    res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
  }
};








export const handleSenderReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    //  Fetch sender details (raw data)
    const orders = await senderOrder.getSenderDetails(filters);

    // Totals
    let subTotal = 0;
    let shippingCharge = 0;
    let grandTotal = 0;
    let totalWarrantyAmount = 0;
    let totalBalance = 0;

    //  Enrich data (same as renderReport)
    const enriched = orders.map((od) => {
     
      

      const collectedAmount = parseFloat(od.calculated_amount || 0);
      const chargeAmount = parseFloat(od.charge || 0);
      const warrantyAmount = parseFloat(od.warranty || 0);
      const balanceAmount = parseFloat(od.balance || 0);

      subTotal += collectedAmount;
      shippingCharge += chargeAmount;
      totalWarrantyAmount += warrantyAmount;
      totalBalance += balanceAmount;

      return {
        ...od,
        collectedAmount,
        chargeAmount,
        warrantyAmount,
        balanceAmount,
      };
    });

    grandTotal = subTotal;

    // Create workbook & worksheet
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet("Sender Report");

    //Define columns
    worksheet.columns = [
      { header: "No", key: "no", width: 5 },
      { header: "Order ID", key: "order_id", width: 15 },
      { header: "Customer Name", key: "customerName", width: 25 },
      { header: "Customer Mobile No", key: "mobile_no", width: 20 },
      { header: "Sender Name", key: "sender_name", width: 20 },
      { header: "Emirate", key: "emirateName", width: 20 },
      { header: "Order Date", key: "order_date", width: 20 },
      { header: "Dispatch Date", key: "dispatch_date", width: 20 },
      { header: "Delivery Date", key: "delivery_date", width: 20 },
      { header: "Done Date", key: "done_date", width: 20 },
      { header: "Amount", key: "collectedAmount", width: 15, style: { numFmt: "#,##0.00" } },
      { header: "Payment Type", key: "payment_mode", width: 20 },
    ];

    worksheet.getRow(1).font = { bold: true };

    // Add order rows
    enriched.forEach((order, index) => {
      worksheet.addRow({
        no: index + 1,
        order_id: order.order_id || "",
        customerName: order.customerName || "",
        mobile_no: order.mobile_no || "",
        sender_name: order.sender_name || "",
        order_date: order.order_date ? new Date(order.order_date).toLocaleDateString("en-GB") : "",
        dispatch_date: order.dispatch_date ? new Date(order.dispatch_date).toLocaleDateString("en-GB") : "",
        delivery_date: order.delivery_date ? new Date(order.delivery_date).toLocaleDateString("en-GB") : "",
        done_date: order.done_date ? new Date(order.done_date).toLocaleDateString("en-GB") : "",
        collectedAmount: order.collectedAmount,
        payment_mode: order.payment_method == 1 ? 'CreditCardPayments' : 'CashOnDelivery',
      });
    });

   

    // Set headers for download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="SenderReport-${new Date().toISOString().slice(0, 10)}.xlsx"`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Excel Export Error:", error);
    res.status(500).send("Could not generate Excel file.");
  }
};




