import Order from './dailyOrderStatusModel.js'; // Import order report data model
import exceljs from 'exceljs'; // Excel file generation library

// Render the order report page with user session info
export const renderDailyOrderStatusPage = (req, res) => {
    res.render('daily-order-status', { user: req.user });
};

// Handle API request to get filtered order report data
export const handleDailyOrderStatusApi = async (req, res) => {
    try {
        const filters = req.query;

        // Validate 'country' filter
        //if (!filters.country) {
            //return res.status(400).json({ success: false, error: "Country parameter is required." });
       // }

        // Fetch report data using filters
        const data = await Order.getOrderReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};



export const handleDailyOrderStatusExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch report data and grand totals
        const { orderPlatform, grandTotal } = await Order.getOrderReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Order Summary');

        // Define Excel sheet columns
        worksheet.columns = [
            { header: 'Date', key: 'date', width: 15 },
            { header: 'Total Orders', key: 'total_orders', width: 12 },
            { header: 'Total Value', key: 'total_amount', width: 15 },
            { header: 'Delivered', key: 'delivered_count', width: 12 },
            { header: 'Delivered Value', key: 'delivered_amount', width: 15 },
            { header: 'Cancelled BF', key: 'cancelled_bf_count', width: 12 },
            { header: 'Cancelled BF Value', key: 'cancelled_bf_amount', width: 15 },
            { header: 'Cancelled AF', key: 'cancelled_af_count', width: 12 },
            { header: 'Cancelled AF Value', key: 'cancelled_af_amount', width: 15 },
            { header: 'Dispatched', key: 'dispatched_count', width: 12 },
            { header: 'Dispatched Value', key: 'dispatched_amount', width: 15 },
            { header: 'To Be Dispatched', key: 'pending_count', width: 12 },
            { header: 'To Be Dispatched Value', key: 'pending_amount', width: 15 },
            { header: 'Others', key: 'others_count', width: 12 },
            { header: 'Others Value', key: 'others_amount', width: 15 },
        ];

        worksheet.getRow(1).font = { bold: true };

        // Loop through each date entry
        Object.keys(orderPlatform).forEach(date => {
            const row = orderPlatform[date];
            const dispatchedCount = row.dispatched.count + row.out_for_delivery.count;
            const dispatchedAmount = row.dispatched.amount + row.out_for_delivery.amount;

            const d = new Date(date);
            const formattedDate = `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;

            worksheet.addRow({
                date:formattedDate, // YYYY-MM-DD
                total_orders: row.total_orders,
                total_amount: parseFloat(row.total_amount).toFixed(2),
                delivered_count: row.delivered.count,
                delivered_amount: parseFloat(row.delivered.amount).toFixed(2),
                cancelled_bf_count: row.cancelled_before_dispatch.count,
                cancelled_bf_amount: parseFloat(row.cancelled_before_dispatch.amount).toFixed(2),
                cancelled_af_count: row.cancelled_after_dispatch.count,
                cancelled_af_amount: parseFloat(row.cancelled_after_dispatch.amount).toFixed(2),
                dispatched_count: dispatchedCount,
                dispatched_amount: parseFloat(dispatchedAmount).toFixed(2),
                pending_count: row.pending.count,
                pending_amount: parseFloat(row.pending.amount).toFixed(2),
                others_count: row.others?.count || 0,
                others_amount: parseFloat(row.others?.amount || 0).toFixed(2),
            });
        });

        // Add Grand Total row
        const grandDispatchedCount = grandTotal.dispatched.count + grandTotal.out_for_delivery.count;
        const grandDispatchedAmount = grandTotal.dispatched.amount + grandTotal.out_for_delivery.amount;

        const grandRow = worksheet.addRow({
            date: 'Grand Total',
            total_orders: grandTotal.total_orders,
            total_amount: parseFloat(grandTotal.total_amount).toFixed(2),
            delivered_count: grandTotal.delivered.count,
            delivered_amount: parseFloat(grandTotal.delivered.amount).toFixed(2),
            cancelled_bf_count: grandTotal.cancelled_before_dispatch.count,
            cancelled_bf_amount: parseFloat(grandTotal.cancelled_before_dispatch.amount).toFixed(2),
            cancelled_af_count: grandTotal.cancelled_after_dispatch.count,
            cancelled_af_amount: parseFloat(grandTotal.cancelled_after_dispatch.amount).toFixed(2),
            dispatched_count: grandDispatchedCount,
            dispatched_amount: parseFloat(grandDispatchedAmount).toFixed(2),
            pending_count: grandTotal.pending.count,
            pending_amount: parseFloat(grandTotal.pending.amount).toFixed(2),
            others_count: grandTotal.others?.count || 0,
            others_amount: parseFloat(grandTotal.others?.amount || 0).toFixed(2),
        });
        grandRow.font = { bold: true };

        // Set headers for Excel file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="OrderSummary-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};

