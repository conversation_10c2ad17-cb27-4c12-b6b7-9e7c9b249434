import express from 'express'; // Import express
import { verifyToken } from '../../middleware/authMiddleware.js'; // Auth middleware
import {
    renderDailyOrderStatusPage,     // Controller to render EJS view
    handleDailyOrderStatusApi,      // Controller to handle JSON API
    handleDailyOrderStatusExcel     // Controller to export Excel report
} from '../dailyOrderStatus/dailyOrderStatusController.js';

const router = express.Router(); // Create router instance

// ==============================
// Protected Routes (with Token)
// ==============================

// Dashboard route
router.get('/dashboard', verifyToken, (req, res) => {
    res.render('dashboard', {
        pageTitle: "Dashboard",
        user: req.user // Pass logged-in user info to template
    });
});

// Order report EJS route (can be removed, since handled below)
router.get('/daily-order-status', verifyToken, (req, res) => {
    res.render('daily-order-status', {
        pageTitle: "DAILY ORDER STATUS",
        user: req.user
    });
});

// ==============================
// Order Report Routes
// ==============================

// Render order report page with filters/user info
router.get('/reports/dailyOrderStatus', verifyToken, renderDailyOrderStatusPage);

// Get order report data as JSON (API call)
router.get('/api/reports/dailyOrderStatus', verifyToken, handleDailyOrderStatusApi);

// Export order report data as Excel file
router.get('/api/reports/dailyOrderStatus/export', verifyToken, handleDailyOrderStatusExcel);

export default router; // Export the configured router
