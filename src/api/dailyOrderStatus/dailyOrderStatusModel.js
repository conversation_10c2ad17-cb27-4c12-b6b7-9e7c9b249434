import db from '../../config/db.js';
import {orderFromId,orderStatus} from '../../utils/commons.js';
class Order {
    static async getOrderReport(filters) {
        const { fromDate, toDate, orderFrom, country } = filters;

        const conditions = [];
        const params = [];

        // Country filter
        if(country){
            conditions.push('oo.country_id = ?');
            params.push(country);

        }
     

        // OrderFrom filter
        if (orderFrom) {
            if (Array.isArray(orderFrom)) {
                const placeholders = orderFrom.map(() => '?').join(',');
                conditions.push(`oo.order_from_id IN (${placeholders})`);
                params.push(...orderFrom);
            } else {
                conditions.push(`oo.order_from_id = ?`);
                params.push(orderFrom);
            }
        }

        // Date filter (based on order_date)
        if (fromDate && toDate) {
            conditions.push('DATE(oo.order_date) BETWEEN ? AND ?');
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions.push('DATE(oo.order_date) = ?');
            params.push(fromDate);
        } else {
            const today = new Date().toISOString().slice(0, 10);
            conditions.push('DATE(oo.order_date) = ?');
            params.push(today);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        const query = `
            SELECT 
                DATE(oo.order_date) AS order_date,
                oo.total_amount AS total,
                oo.order_statusid
            FROM oms_orders oo
            ${whereClause} AND oo.order_statusid > 0 AND oo.type = "order" AND (
                JSON_VALID(oo.customer_details) = 0
                OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
            )
            ORDER BY oo.order_date ASC
        `;

        try {
            const [rows] = await db.execute(query, params);

            const orderPlatform = {};
            const grandTotal = {
                total_orders: 0,
                total_amount: 0,
                pending: { count: 0, amount: 0 },
                dispatched: { count: 0, amount: 0 },
                out_for_delivery: { count: 0, amount: 0 },
                delivered: { count: 0, amount: 0 },
                cancelled_before_dispatch: { count: 0, amount: 0 },
                cancelled_after_dispatch: { count: 0, amount: 0 },
            };

            for (const row of rows) {
                const date = row.order_date;
                const total = parseFloat(row.total) || 0; // <-- Convert to number

                if (!orderPlatform[date]) {
                    orderPlatform[date] = {
                        total_orders: 0,
                        total_amount: 0,
                        pending: { count: 0, amount: 0 },
                        dispatched: { count: 0, amount: 0 },
                        out_for_delivery: { count: 0, amount: 0 },
                        delivered: { count: 0, amount: 0 },
                        cancelled_before_dispatch: { count: 0, amount: 0 },
                        cancelled_after_dispatch: { count: 0, amount: 0 },
                    };
                }

                orderPlatform[date].total_orders++;
                orderPlatform[date].total_amount += total;
                grandTotal.total_orders++;
                grandTotal.total_amount += total;

                switch (row.order_statusid) {
                    case 1: // Pending
                        orderPlatform[date].pending.count++;
                        orderPlatform[date].pending.amount += total;
                        grandTotal.pending.count++;
                        grandTotal.pending.amount += total;
                        break;
                    case 3: // Dispatched
                        orderPlatform[date].dispatched.count++;
                        orderPlatform[date].dispatched.amount += total;
                        grandTotal.dispatched.count++;
                        grandTotal.dispatched.amount += total;
                        break;
                    case 5: // Out for Delivery
                        orderPlatform[date].out_for_delivery.count++;
                        orderPlatform[date].out_for_delivery.amount += total;
                        grandTotal.out_for_delivery.count++;
                        grandTotal.out_for_delivery.amount += total;
                        break;
                    case 6: // Delivered
                        orderPlatform[date].delivered.count++;
                        orderPlatform[date].delivered.amount += total;
                        grandTotal.delivered.count++;
                        grandTotal.delivered.amount += total;
                        break;
                    case 7: // Cancelled Before Dispatch
                        orderPlatform[date].cancelled_before_dispatch.count++;
                        orderPlatform[date].cancelled_before_dispatch.amount += total;
                        grandTotal.cancelled_before_dispatch.count++;
                        grandTotal.cancelled_before_dispatch.amount += total;
                        break;
                    case 11: // Cancelled After Dispatch
                        orderPlatform[date].cancelled_after_dispatch.count++;
                        orderPlatform[date].cancelled_after_dispatch.amount += total;
                        grandTotal.cancelled_after_dispatch.count++;
                        grandTotal.cancelled_after_dispatch.amount += total;
                        break;
                    default:
                        if (!orderPlatform[date].others) orderPlatform[date].others = { count: 0, amount: 0 };
                        orderPlatform[date].others.count++;
                        orderPlatform[date].others.amount += total;

                        if (!grandTotal.others) grandTotal.others = { count: 0, amount: 0 };
                        grandTotal.others.count++;
                        grandTotal.others.amount += total;
                        break;
                }
            }

            return { orderPlatform, grandTotal };
        } catch (error) {
            console.error("Error in getOrderReport:", error);
            return { orderPlatform: {}, grandTotal: {} };
        }
    }
}



export default Order;