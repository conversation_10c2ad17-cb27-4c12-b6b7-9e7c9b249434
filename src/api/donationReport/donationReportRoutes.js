import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderDonationReportPage,
    handleDonationReportApi
} from '../donationReport/donationReportController.js';

const router = express.Router();

router.get('/donation-report', verifyToken, (req, res) => {
    res.render('donation-report', {
        pageTitle: "DONATION REPORT",
        user: req.user
    });
});

// ===============================================
// DONATION REPORT ROUTES
// ===============================================
router.get('/reports/donation', verifyToken, renderDonationReportPage);
router.get('/api/reports/donation', verifyToken, handleDonationReportApi);

export default router;
