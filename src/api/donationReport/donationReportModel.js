import db from '../../config/db.js';

class DonationReport {
    static async getReport(filters) {
        let { fromDate, toDate } = filters;
        // AND o.virtual_store = 0
        let conditions = `o.country_id = 1 AND o.donation_fee > 0 AND order_statusid > 0 AND LOWER(JSON_UNQUOTE(JSON_EXTRACT(o.customer_details, '$.name'))) NOT LIKE '%test%' ` ;
        const params = [];
        
        if (fromDate && toDate) {
            conditions += " AND DATE(o.order_date) BETWEEN ? AND ?";
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions += " AND DATE(o.order_date) = ?";
            params.push(fromDate);
        }

        const sql = `
            SELECT 
                o.order_ref_code as orderid, 
                o.donation_fee, 
                JSON_UNQUOTE(JSON_EXTRACT(o.customer_details, '$.name')) AS customer, 
                o.customer_contact AS contact, 
                p.pay_percentage, 
                p.name AS payment_method_name, 
                o.order_date,
                o.order_status AS status ,
                o.gway_transaction_id,
                o.payment_method
            FROM oms_orders o 
            LEFT JOIN oms_payment_method p ON o.gway_paymentmethod = p.id AND p.country_id = o.country_id
            WHERE ${conditions}
            ORDER BY o.order_date ASC
        `;
        const [results] = await db.query(sql, params);

        // console.log(results)
        return results;
    }
}

export default DonationReport;