import DonationReport from '../donationReport/donationReportModel.js';
import Pay_percentage from '../../utils/constants.js'
import ExcelJS from 'exceljs';

export const renderDonationReportPage = (req, res) => {
    res.render('reports/donation-report', {
        user: req.user,
        pageTitle: "Donation Report"
    });
};

export const handleDonationReportApi = async (req, res) => {
    try {
        const reportData = await DonationReport.getReport(req.query);
        const formattedData = await formatData(reportData)
        const excel = await generateDonationReportExcel(formattedData)

         // Convert Excel to base64
        const buffer = await excel.xlsx.writeBuffer();
        const excelBase64 = buffer.toString('base64');
        
        res.json({ 
            success: true, 
            data: formattedData, 
            excelFile: excelBase64,
            fileName: `Donation_Report_${req.query.fromDate}_${req.query.toDate}.xlsx`
        });
    } catch (error) {
        console.error("API Error fetching donation report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

const formatData = async (data) => {
    try {
        if (data.length > 0) {
            const totals = { ftotal: 0, pamount: 0, ramount: 0, camount: 0 };
            const outputForReport = data.map ( order => {

                // correct migration data for payment methods 
                if(order.payment_method_name === null) {
                    if (order.payment_method === 'Credit') order.payment_method_name = 'COD'
                    if (order.payment_method === 'Cash') {
                        if (order.gway_transaction_id.split('-').length === 1) {
                            order.payment_method_name = 'Payfort'
                        } else {
                            order.payment_method_name = 'Tabby'
                        }
                    }
                }
                const charge = (parseFloat(order.donation_fee || 0) * parseFloat(Pay_percentage[order.payment_method_name] || 0)) / 100;
                const total = parseFloat(order.donation_fee || 0) - charge;
                let displayStatus = 'Pending';
                
                if(order.payment_method_name != 'COD'){
                    displayStatus = 'Received';
                    totals.ramount += total;
                } else {
                    if(order.status.toLowerCase().includes('cancelled')) {
                        displayStatus = 'Cancelled';
                        totals.camount += total;
                    } else if (order.status === 'Delivered') {
                        displayStatus = 'Received';
                        totals.ramount += total;
                    } else {
                        totals.pamount += total;
                    }
                }

                totals.ftotal += total;

                return {
                    orderid: order.orderid,
                    donation_fee: order.donation_fee,
                    customer: order.customer,
                    contact: order.contact,
                    payment_method_name: order.payment_method_name,
                    order_date: order.order_date,
                    status: order.status,
                    charge,
                    total,
                    displayStatus
                }

            } )

            totals.camount = totals.camount.toFixed(2)
            totals.ramount = totals.ramount.toFixed(2)
            totals.pamount = totals.pamount.toFixed(2)
            totals.ftotal = totals.ftotal.toFixed(2)

            return {orders: outputForReport, totals }
        }


    } catch (err) {
        console.log(err)
    }
}



async function generateDonationReportExcel(reportData, dates) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Donation Report');

    // Set up column definitions
    worksheet.columns = [
        { header: 'No', key: 'no', width: 8 },
        { header: 'Order Details', key: 'orderDetails', width: 35 },
        { header: 'Mobile', key: 'mobile', width: 15 },
        { header: 'Customer', key: 'customer', width: 25 },
        { header: 'Pay Mode', key: 'payMode', width: 15 },
        { header: 'Donation Fee', key: 'donationFee', width: 15 },
        { header: 'Charges', key: 'charges', width: 12 },
        { header: 'Final Amount', key: 'finalAmount', width: 15 },
        { header: 'Status', key: 'status', width: 12 }
    ];

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Add data rows
    reportData.orders.forEach((order, index) => {
        const orderDate = new Date(order.order_date);
        const formattedDate = orderDate.toLocaleString('en-US', {
            month: 'numeric',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });

        // Create order details string with line breaks
        const orderDetails = `${order.orderid}\n${formattedDate}\n${order.status}`;

        const row = worksheet.addRow({
            no: index + 1,
            orderDetails: orderDetails,
            mobile: order.contact || '',
            customer: order.customer || '',
            payMode: order.payment_method_name || 'N/A',
            donationFee: parseFloat(order.donation_fee || 0),
            charges: parseFloat(order.charge || 0),
            finalAmount: parseFloat(order.total || 0),
            status: order.displayStatus || ''
        });

        // Enable text wrapping for order details
        row.getCell('orderDetails').alignment = { 
            wrapText: true, 
            vertical: 'top' 
        };
        row.height = 60; // Increase row height for wrapped text

        // Right align numeric columns
        ['donationFee', 'charges', 'finalAmount'].forEach(key => {
            row.getCell(key).alignment = { horizontal: 'right' };
            row.getCell(key).numFmt = '#,##0.00';
        });

        // Center align status column
        row.getCell('status').alignment = { horizontal: 'center' };

        // Color code status
        const statusCell = row.getCell('status');
        if (order.displayStatus === 'Received') {
            statusCell.font = { color: { argb: '0070C0' }, bold: true };
        } else if (order.displayStatus === 'Cancelled') {
            statusCell.font = { color: { argb: 'C00000' }, bold: true };
        } else if (order.displayStatus === 'Pending') {
            statusCell.font = { color: { argb: 'E36C0A' }, bold: true };
        }
    });

    // Add spacing before totals
    worksheet.addRow({});
    worksheet.addRow({});

    // Add totals section
    const totalsStartRow = worksheet.rowCount + 1;
    
    // Add totals with proper alignment
    const totalsData = [
        ['Total', ':', parseFloat(reportData.totals.ftotal)],
        ['Pending', ':', parseFloat(reportData.totals.pamount)],
        ['Received', ':', parseFloat(reportData.totals.ramount)],
        ['Cancelled', ':', parseFloat(reportData.totals.camount)]
    ];

    totalsData.forEach(([label, colon, amount]) => {
        const row = worksheet.addRow({});
        row.getCell(7).value = label; // Column G
        row.getCell(8).value = colon; // Column H
        row.getCell(9).value = amount; // Column I

        // Style totals
        row.getCell(7).font = { bold: true };
        row.getCell(9).font = { bold: true };
        row.getCell(9).alignment = { horizontal: 'right' };
        row.getCell(9).numFmt = '#,##0.00';
    });

    // Add borders to the main table
    const tableRange = `A1:I${reportData.orders.length + 1}`;
    worksheet.getCell(tableRange).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    };

    return workbook;
}
