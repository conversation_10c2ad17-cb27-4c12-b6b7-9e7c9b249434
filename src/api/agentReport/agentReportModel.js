import db from '../../config/db.js';

class agentReportOld {
 
  static async getAgentReport(filters = {}) {
    try {
      let cond = '';
      const params = [];

      const fromDate = filters.fromDate || '';
      const toDate = filters.toDate || '';

      if (fromDate && !toDate) {
        cond = `DATE(order_date) = ?`;
        params.push(fromDate);
      } else if (fromDate && toDate) {
        cond = `DATE(order_date) BETWEEN ? AND ?`;
        params.push(fromDate, toDate);
      } else {
        const today = new Date().toISOString().slice(0, 10);
        cond = `DATE(order_date) = ?`;
        params.push(today);
      }

      // Fetch agents
      const [agents] = await db.query(`
        SELECT 
          u.id, 
          CONCAT(u.firstname, ' ', COALESCE(u.lastname, '')) AS name 
        FROM aauth_users u 
        LEFT JOIN aauth_user_to_group g 
          ON u.id = g.user_id  
        WHERE g.group_id = 10 
          AND u.rstatus = 1
      `);

      // Country mapping
      const countryIds = [1, 2, 3, 6, 5];
      const countryLabels = {
        1: 'UAE',
        2: 'Oman',
        3: 'Qatar',
        6: 'Bahrain',
        5: 'Kuwait',
      };

      // Get distinct orderfrom values
      const [orderFromRows] = await db.query(`
        SELECT DISTINCT list_lable AS name 
        FROM admin_lists 
        WHERE list_type = 'order_from'
      `);

      const orderFromMap = orderFromRows.reduce((map, { name }) => {
        const original = name ?? '';
        const alias = original === '' ? 'replacemant' : original.toLowerCase();
        map[original] = alias;
        return map;
      }, {});
      if (!('' in orderFromMap)) {
        orderFromMap[''] = 'replacemant';
      }
      const orderFromAliases = [...new Set(Object.values(orderFromMap))].sort();

      // Grand totals init
      const grandTotal = {
        UAE: 0, Oman: 0, Qatar: 0, Bahrain: 0, Kuwait: 0, total: 0,
      };
      orderFromAliases.forEach(alias => (grandTotal[alias] = 0));

      const result = [];

      for (const agent of agents) {
        const row = {
          staffId: agent.id,
          name: agent.name,
          UAE: 0, Oman: 0, Qatar: 0, Bahrain: 0, Kuwait: 0,
          total: 0,
        };
        orderFromAliases.forEach(alias => (row[alias] = 0));

        for (const countryId of countryIds) {
          const countryLabel = countryLabels[countryId];
          const extraCond = `AND country_id = ${db.escape(countryId)}`;

          // SUM(CASE WHEN ...) for orderfrom
          const sumCases = Object.keys(orderFromMap)
            .map(original => {
              const alias = orderFromMap[original];
              const val = original === '' ? '' : original;
              return `SUM(CASE WHEN orderfrom = ${db.escape(val)} THEN 1 ELSE 0 END) AS \`${alias}\``;
            })
            .join(', ');

          const sql = `
            SELECT COUNT(orderid) AS total_order, ${sumCases}
            FROM oms_orders
            WHERE staff_id = ? AND ${cond} ${extraCond}
          `;

          const queryParams = [agent.id, ...params];
          const [[data]] = await db.query(sql, queryParams);

          const countryTotal = Number(data.total_order) || 0;
          row[countryLabel] += countryTotal;
          row.total += countryTotal;
          grandTotal[countryLabel] += countryTotal;
          grandTotal.total += countryTotal;

          for (const alias of orderFromAliases) {
            row[alias] += Number(data[alias]) || 0;
            grandTotal[alias] += Number(data[alias]) || 0;
          }
        }

        if (row.total > 0) {
          result.push(row);
        }
      }

      return { result, grandTotal };
    } catch (error) {
      console.error('Error in getAgentReport:', error);
      throw error;
    }
  }

}

class agentReport {
  static async getAgentReport(filters = {}) {
    try {
      const fromDate = filters.fromDate || '';
      const toDate = filters.toDate || '';

      const params = [];
      let dateCond = '';

      if (fromDate && !toDate) {
        dateCond = `DATE(oo.order_date) = ?`;
        params.push(fromDate);
      } else if (fromDate && toDate) {
        dateCond = `DATE(oo.order_date) BETWEEN DATE(?) AND DATE(?)`;
        params.push(fromDate, toDate);
      } else {
        const today = new Date().toISOString().slice(0, 10);
        dateCond = `DATE(oo.order_date) = DATE(?)`;
        params.push(today);
      }

      const sql = `
        SELECT 
          au.id AS staffId,
          CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS name,
          DATE(oo.order_date) AS orderDate,

          -- Country-wise
          SUM(CASE WHEN ac.name = 'UAE' THEN 1 ELSE 0 END) AS UAE,
          SUM(CASE WHEN ac.name = 'Oman' THEN 1 ELSE 0 END) AS Oman,
          SUM(CASE WHEN ac.name = 'Qatar' THEN 1 ELSE 0 END) AS Qatar,
          SUM(CASE WHEN ac.name = 'Bahrain' THEN 1 ELSE 0 END) AS Bahrain,
          SUM(CASE WHEN ac.name = 'Kuwait' THEN 1 ELSE 0 END) AS Kuwait,

          -- Platform-wise
         
       
          SUM(CASE WHEN oo.order_from_id = 5 THEN 1 ELSE 0 END) AS FACEBOOK,
          SUM(CASE WHEN oo.order_from_id = 8 THEN 1 ELSE 0 END) AS FBFEEDLIST,
          SUM(CASE WHEN oo.order_from_id = 11 THEN 1 ELSE 0 END) AS OTHER,
          SUM(CASE WHEN oo.order_from_id = 9 THEN 1 ELSE 0 END) AS Webfeed_OS,
          SUM(CASE WHEN oo.order_from_id = 7 THEN 1 ELSE 0 END) AS TELEPHONE,
          SUM(CASE WHEN oo.order_from_id = 6 THEN 1 ELSE 0 END) AS WEBFEED,
          SUM(CASE WHEN oo.order_from_id = 1 THEN 1 ELSE 0 END) AS WEBSITE,
          SUM(CASE WHEN oo.order_from_id = 4 THEN 1 ELSE 0 END) AS WHATSAPP,

          COUNT(oo.orderid) AS total
        FROM aauth_users au
        JOIN aauth_user_to_group aug ON au.id = aug.user_id
        LEFT JOIN oms_orders oo 
          ON oo.staff_id = au.id
          AND ${dateCond}
        LEFT JOIN admin_country ac ON oo.country_id = ac.country_id
        WHERE aug.group_id = 10
          AND au.rstatus = 1
        GROUP BY au.id
        ORDER BY orderDate DESC
      `;

      const [rows] = await db.query(sql, params);

      // Build grand total dynamically
      const grandTotal = {};
      rows.forEach(r => {
        Object.keys(r).forEach(key => {
          if (['staffId', 'name', 'orderDate'].includes(key)) return;
          grandTotal[key] = (grandTotal[key] || 0) + Number(r[key] || 0);
        });
      });


      return { result: rows, grandTotal };
    } catch (error) {
      console.error('Error in getAgentReport:', error);
      throw error;
    }
  }
}

export default agentReport;
