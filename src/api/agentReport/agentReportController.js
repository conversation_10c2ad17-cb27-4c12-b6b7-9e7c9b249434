import agentReport from './agentReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderAgentReportPage = (req, res) => {
    res.render('agent-report', { user: req.user });
};

// API handler to fetch order report data based on filters 
export const handleAgentReportApi = async (req, res) => {
    try {
        const filters = req.query;
        // Get filtered order data from model
        const data = await agentReport.getAgentReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};



