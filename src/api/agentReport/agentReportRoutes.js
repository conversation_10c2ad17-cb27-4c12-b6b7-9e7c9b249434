import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderAgentReportPage,
    handleAgentReportApi
} from './agentReportController.js';

const router = express.Router(); // Create router instance

//DAILY PROFIT REPORT
router.get('/reports/agentReport', verifyToken, renderAgentReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/agentReport', verifyToken, handleAgentReportApi); 


// Order report EJS route (can be removed, since handled below)
router.get('/agent-report', verifyToken, (req, res) => {
    res.render('agent-report', {
        pageTitle: "Agent Report",
        user: req.user
    });
});

export default router; // Export the configured router
