import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderStockReportPage,
    handleStockReportApi,
    handleStockReportExcel
} from '../stockReport/stockReportController.js';

const router = express.Router(); // Create router instance

// ===============================================
// STOCK REPORT ROUTES
// ===============================================

// Render the Stock Report EJS page
router.get('/stock-report', verifyToken, (req, res) => {
    res.render('stock-report', {
        pageTitle: "STOCK REPORT",
        user: req.user
    });
});

// Render Stock Report page (used internally by layout or routes)
router.get('/reports/stock', verifyToken, renderStockReportPage);

// API: Fetch Stock Report data in JSON format
router.get('/api/reports/stock', verifyToken, handleStockReportApi);

// API: Export Stock Report data to Excel
router.get('/api/reports/stock/export', verifyToken, handleStockReportExcel);

export default router;
