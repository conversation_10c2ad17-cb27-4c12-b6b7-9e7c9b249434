import db from '../../config/db.js';

class StockReport {

    static async getStockReport(filters) {
        const { country: countryId, buyer, category, product } = filters;
        if (!countryId) return { products: [] };

        let conditions = [];
        const params = [];

        if (category) { conditions.push('p.categoryid = ?'); params.push(category); }
        if (product) { conditions.push('p.productid = ?'); params.push(product); }
        if (buyer) { conditions.push('p.buyer_id = ?'); params.push(buyer); }

        if (countryId === '1') { // UAE specific logic
            conditions.push('p.rstatus = 1');
        } else if (countryId === '6') { // Bahrain specific logic
            conditions.push('p.rstatus = 1 AND inv.inventory != 0');
        } else if (['2'].includes(countryId)) { // Oman specific logic
            conditions.push('p.rstatus = 1');
            if (category || buyer || product) {
                conditions.push('inv.inventory != 0');
            }
        } else { // Qatar, Kuwait, and other countries logic
            conditions.push('p.rstatus = 1');
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // --- Step 2: Fetch the initial list of products ---
        const baseQuery = ` 
        SELECT p.productid as id, p.sku AS product_code, p.title AS name, inv.inventory AS quantity 
        FROM catalog_product p 
        INNER JOIN catalog_product_inventory inv ON p.productid = inv.productid
        AND inv.country_id = ${countryId}
        ${whereClause} ORDER BY product_code DESC`;
        const [products] = await db.query(baseQuery, params);
        if (products.length === 0) {
            return { products: [] };
        }

        // --- Step 3: Fetch all purchase data for these products in a single query ---
        const productIds = products.map(p => p.id);
        const purchaseDataMap = new Map();

        const purchaseSql = `
            SELECT productid AS item_name, SUM(inventory) as total_purchased 
            FROM procurement_purchase_products
            WHERE productid IN (?)
            GROUP BY productid
        `;
        const [purchases] = await db.query(purchaseSql, [productIds]);
        purchases.forEach(p => {
            purchaseDataMap.set(p.item_name, p.total_purchased);
        });
        // --- Step 4: Process the data in JavaScript, exactly like the PHP `foreach` loop ---
        const reportProducts = products.map(p => {
            const stock_in = p.quantity;
            const stock_out = Number(purchaseDataMap.get(p.id)) || 0;
            const balance = stock_in - stock_out;
            return {
                id: p.id,
                product_code: p.product_code,
                product_name: p.name,
                balance,
                stock_in,
                stock_out
            };
        });

        return { products: reportProducts };
    }
}

export default StockReport;