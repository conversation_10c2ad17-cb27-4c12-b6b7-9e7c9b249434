import Stock from '../stockReport/stockReportModel.js';
import exceljs from 'exceljs';
import db from '../../config/db.js'; // Assuming db is in config

// =====================================================
// RENDER STOCK REPORT PAGE
// =====================================================
export const renderStockReportPage = (req, res) => {
    res.render('stock-report', { user: req.user, pageTitle: "Stock Report" });
};

// =====================================================
// API: GET STOCK REPORT DATA
// =====================================================
export const handleStockReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Country is mandatory for this report
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required filter." });
        }

        const data = await Stock.getStockReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching stock report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// =====================================================
// API: EXPORT STOCK REPORT TO EXCEL
// =====================================================
export const handleStockReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch report data for Excel
        const { products } = await Stock.getStockReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Stock Report');

        // Define column headers and widths
        worksheet.columns = [
            { header: 'Product/Item Code', key: 'product_code', width: 25 },
            { header: 'Product Name', key: 'product_name', width: 60 },
            { header: 'Stock In', key: 'stock_in', width: 15, style: { numFmt: '0' } },
            { header: 'Stock Out', key: 'stock_out', width: 15, style: { numFmt: '0' } },
            { header: 'Balance', key: 'balance', width: 15, style: { numFmt: '0' } }
        ];

        worksheet.getRow(1).font = { bold: true };

        // Append rows
        products.forEach(product => {
            worksheet.addRow(product);
        });

        // Set response headers for file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="StockReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};
