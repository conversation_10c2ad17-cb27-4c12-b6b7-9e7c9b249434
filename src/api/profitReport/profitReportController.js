import { getCategoryLineage } from '../../utils/commons.js';
import profitReport from './profitReportModel.js';
import exceljs from 'exceljs';

// Render the order report page with user info
export const renderProfitReportPage = (req, res) => {
  res.render('profit-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleProfitReportApi = async (req, res) => {
  try {
    const filters = req.query;

    // Ensure 'country' filter is provided
    if (!filters.country) {
      return res.status(400).json({ success: false, error: "Country parameter is required." });
    }

    // Get filtered order data from model
    const data = await profitReport.getProfitReport(filters);
    res.json({ success: true, data: { ...data, filters } });
  } catch (error) {
    console.error("API Error fetching order report:", error);
    res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
  }
};

// API handler to export order report data as Excel fil

export const handleProfitReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    // Fetch the same data the UI renders
    const { categoryData = [], grandTotals = {}, orderSummary = {} } =
      await profitReport.getProfitReport(filters); // returns categoryData, grandTotals, orderSummary [1]

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Profit Report'); // create worksheet [1]

    // Define detail table columns (no separate category header row)
    worksheet.columns = [
      { header: 'No', key: 'no', width: 5 },
      { header: 'Product Name', key: 'productName', width: 30 },
      { header: 'Category Name', key: 'categoryName', width: 28 },
      { header: 'SKU', key: 'productCode', width: 16 },
      { header: 'Buyer', key: 'buyerName', width: 22 },
      { header: 'Qty', key: 'quantity', width: 10 },
      { header: 'Price', key: 'totalPrice', width: 15 },
      { header: 'Cost', key: 'totalCost', width: 15 },
      { header: 'VAT', key: 'vat', width: 12 },
      { header: 'Profit', key: 'profit', width: 15 },
      { header: 'Profit %', key: 'profitPercent', width: 12 },
    ]; // column key mapping [1]
    worksheet.getRow(1).font = { bold: true }; // header bold [1]

    const num = (v) => Number(v) || 0; // numeric helper [1]
    const f2 = (v) => Number(v ?? 0).toFixed(2); // fixed 2dp text when writing as string [4]

    // Write product rows directly (show category in the Category Name column)
    let globalNo = 1; // continuous numbering across all categories [1]
    for (const category of categoryData) {
      const products = category.products || []; // guard undefined arrays [1]
      for (const p of products) {
        const row = worksheet.addRow({
          no: globalNo++,
          productName: p.productName,
          categoryName: category.catname,
          productCode: p.productCode,
          buyerName: p.buyerName,
          quantity: num(p.quantity),
          // write numbers so Excel can aggregate; apply numFmt for display
          totalPrice: num(p.totalPrice),
          totalCost: num(p.totalCost),
          vat: num(p.vat),
          profit: num(p.profit),
          // write as fraction for proper % formatting later (or keep number then set '0.00%')
          profitPercent: num(p.profitPercent),
        }); // add row per product [1]

        // Align and format numeric cells
        ['F', 'G', 'H', 'I', 'J', 'K'].forEach(c => {
          row.getCell(c).alignment = { horizontal: 'right' };
          row.getCell(c).numFmt = '#,##0.00';
        }); // number format [3]
        // Qty: right align integer
        row.getCell('F').numFmt = '#,##0'; // quantity as integer [3]
        // Profit %: percent format
        row.getCell('L').alignment = { horizontal: 'right' };
        row.getCell('L').numFmt = '0.00%'; // percent number format [3]
      }

      // Optional: category subtotal row (kept since category name is already a column)
      // if (category.totals) {
      //   const t = category.totals;
      //   const tr = worksheet.addRow({
      //     productName: 'Category Total',
      //     categoryName: category.catname,
      //     quantity: num(t.totalQuantity),
      //     totalPrice: num(t.totalSales),
      //     totalCost: num(t.totalCost),
      //     vat: num(t.totalVat),
      //     totalInclVat: num(t.totalWithoutVat),
      //     profit: num(t.totalProfit),
      //     profitPercent: num(t.totalProfitPercent) / 100,
      //   }); // category totals [1]
      //   tr.font = { bold: true };
      //   ['F', 'G', 'H', 'I', 'J', 'K'].forEach(c => {
      //     tr.getCell(c).alignment = { horizontal: 'right' };
      //     tr.getCell(c).numFmt = '#,##0.00';
      //   }); // numeric formats [3]
      //   tr.getCell('F').numFmt = '#,##0';
      //   tr.getCell('L').alignment = { horizontal: 'right' };
      //   tr.getCell('L').numFmt = '0.00%'; // percent [3]
      //   worksheet.addRow([]); // spacer [1]
      // }
    }

    // Grand totals (detail-level)
    const gtTitle = worksheet.addRow(['Grand Totals']); // title row [1]
    gtTitle.font = { bold: true };
    // Fill grand totals in appropriate columns by adding an object row
    const gtRow = worksheet.addRow({
      quantity: num(grandTotals.totalQuantity),
      totalPrice: num(grandTotals.totalSales),
      totalCost: num(grandTotals.totalCost),
      vat: num(grandTotals.totalVat),
      totalInclVat: num(grandTotals.totalWithoutVat),
      profit: num(grandTotals.totalProfit),
      profitPercent: num(grandTotals.totalProfitPercent) / 100,
    }); // grand totals [1]
    gtRow.font = { bold: true };
    ['F', 'G', 'H', 'I', 'J', 'K'].forEach(c => {
      gtRow.getCell(c).alignment = { horizontal: 'right' };
      gtRow.getCell(c).numFmt = '#,##0.00';
    }); // numeric formats [3]
    gtRow.getCell('F').numFmt = '#,##0';
    gtRow.getCell('L').alignment = { horizontal: 'right' };
    gtRow.getCell('L').numFmt = '0.00%'; // percent [3]
    worksheet.addRow([]); // spacer [1]

    // Summary (use API values directly to match UI)
    const totalOrders = num(orderSummary.totalorder);
    const totalQtySum = num(orderSummary.totalquantity);
    const subtotal = num(orderSummary.subtotal);   // API subtotal [1]
    const totvat = num(orderSummary.totvat);
    const discount = num(orderSummary.discount);
    const shipping = num(orderSummary.shippingcharge);
    const pfee = num(orderSummary.pfee);
    const donation = num(orderSummary.donation);
    const grandTotal = num(orderSummary.total);

    const summaryRows = [
      ['Total Orders', totalOrders],
      ['Total Quantity', totalQtySum],
      ['Subtotal (Incl. VAT)', subtotal],
      ['VAT', totvat],
      ['Discount', -discount],
      ['Shipping Charge', shipping],
      ['Processing Fee', pfee],
      ['Donation', donation],
      ['Grand Total', grandTotal],
      ['Total Cost', num(grandTotals.totalCost)],
      ['Total Profit', num(grandTotals.totalProfit)],
      ['Profit %', num(grandTotals.totalProfitPercent)],
    ]; // summary lines [1]

    // Render summary with labels in A:K and value in L
    for (const [label, value] of summaryRows) {
      const row = worksheet.addRow([]);
      const r = row.number;
      // merge label cells for readability
      worksheet.mergeCells(`A${r}:K${r}`); // horizontal merge for label [2]
      const labelCell = worksheet.getCell(`A${r}`);
      labelCell.value = label;
      labelCell.font = { bold: true };
      const valCell = worksheet.getCell(`L${r}`);
      if (label === 'Profit %') {
        valCell.value = Number(value) / 100;
        valCell.numFmt = '0.00%';
        valCell.alignment = { horizontal: 'right' };
      } else {
        valCell.value = num(value);
        valCell.numFmt = '#,##0.00';
        valCell.alignment = { horizontal: 'right' };
      }
    } // summary formatting [3]

    // Stream Excel
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'); // MIME [1]
    res.setHeader('Content-Disposition', `attachment; filename="ProfitReport-${new Date().toISOString().slice(0, 10)}.xlsx"`); // download [1]
    await workbook.xlsx.write(res); // write stream [1]
    res.end(); // finish response [1]
  } catch (error) {
    console.error('Excel Export Error:', error); // log [1]
    res.status(500).send('Could not generate Excel file.'); // error response [1]
  }
};


//DAILY PROFIT REPORT
// Render the order report page with user info
export const renderDailyProfitReportPage = (req, res) => {
  res.render('daily-profit-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleDailyProfitReportApi = async (req, res) => {
  try {
    const filters = req.query;

    // Ensure 'country' filter is provided
    if (!filters.country) {
      return res.status(400).json({ success: false, error: "Country parameter is required." });
    }

    // Get filtered order data from model
    const data = await profitReport.getDailyProfitReport(filters);
    res.json({ success: true, data: { ...data, filters } });
  } catch (error) {
    console.error("API Error fetching order report:", error);
    res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
  }
};

// API handler to export order report data as Excel fil

export const handleDailyProfitReportExcel = async (req, res) => {
  try {
    const filters = req.query;

    const { categoryData = [], grandTotals = {}, orderSummary = {} } =
      await profitReport.getDailyProfitReport(filters); // same data used by UI [6]

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Profit Report'); // create worksheet [6]

    // Detail table columns (match UI product table)
    worksheet.columns = [
      { header: 'No', key: 'no', width: 5 },
      { header: 'Product Name', key: 'productName', width: 35 },
      { header: 'SKU', key: 'productCode', width: 18 },
      { header: 'Buyer', key: 'buyerName', width: 22 },
      { header: 'Qty', key: 'quantity', width: 10 },
      { header: 'Price', key: 'totalPrice', width: 15 },
      { header: 'Cost', key: 'totalCost', width: 15 },
      { header: 'VAT', key: 'vat', width: 12 },
      // { header: 'Total Incl. VAT', key: 'totalInclVat', width: 18 },
      { header: 'Profit', key: 'profit', width: 15 },
      { header: 'Profit %', key: 'profitPercent', width: 12 },
    ]; // column definitions [6]
    worksheet.getRow(1).font = { bold: true }; // header style [6]

    const num = (v) => Number(v) || 0;

    // Write product rows directly (NO merged category header rows)
    let globalNo = 1;
    for (const category of categoryData) {
      const products = category.products || [];
      for (const p of products) {
        const row = worksheet.addRow({
          no: globalNo++,
          productName: p.productName,
          productCode: p.productCode,
          buyerName: p.buyerName,
          quantity: num(p.quantity),
          totalPrice: num(p.totalPrice),
          totalCost: num(p.totalCost),
          vat: num(p.vat),
          // totalInclVat: num(p.totalInclVat),
          profit: num(p.profit),
          // write as fraction; Excel formats as percent
          profitPercent: num(p.profitPercent)
        }); // addRow avoids row overlap; no manual rowIndex needed [6][15]

        // Align and format numeric columns
        ['F', 'G', 'H', 'I', 'J'].forEach(c => {
          row.getCell(c).alignment = { horizontal: 'right' }; // right-align numbers [7][13]
          row.getCell(c).numFmt = '#,##0.00';                 // numeric format [11][14]
        });
        row.getCell('E').alignment = { horizontal: 'right' };
        row.getCell('E').numFmt = '#,##0';                    // Qty as integer [11]
        row.getCell('L').alignment = { horizontal: 'right' };
        row.getCell('L').numFmt = '0.00%';                    // percent format [1][11]
      }

      // Optional: keep category totals row (bold), without merging headers
      // if (category.totals) {
      //   const t = category.totals;
      //   const tr = worksheet.addRow({
      //     productName: 'Total',
      //     quantity: num(t.totalQuantity),
      //     totalPrice: num(t.totalSales),
      //     totalCost: num(t.totalCost),
      //     vat: num(t.totalVat),
      //     totalInclVat: num(t.totalWithoutVat),
      //     profit: num(t.totalProfit),
      //     profitPercent: num(t.totalProfitPercent) / 100
      //   }); // category totals [6]
      //   tr.font = { bold: true };
      //   ['E', 'F', 'G', 'H', 'I', 'J'].forEach(c => {
      //     tr.getCell(c).alignment = { horizontal: 'right' };
      //     tr.getCell(c).numFmt = '#,##0.00';
      //   }); // numeric formats [11]
      //   tr.getCell('E').numFmt = '#,##0';
      //   tr.getCell('L').alignment = { horizontal: 'right' };
      //   tr.getCell('L').numFmt = '0.00%'; // percent [1][11]
      //   worksheet.addRow([]); // spacer [6]
      // }
    }

    // Grand totals (detail, same as UI rollup)
    const gtTitle = worksheet.addRow(['Grand Totals']); // title row [6]
    gtTitle.font = { bold: true };
    const gtRow = worksheet.addRow({
      quantity: num(grandTotals.totalQuantity),
      totalPrice: num(grandTotals.totalSales),
      totalCost: num(grandTotals.totalCost),
      vat: num(grandTotals.totalVat),
      totalInclVat: num(grandTotals.totalWithoutVat),
      profit: num(grandTotals.totalProfit),
      profitPercent: num(grandTotals.totalProfitPercent) / 100
    }); // totals row [6]
    gtRow.font = { bold: true };
    ['E', 'F', 'G', 'H', 'I', 'J'].forEach(c => {
      gtRow.getCell(c).alignment = { horizontal: 'right' };
      gtRow.getCell(c).numFmt = '#,##0.00';
    }); // number formats [11]
    gtRow.getCell('E').numFmt = '#,##0';
    gtRow.getCell('L').alignment = { horizontal: 'right' };
    gtRow.getCell('L').numFmt = '0.00%'; // percent format [1][11]
    worksheet.addRow([]); // spacer [6]

    // Summary (use API values verbatim; mirror the UI)
    const totalOrders = num(orderSummary.totalorder);
    const totalQtySum = num(orderSummary.totalquantity);
    const subtotal = num(orderSummary.subtotal);
    const totvat = num(orderSummary.totvat);
    const discount = num(orderSummary.discount);
    const shipping = num(orderSummary.shippingcharge);
    const pfee = num(orderSummary.pfee);
    const donation = num(orderSummary.donation);
    const grandTotal = num(orderSummary.total);

    const summary = [
      ['Total Orders', totalOrders],
      ['Total Quantity', totalQtySum],
      ['Subtotal (Incl. VAT)', subtotal],
      ['VAT', totvat],
      ['Discount', -discount],
      ['Shipping Charge', shipping],
      ['Processing Fee', pfee],
      ['Donation', donation],
      ['Grand Total', grandTotal],
      ['Total Cost', num(grandTotals.totalCost)],
      ['Total Profit', num(grandTotals.totalProfit)],
      ['Profit %', num(grandTotals.totalProfitPercent)]
    ]; // summary rows [6]

    // Render summary rows with merged label cells and numeric/percent formats
    summary.forEach(([label, value]) => {
      const row = worksheet.addRow([]);
      const r = row.number;
      worksheet.mergeCells(`A${r}:J${r}`); // label span [6]
      const labelCell = worksheet.getCell(`A${r}`);
      labelCell.value = label;
      labelCell.font = { bold: true };
      const valCell = worksheet.getCell(`K${r}`);
      if (label === 'Profit %') {
        valCell.value = Number(value) / 100;  // write as 0.1439 for 14.39% [11]
        valCell.numFmt = '0.00%';             // show as percentage [1][3]
      } else {
        valCell.value = num(value);
        valCell.numFmt = '#,##0.00';          // numeric formatting [11][14]
      }
      valCell.alignment = { horizontal: 'right' }; // right align values [7][13]
    });

    // Stream Excel
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'); // XLSX MIME [6]
    res.setHeader('Content-Disposition', `attachment; filename="DailyProfitReport-${new Date().toISOString().slice(0, 10)}.xlsx"`); // download name [6]
    await workbook.xlsx.write(res); // write to response [6]
    res.end(); // finish [6]
  } catch (error) {
    console.error('Excel Export Error:', error);
    res.status(500).send('Could not generate Excel file.');
  }
};
