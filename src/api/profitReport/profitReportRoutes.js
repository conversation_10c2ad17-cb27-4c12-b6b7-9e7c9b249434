import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderProfitReportPage,
    handleProfitReportApi,
    handleProfitReportExcel,
    renderDailyProfitReportPage,
    handleDailyProfitReportApi,
    handleDailyProfitReportExcel
} from './profitReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/profitReport', verifyToken, renderProfitReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/profitReport', verifyToken, handleProfitReportApi);

//FIXED: Corrected function name
router.get('/api/reports/profitReport/export', verifyToken, handleProfitReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/profit-report', verifyToken, (req, res) => {
    res.render('profit-report', {
        pageTitle: "Profit Report",
        user: req.user
    });
});

//DAILY PROFIT REPORT
router.get('/reports/dailyProfitReport', verifyToken, renderDailyProfitReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/dailyProfitReport', verifyToken, handleDailyProfitReportApi); 

//FIXED: Corrected function name
router.get('/api/reports/dailyProfitReport/export', verifyToken, handleDailyProfitReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/daily-profit-report', verifyToken, (req, res) => {
    res.render('daily-profit-report', {
        pageTitle: "Daily Profit Report",
        user: req.user
    });
});

export default router; // Export the configured router
