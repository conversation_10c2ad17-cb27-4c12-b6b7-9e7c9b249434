import db from '../../config/db.js';
import { getCategoryLineage } from '../../utils/commons.js';
import { getTableName } from '../helper/getTableName.js';

class ProfitReport {
  static async getProfitReport(filters = {}) {
    try {
      const {
        fromDate,
        toDate,
        based_on = 'delivery_date', // 'delivery_date' or 'created_date'
        country = 1,
        category,
        product,
        buyer,
        orderFrom,
      } = filters;

      const isDelivery = based_on === 'delivery_date';

      // 1) Build dynamic WHERE for the base orders CTE
      const baseFilters = ['oo.country_id = ?'];
      const baseParams = [parseInt(country, 10)];

      // Date column depends on basis
      const dateColumn = isDelivery ? 'fd.first_delivered_date' : 'oo.order_date';

      if (fromDate && toDate) {
        if (fromDate === toDate) {
          baseFilters.push(`DATE(${dateColumn}) = DATE(?)`);
          baseParams.push(fromDate);
        } else {
          baseFilters.push(`DATE(${dateColumn}) BETWEEN DATE(?) AND DATE(?)`);
          baseParams.push(fromDate, toDate);
        }
      } else if (fromDate) {
        baseFilters.push(`DATE(${dateColumn}) = DATE(?)`);
        baseParams.push(fromDate);
      }

      if (orderFrom && orderFrom.length > 0) {
        const arr = Array.isArray(orderFrom) ? orderFrom : [orderFrom];
        const placeholders = arr.map(() => '?').join(',');
        baseFilters.push(`oo.orderfrom IN (${placeholders})`);
        baseParams.push(...arr);
      }

      // 2) Optional product/category/buyer filters for the detail query
      const detailFilters = [];
      const detailParams = [];
      if (category) {
        detailFilters.push('cc.categoryid = ?');
        detailParams.push(category);
      }
      if (product) {
        detailFilters.push('cp.productid = ?');
        detailParams.push(product);
      }
      if (buyer) {
        detailFilters.push('cp.buyer_id = ?');
        detailParams.push(buyer);
      }

      // 3) Base CTEs for delivered vs created basis
      const withClause = isDelivery
        ? `
WITH first_delivered AS (
  SELECT oh.orderid, MIN(oh.statusdate) AS first_delivered_date
  FROM oms_order_history oh
  WHERE oh.order_statusid = 6
    AND oh.order_status_type = 'order_status'
  GROUP BY oh.orderid
),
base_orders AS (
  SELECT
    oo.orderid,
    oo.order_statusid,
    oo.shippingaddress,
    oo.order_ref_code,
    oo.country_id,
    oo.sub_total,
    oo.discount_amount,
    oo.total_amount,
    oo.donation_fee as donation,
    oo.shipping_charges,
    oo.processing_fee,
    oo.tax_amount,
    oo.order_date
  FROM oms_orders oo
  JOIN first_delivered fd ON fd.orderid = oo.orderid
  WHERE ${baseFilters.join(' AND ')}
    AND oo.shippingaddress NOT LIKE "%test%"
    AND oo.order_statusid > 0
)`
        : `
WITH base_orders AS (
  SELECT
    oo.orderid,
    oo.order_ref_code,
    oo.order_statusid,
    oo.shippingaddress,
    oo.country_id,
    oo.sub_total,
    oo.discount_amount,
    oo.total_amount,
    oo.donation_fee as donation,
    oo.shipping_charges,
    oo.processing_fee,
    oo.tax_amount,
    oo.order_date
  FROM oms_orders oo
    AND ${baseFilters.join(' AND ')}
    AND oo.shippingaddress NOT LIKE "%test%"
    AND oo.order_statusid > 0
)`; // Created basis

      // 4) Allocation CTEs
      const allocCtes = `
, order_line AS (
  SELECT
    ood.orderid,
    ood.product_sku,
    ood.productid,
    ood.quantity,
    (ood.selling_price * ood.quantity) AS line_subtotal,
    (ood.cost * ood.quantity) AS line_cost
  FROM oms_order_detail ood
  INNER JOIN base_orders bo ON bo.orderid = ood.orderid
),
order_line_sums AS (
  SELECT orderid, SUM(line_subtotal) AS order_subtotal
  FROM order_line
  GROUP BY orderid
),
alloc AS (
  SELECT
    ol.orderid,
    ol.product_sku,
    ol.productid,
    ol.quantity,
    ol.line_subtotal,
    ol.line_cost,
    bo.tax_amount AS line_vat
  FROM order_line ol
  JOIN order_line_sums s ON s.orderid = ol.orderid
  JOIN base_orders bo    ON bo.orderid = ol.orderid
)`;

      // 5) Detail query
      const detailSql = `
${withClause}
${allocCtes}
SELECT
  bo.orderid,
  COALESCE(cc.categoryid, 0)                       AS catid,
  COALESCE(cc.category_name, 'Uncategorized')      AS catname,
  COALESCE(cp.sku, alloc.product_sku)              AS product_code,
  ANY_VALUE(cp.productid)                          AS product_id,
  ANY_VALUE(COALESCE(cp.title, alloc.product_sku)) AS product_name,
  ANY_VALUE(au.firstname)                          AS buyer_name,
  ANY_VALUE(bo.country_id)                         AS country_id,
  SUM(alloc.quantity)                              AS total_quantity,
  SUM(alloc.line_cost)                             AS total_cost,
  SUM(alloc.line_subtotal)                         AS total_sales,
  SUM(alloc.line_vat)                              AS total_vat
FROM alloc
JOIN base_orders bo          ON bo.orderid = alloc.orderid
LEFT JOIN catalog_product cp ON cp.productid = alloc.productid
LEFT JOIN catalog_category cc ON cc.categoryid = cp.categoryid
LEFT JOIN aauth_users au      ON cp.buyer_id = au.id
WHERE bo.shippingaddress NOT LIKE "%test%"
  AND bo.order_statusid > 0
GROUP BY
  COALESCE(cc.categoryid, 0),
  COALESCE(cc.category_name, 'Uncategorized'),
  COALESCE(cp.sku, alloc.product_sku)
ORDER BY catname, product_name;`;

      const detailParamsAll = [...baseParams, ...detailParams];

      // 6) Summary with same filters
      const detailWhere = detailFilters.length ? `WHERE ${detailFilters.join(' AND ')}` : '';

      const summarySql = `
${withClause}
, lines_filtered AS (
  SELECT DISTINCT ood.orderid
  FROM oms_order_detail ood
  LEFT JOIN catalog_product   cp ON cp.productid  = ood.productid
  LEFT JOIN catalog_category  cc ON cc.categoryid = cp.categoryid
  ${detailWhere}
),
detail_sum AS (
  SELECT ood.orderid, SUM(ood.quantity) AS order_qty
  FROM oms_order_detail ood
  ${detailFilters.length ? 'JOIN lines_filtered lf ON lf.orderid = ood.orderid' : ''}
  GROUP BY ood.orderid
)
SELECT
  COUNT(DISTINCT bo.orderid) AS totalorder,
  COALESCE(SUM(ds.order_qty), 0) AS totalquantity,
  /* sub_total already includes tax_amount */
  SUM(bo.sub_total) AS subtotal,
  SUM(bo.discount_amount) AS discount,
  SUM(bo.donation) AS donation,
  SUM(bo.total_amount) AS total,
  SUM(bo.shipping_charges) AS shippingcharge,
  SUM(bo.processing_fee)   AS pfee,
  SUM(bo.tax_amount)       AS totvat
FROM base_orders bo
${detailFilters.length ? 'JOIN lines_filtered lf ON lf.orderid = bo.orderid' : ''}
LEFT JOIN detail_sum ds ON ds.orderid = bo.orderid;`;

      // Execute detail and summary
      const [rows] = await db.query(detailSql, detailParamsAll);
      const summaryParams = [...baseParams, ...detailParams];
      const [summaryRows] = await db.query(summarySql, summaryParams);

      // 7) Build categoryData and grandTotals
      const categoryMap = new Map();
      const grandTotals = {
        totalQuantity: 0,
        totalSales: 0,
        totalCost: 0,
        totalProfit: 0,
        totalVat: 0,
        totalWithoutVat: 0,
      };

      for (const r of rows) {
        const categ = await getCategoryLineage(db, r.catid);
        const profit = Number(r.total_sales) - Number(r.total_cost);
        // const profit = Number(r.total_sales) - Number(r.total_cost) - Number(r.discount);
        const profitPercent = profit / r.total_cost * 100;

        if (!categoryMap.has(categ?.category_id)) {
          categoryMap.set(categ?.category_id, {
            catid: categ?.category_id,
            catname: categ?.category_name,
            products: [],
            totals: {
              totalQuantity: 0,
              totalSales: 0,
              totalCost: 0,
              totalProfit: 0,
              totalVat: 0,
              totalWithoutVat: 0,
            },
          });
        }

        const cat = categoryMap.get(categ?.category_id);
        cat.products.push({
          productId: r.product_id ?? null,
          productName: r.product_name,
          productCode: r.product_code,
          buyerName: r.buyer_name ?? null,
          quantity: Number(r.total_quantity),
          totalPrice: Number(r.total_sales),
          totalCost: Number(r.total_cost),
          donation: r?.donation || 0,
          discount: r?.discount || 0,
          profit: profit,
          profitPercent: profitPercent.toFixed(2),
          vat: Number(r.total_vat).toFixed(2)
        });

        cat.totals.totalQuantity += Number(r.total_quantity);
        cat.totals.totalSales += Number(r.total_sales);
        cat.totals.totalCost += Number(r.total_cost);
        cat.totals.totalVat += Number(r.total_vat);
        cat.totals.totalProfit += profit;
      }

      const categoryData = Array.from(categoryMap.values());
      for (const cat of categoryData) {
        cat.totals.totalProfitPercent = (cat.totals.totalProfit / cat.totals.totalCost) * 100
        grandTotals.totalQuantity += cat.totals.totalQuantity;
        grandTotals.totalSales += cat.totals.totalSales;
        grandTotals.totalCost += cat.totals.totalCost;
        grandTotals.totalProfit += cat.totals.totalProfit;
        grandTotals.totalVat += cat.totals.totalVat;
      }
      grandTotals.totalProfitPercent = (grandTotals.totalProfit / grandTotals.totalCost) * 100

      // Summary (reconciled with detail filters)
      const orderSummary = summaryRows?.[0] || {
        totalorder: 0,
        totalquantity: 0,
        subtotal: 0,
        discount: 0,
        donation: 0,
        total: 0,
        shippingcharge: 0,
        pfee: 0,
        totvat: 0,
      };

      return { categoryData, grandTotals, orderSummary, salesReturn: 0 };
    } catch (err) {
      console.error('getProfitReport error:', err);
      throw err;
    }
  }

  static async getDailyProfitReport(filters = {}) {
    try {
      const {
        fromDate,
        toDate,
        country = 1,
        buyer,
        orderFrom,
        status,   // Optional status mapping
        category, // Optional product/category/buyer filters
        product
      } = filters;

      const statusFieldMap = {
        Pending: [1, 2],
        Invoiced: [3],
        Despatch: [5],
        Delivered: [6],
        Cancelled: [7, 11],
        Return: [8, 9],
      };

      const isDelivery = false;

      // Base WHERE for oms_orders
      const baseFilters = ['oo.country_id = ?'];
      const baseParams = [parseInt(country, 10)];

      const dateColumn = isDelivery ? 'fd.first_delivered_date' : 'oo.order_date';

      if (fromDate && toDate) {
        if (fromDate === toDate) {
          baseFilters.push(`DATE(${dateColumn}) = DATE(?)`);
          baseParams.push(fromDate);
        } else {
          baseFilters.push(`DATE(${dateColumn}) BETWEEN DATE(?) AND DATE(?)`);
          baseParams.push(fromDate, toDate);
        }
      } else if (fromDate) {
        baseFilters.push(`DATE(${dateColumn}) = DATE(?)`);
        baseParams.push(fromDate);
      }

      if (orderFrom && orderFrom.length > 0) {
        const arr = Array.isArray(orderFrom) ? orderFrom : [orderFrom];
        const placeholders = arr.map(() => '?').join(',');
        baseFilters.push(`oo.orderfrom IN (${placeholders})`);
        baseParams.push(...arr);
      }

      if (status && statusFieldMap[status]) {
        const ids = statusFieldMap[status];
        const placeholders = ids.map(() => '?').join(',');
        baseFilters.push(`oo.order_statusid IN (${placeholders})`);
        baseParams.push(...ids);
      }

      const detailFilters = [];
      const detailParams = [];
      if (category) {
        detailFilters.push('cc.categoryid = ?');
        detailParams.push(category);
      }
      if (product) {
        detailFilters.push('cp.productid = ?');
        detailParams.push(product);
      }
      if (buyer) {
        detailFilters.push('cp.buyer_id = ?');
        detailParams.push(buyer);
      }

      const withClause = isDelivery
        ? `
WITH first_delivered AS (
  SELECT oh.orderid, MIN(oh.statusdate) AS first_delivered_date
  FROM oms_order_history oh
  WHERE oh.order_statusid = 6
    AND oh.order_status_type = 'order_status'
  GROUP BY oh.orderid
),
base_orders AS (
  SELECT
    oo.orderid,
    oo.order_statusid,
    oo.order_ref_code,
    oo.shippingaddress,
    oo.country_id,
    oo.sub_total,
    oo.discount_amount,
    oo.total_amount,
    oo.donation_fee      AS donation,
    oo.shipping_charges,
    oo.processing_fee,
    oo.tax_amount,
    oo.order_date
  FROM oms_orders oo
  JOIN first_delivered fd ON fd.orderid = oo.orderid
  WHERE ${baseFilters.join(' AND ')}
    AND oo.order_statusid > 0
    AND oo.type = 'order'
    AND oo.shippingaddress NOT LIKE '%test%'
)`
        : `
WITH base_orders AS (
  SELECT
    oo.orderid,
    oo.order_ref_code,
    oo.order_statusid,
    oo.shippingaddress,
    oo.country_id,
    oo.sub_total,
    oo.discount_amount,
    oo.total_amount,
    oo.shipping_charges,
    oo.processing_fee,
    oo.tax_amount,
    oo.donation_fee      AS donation,
    oo.order_date
  FROM oms_orders oo
  WHERE ${baseFilters.join(' AND ')}
    AND oo.order_statusid > 0
    AND oo.type = 'order'
    AND oo.shippingaddress NOT LIKE '%test%'
)`;

      const allocCtes = `
, order_line AS (
  SELECT
    ood.orderid,
    ood.product_sku,
    ood.productid,
    ood.quantity,
    (ood.selling_price * ood.quantity) AS line_subtotal,
    (ood.cost * ood.quantity)          AS line_cost
  FROM oms_order_detail ood
  INNER JOIN base_orders bo ON bo.orderid = ood.orderid
),
order_line_sums AS (
  SELECT orderid, SUM(line_subtotal) AS order_subtotal
  FROM order_line
  GROUP BY orderid
),
alloc AS (
  SELECT
    ol.orderid,
    ol.product_sku,
    ol.productid,
    ol.quantity,
    ol.line_subtotal,
    ol.line_cost,
    /* Pro-rate header values to lines by share of order_subtotal; avoid division by zero */
    CASE
      WHEN s.order_subtotal > 0
        THEN ROUND(bo.tax_amount * (ol.line_subtotal / s.order_subtotal), 6)
      ELSE 0
    END AS line_vat,
    CASE
      WHEN s.order_subtotal > 0
        THEN ROUND(bo.discount_amount * (ol.line_subtotal / s.order_subtotal), 6)
      ELSE 0
    END AS line_discount,
    CASE
      WHEN s.order_subtotal > 0
        THEN ROUND(bo.donation * (ol.line_subtotal / s.order_subtotal), 6)
      ELSE 0
    END AS line_donation,
    CASE
      WHEN s.order_subtotal > 0
        THEN ROUND(bo.shipping_charges * (ol.line_subtotal / s.order_subtotal), 6)
      ELSE 0
    END AS line_shipping,
    CASE
      WHEN s.order_subtotal > 0
        THEN ROUND(bo.processing_fee * (ol.line_subtotal / s.order_subtotal), 6)
      ELSE 0
    END AS line_processing
  FROM order_line ol
  JOIN order_line_sums s ON s.orderid = ol.orderid
  JOIN base_orders bo    ON bo.orderid = ol.orderid
)`;

      // Detail WHERE (applied to report)
      const whereDetail = [];
      if (detailFilters.length) whereDetail.push(detailFilters.join(' AND '));
      whereDetail.push(`bo.shippingaddress NOT LIKE '%test%'`);
      whereDetail.push(`bo.order_statusid > 0`);
      const whereDetailSql = `WHERE ${whereDetail.join(' AND ')}`;

      // CATEGORY/PRODUCT REPORT (detail)
      const reportSql = `
${withClause}
${allocCtes}
SELECT
  COALESCE(cc.categoryid, 0)                      AS catid,
  COALESCE(cc.category_name, 'Uncategorized')     AS catname,
  COALESCE(cp.sku, alloc.product_sku)             AS product_code,
  ANY_VALUE(cp.productid)                         AS product_id,
  ANY_VALUE(COALESCE(cp.title, alloc.product_sku))AS product_name,
  ANY_VALUE(au.firstname)                         AS buyer_name,
  ANY_VALUE(bo.country_id)                        AS country_id,
  SUM(alloc.quantity)                             AS total_quantity,
  SUM(alloc.line_cost)                            AS total_cost,
  SUM(alloc.line_subtotal)                        AS total_sales,
  SUM(alloc.line_vat)                             AS total_vat,
  SUM(alloc.line_discount)                        AS total_discount,
  SUM(alloc.line_donation)                        AS total_donation,
  SUM(alloc.line_shipping)                        AS total_shipping,
  SUM(alloc.line_processing)                      AS total_processing
FROM alloc
JOIN base_orders bo           ON bo.orderid = alloc.orderid
LEFT JOIN catalog_product  cp ON cp.productid  = alloc.productid
LEFT JOIN catalog_category cc ON cc.categoryid = cp.categoryid
LEFT JOIN aauth_users      au ON cp.buyer_id   = au.id
${whereDetailSql}
GROUP BY
  COALESCE(cc.categoryid, 0),
  COALESCE(cc.category_name, 'Uncategorized'),
  COALESCE(cp.sku, alloc.product_sku)
ORDER BY catname, product_name;`;

      // SUMMARY built off the same alloc CTE so filters/rounding match report
      const detailWhere = detailFilters.length ? `WHERE ${detailFilters.join(' AND ')}` : '';
      const summarySql = `
${withClause}
${allocCtes}
, filtered_alloc AS (
  SELECT a.*
  FROM alloc a
  LEFT JOIN catalog_product  cp ON cp.productid  = a.productid
  LEFT JOIN catalog_category cc ON cc.categoryid = cp.categoryid
  ${detailWhere}
)
SELECT
  COUNT(DISTINCT fa.orderid)          AS totalorder,
  COALESCE(SUM(fa.quantity), 0)       AS totalquantity,
  SUM(bo.sub_total)                   AS subtotal,
  SUM(bo.discount_amount)             AS discount,
  SUM(bo.donation)                    AS donation,
  SUM(bo.total_amount)                AS total,
  SUM(bo.shipping_charges)            AS shippingcharge,
  SUM(bo.processing_fee)              AS pfee,
  COALESCE(SUM(fa.line_vat), 0)       AS totvat
FROM base_orders bo
JOIN filtered_alloc fa ON fa.orderid = bo.orderid;`;

      const detailParamsAll = [...baseParams, ...detailParams];
      const [flatReportData] = await db.query(reportSql, detailParamsAll);

      const summaryParams = [...baseParams, ...detailParams];
      const [summaryRows] = await db.query(summarySql, summaryParams);

      // Aggregate in JS
      const categoryMap = new Map();
      const grandTotals = {
        totalQuantity: 0,
        totalSales: 0,
        totalCost: 0,
        totalProfit: 0,
        totalVat: 0,
        totalWithoutVat: 0,
        discount: 0,
        donation: 0,
        shipping: 0,
        processing: 0,
      };

      for (const row of flatReportData) {
        const categ = await getCategoryLineage(db, row?.catid);
        const profit = Number(row.total_sales) - Number(row.total_cost);
        const profitPercent = Number(row.total_cost) > 0 ? (profit / Number(row.total_cost)) * 100 : 0;

        if (!categoryMap.has(categ?.category_id)) {
          categoryMap.set(categ?.category_id, {
            catid: categ?.category_id,
            catname: categ?.category_name,
            products: [],
            totals: {
              totalQuantity: 0,
              totalSales: 0,
              totalCost: 0,
              totalProfit: 0,
              totalVat: 0,
              totalWithoutVat: 0,
              discount: 0,
              donation: 0,
              shipping: 0,
              processing: 0,
            },
          });
        }

        const category = categoryMap.get(categ?.category_id);

        const vat = Number(row.total_vat) || 0;
        const discount = Number(row.total_discount) || 0;
        const donation = Number(row.total_donation) || 0;
        const shipping = Number(row.total_shipping) || 0;
        const processing = Number(row.total_processing) || 0;

        category.products.push({
          productId: row.product_id,
          productName: row.product_name,
          productCode: row.product_code,
          buyerName: row.buyer_name,
          quantity: Number(row.total_quantity),
          totalPrice: Number(row.total_sales),
          totalCost: Number(row.total_cost),
          profit,
          profitPercent: profitPercent.toFixed(2),
          vat: vat.toFixed(2),
          discount: discount.toFixed(2),
          donation: donation.toFixed(2),
          shipping: shipping.toFixed(2),
          processing: processing.toFixed(2),
        });

        category.totals.totalQuantity += Number(row.total_quantity);
        category.totals.totalSales += Number(row.total_sales);
        category.totals.totalCost += Number(row.total_cost);
        category.totals.totalVat += vat;
        category.totals.totalProfit += profit;
        category.totals.totalWithoutVat += Number(row.total_sales) - vat;
        category.totals.discount += discount;
        category.totals.donation += donation;
        category.totals.shipping += shipping;
        category.totals.processing += processing;
      }

      const detailedCategoryData = Array.from(categoryMap.values());
      detailedCategoryData.forEach((cat) => {
        cat.totals.totalProfitPercent =
          cat.totals.totalCost > 0 ? (cat.totals.totalProfit / cat.totals.totalCost) * 100 : 0;

        grandTotals.totalQuantity += cat.totals.totalQuantity;
        grandTotals.totalSales += cat.totals.totalSales;
        grandTotals.totalCost += cat.totals.totalCost;
        grandTotals.totalProfit += cat.totals.totalProfit;
        grandTotals.totalVat += cat.totals.totalVat;
        grandTotals.totalWithoutVat += cat.totals.totalWithoutVat;
        grandTotals.discount += cat.totals.discount;
        grandTotals.donation += cat.totals.donation;
        grandTotals.shipping += cat.totals.shipping;
        grandTotals.processing += cat.totals.processing;
      });

      grandTotals.totalProfitPercent =
        grandTotals.totalCost > 0 ? (grandTotals.totalProfit / grandTotals.totalCost) * 100 : 0;

      const orderSummary = summaryRows?.[0] || {
        totalorder: 0,
        totalquantity: 0,
        discount: 0,
        donation: 0,
        subtotal: 0,
        total: 0,
        shippingcharge: 0,
        pfee: 0,
        totvat: 0,
      };

      return { categoryData: detailedCategoryData, grandTotals, orderSummary };
    } catch (err) {
      console.error('getDailyProfitReport error:', err);
      throw err;
    }
  }

}

export default ProfitReport;
