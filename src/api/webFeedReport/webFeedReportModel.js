import db from '../../config/db.js';

class WebFeedReport {

    static async getReport(filters) {
        const { country, status, fromDate, toDate } = filters;

        if (!country) {
            return { reportData: [] };
        }

        // Build dynamic WHERE
        const conditions = [];
        const params = [];

        // Country filter: accept numeric id (ow.country_id) or name (ac.name)
        if (!Number.isNaN(Number(country))) {
            conditions.push('ow.country_id = ?');
            params.push(Number(country));
        } else {
            conditions.push('ac.name = ?');
            params.push(String(country));
        }

        // Status filter (exact when provided; else default list)
        if (status) {
            conditions.push('ow.status = ?');
            params.push(status);
        }

        // Date filters on DATE(ow.order_date)
        if (fromDate && toDate) {
            conditions.push('DATE(ow.order_date) BETWEEN Date(?) AND Date(?)');
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions.push('DATE(ow.order_date) = ?');
            params.push(fromDate);
        }

        // Exclude test customers as in sample
        conditions.push('ow.customer NOT LIKE "%test%"');

        const whereClause = conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';

        const sql = `
    WITH latest_comments AS (
      SELECT
        webfeed_id,
        comment,
        status_message,
        quick_status,
        ROW_NUMBER() OVER (PARTITION BY webfeed_id ORDER BY date DESC) AS rn
      FROM oms_webfeed_comments
    )
    SELECT
      ow.webfeed_id,
      ow.customer,
      ac.name AS country_name,
      ow.order_from,
      DATE(ow.order_date) AS order_date,
      ow.status,
      cp.title AS product_name,
      cp.sku AS sku,
      ow.price,
      ow.quantity,
      owc.comment,
      ow.mobileno,
      owc.quick_status
    FROM oms_webfeed AS ow
    LEFT JOIN latest_comments AS owc
      ON ow.webfeed_id = owc.webfeed_id AND owc.rn = 1
    LEFT JOIN catalog_product AS cp
      ON ow.product_code = cp.sku
    LEFT JOIN admin_country AS ac
      ON ow.country_id = ac.country_id
    ${whereClause}
    ORDER BY ow.webfeed_id asc;
  `;
console.log(db.format(sql, params));
        const [reportData] = await db.query(sql, params);
        return { reportData };
    }
}

export default WebFeedReport;