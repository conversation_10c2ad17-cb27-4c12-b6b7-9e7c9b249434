import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderWebFeedReportPage,
    handleWebFeedReportApi,
    handleWebFeedReportExcel
} from '../webFeedReport/webFeedReportController.js';

const router = express.Router();

router.get('/web-feed-report', verifyToken, (req, res) => {
    res.render('web-feed-report', {
        pageTitle: "WEB FEED REPORT",
        user: req.user
    });
});

router.get('/web-feed-report', verifyToken, renderWebFeedReportPage);

router.get('/api/reports/web-feed', verifyToken, handleWebFeedReportApi);

router.get('/api/reports/web-feed/export', verifyToken, handleWebFeedReportExcel);

export default router;