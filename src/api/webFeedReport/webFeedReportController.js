import WebFeedReport from '../webFeedReport/webFeedReportModel.js';
import exceljs from 'exceljs';

export const renderWebFeedReportPage = (req, res) => {
    res.render('web-feed-report', { user: req.user, pageTitle: "Web Feed Report" });
};

export const handleWebFeedReportApi = async (req, res) => {
    try {
        const filters = req.query;

        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required filter." });
        }

        const data = await WebFeedReport.getReport(filters);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching web feed report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

export const handleWebFeedReportExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { reportData } = await WebFeedReport.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('WebFeedReport');

        worksheet.columns = [
            { header: 'Sl. No.', key: 'sl_no', width: 10 },
            { header: 'Customer Name', key: 'customer', width: 25 },
            { header: 'Mobile No', key: 'mobileno', width: 20 },
            { header: 'Product', key: 'product_name', width: 50 },
            { header: 'SKU', key: 'sku', width: 15 },
            { header: 'Quantity', key: 'quantity', width: 10 },
            { header: 'Price/Pc.', key: 'price', width: 10 },
            { header: 'Date', key: 'order_date', width: 25 },
            { header: 'Order From', key: 'order_from', width: 15 },
            { header: 'Status', key: 'status', width: 15 },
            { header: 'Comment', key: 'comment', width: 40 },
            { header: 'Status Message', key: 'quick_status', width: 40 }
        ];

        worksheet.getRow(1).font = { bold: true };

        reportData.forEach((row, index) => {
            worksheet.addRow({
                sl_no: index + 1,
                ...row,
                order_date: new Date(row.order_date).toLocaleString('en-GB', { timeZone: 'Asia/Dubai' })
            });
        });

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="WebFeedReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error){
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};