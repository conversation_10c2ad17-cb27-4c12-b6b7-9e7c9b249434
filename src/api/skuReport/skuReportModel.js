import db from '../../config/db.js';

class SkuReportOld {
    static async getSkuReport(filters) {
        const { country: countryId, buyer, category, subcategory, subsubcategory, brand, agent, fromDate, toDate} = filters;
        if (!countryId) return { products: [] };

        let conditions = [];
        const params = [];

        // Buyer filter
        if (buyer) { 
            conditions.push('p.buyer_id = ?'); 
            params.push(buyer); 
        }

        
        // brandfilter
        if (brand) { 
            conditions.push('p.brandid = ?'); 
            params.push(brand); 
        }

         
        // brandfilter
        if (agent) { 
            conditions.push('p.created_by = ?'); 
            params.push(agent); 
        }

        if (filters.fromDate && filters.toDate) {
            conditions.push(`DATE(created_date) BETWEEN DATE(?) AND DATE(?)`);
            params.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            conditions.push(`DATE(created_date) = DATE(?)`);
            params.push(filters.fromDate);
        }


      // Category hierarchy filters
        if (subsubcategory) {
            conditions.push('c1.categoryid = ?');  // product assigned at sub-sub level
            params.push(subsubcategory);
        } else if (subcategory) {
            conditions.push('c2.categoryid = ?');  // product under this subcategory (and its sub-subs)
            params.push(subcategory);
        } else if (category) {
            conditions.push('c3.categoryid = ?');  // product under this main category (and all subs)
            params.push(category);
        }



        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // --- Fetch all product details ---
        const baseQuery = `SELECT 
            p.productid AS id,
            p.sku AS product_code,
            p.title AS name,
            inv.selling_price AS price,
            inv.cost AS cost,
            p.model_no AS product_model,
            b.brand_name AS brand,
            p.created_date,
            CONCAT(u.firstname, ' ', u.lastname) AS buyer,
            inv.rstatus AS status,
            inv.inventory AS stock_in,
            CASE 
                WHEN c3.category_name IS NOT NULL THEN c3.category_name
                WHEN c2.category_name IS NOT NULL THEN c2.category_name
                ELSE c1.category_name
            END AS category,
            CASE 
                WHEN c3.category_name IS NOT NULL THEN c2.category_name
                WHEN c2.category_name IS NOT NULL THEN c2.category_name
                ELSE NULL
            END AS subcategory,
            CASE 
                WHEN c3.category_name IS NOT NULL THEN c1.category_name
                ELSE NULL
            END AS subsubcategory
        FROM catalog_product p
        INNER JOIN catalog_product_inventory inv 
            ON p.productid = inv.productid 
            AND inv.country_id = ${countryId}
        LEFT JOIN catalog_category c1 
            ON p.categoryid = c1.categoryid
        LEFT JOIN catalog_category c2 
            ON c1.categorypid = c2.categoryid
        LEFT JOIN catalog_category c3 
            ON c2.categorypid = c3.categoryid
        LEFT JOIN catalog_brand b 
            ON p.brandid = b.brandid
        LEFT JOIN aauth_users u
            ON p.buyer_id = u.id
        ${whereClause}
        ORDER BY product_code DESC;`;


        const [products] = await db.query(baseQuery, params);
        if (products.length === 0) {
            return { products: [] };
        }

        // --- Map all columns from query ---
        const reportProducts = products.map((p, index) => ({
            sr_no: index + 1,
            id: p.id,
            product_code: p.product_code,
            product_name: p.name,
            price: p.price,
            cost: p.cost,
            product_model: p.product_model,
            brand: p.brand,
            buyer: p.buyer,
            status: p.status,
            category: p.category,
            subcategory: p.subcategory,
            subsubcategory: p.subsubcategory,
            created_date: p.created_date
        }));

        return { products: reportProducts };
    }
}

class SkuReport {
    static async getSkuReport(filters) {
        const { country: countryId, buyer, category, subcategory, subsubcategory, brand, agent, fromDate, toDate } = filters;
        if (!countryId) return { products: [], summary: null };

        let conditions = [];
        const params = [];

        // Buyer filter
        if (buyer) { 
            conditions.push('p.buyer_id = ?'); 
            params.push(buyer); 
        }

        // Brand filter
        if (brand) { 
            conditions.push('p.brandid = ?'); 
            params.push(brand); 
        }

        // Agent filter
        if (agent) { 
            conditions.push('p.created_by = ?'); 
            params.push(agent); 
        }

        // Date filters
        if (fromDate && toDate) {
            conditions.push(`DATE(p.created_date) BETWEEN DATE(?) AND DATE(?)`);
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions.push(`DATE(p.created_date) = DATE(?)`);
            params.push(fromDate);
        }

        // Category hierarchy filters
        if (subsubcategory) {
            // Leaf level selected → only products in that leaf
            conditions.push('c1.categoryid = ?');
            params.push(subsubcategory);
        } else if (subcategory) {
            // Parent level selected → products in that parent OR its children
            conditions.push('(c2.categoryid = ? OR c1.categoryid = ?)');
            params.push(subcategory, subcategory);
        } else if (category) {
            // Grandparent selected → products in that grandparent OR its children (parent + leaf)
            conditions.push('(c3.categoryid = ? OR c2.categoryid = ? OR c1.categoryid = ?)');
            params.push(category, category, category);
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // --- Query 1: Fetch detailed products ---
        const baseQuery = `
            SELECT 
                p.productid AS id,
                p.sku AS product_code,
                p.title AS name,
                inv.selling_price AS price,
                inv.cost AS cost,
                p.model_no AS product_model,
                b.brand_name AS brand,
                p.created_date,
                CONCAT(u.firstname, ' ', u.lastname) AS buyer,
                inv.rstatus AS status,
                inv.inventory AS stock_in,
                CASE 
                    WHEN c3.category_name IS NOT NULL THEN c3.category_name
                    WHEN c2.category_name IS NOT NULL THEN c2.category_name
                    ELSE c1.category_name
                END AS category,
                CASE 
                    WHEN c3.category_name IS NOT NULL THEN c2.category_name
                    WHEN c2.category_name IS NOT NULL THEN c2.category_name
                    ELSE NULL
                END AS subcategory,
                CASE 
                    WHEN c3.category_name IS NOT NULL THEN c1.category_name
                    ELSE NULL
                END AS subsubcategory
            FROM catalog_product p
            INNER JOIN catalog_product_inventory inv 
                ON p.productid = inv.productid 
                AND inv.country_id = ${countryId}
            LEFT JOIN catalog_category c1 
                ON p.categoryid = c1.categoryid
            LEFT JOIN catalog_category c2 
                ON c1.categorypid = c2.categoryid
            LEFT JOIN catalog_category c3 
                ON c2.categorypid = c3.categoryid
            LEFT JOIN catalog_brand b 
                ON p.brandid = b.brandid
            LEFT JOIN aauth_users u
                ON p.buyer_id = u.id
            ${whereClause}
            ORDER BY product_code DESC;
        `;


        // --- Query 2: Fetch summary counts ---
        const summaryQuery = `
            SELECT 
                COUNT(*) AS total_skus,
                SUM(CASE WHEN inv.rstatus = 1 THEN 1 ELSE 0 END) AS active_skus,
                SUM(CASE WHEN inv.rstatus = 0 THEN 1 ELSE 0 END) AS inactive_skus
            FROM catalog_product p
            INNER JOIN catalog_product_inventory inv 
                ON p.productid = inv.productid 
               AND inv.country_id = ?
        `;

        // Execute queries
        const [products] = await db.query(baseQuery, params);
        const [summaryRows] = await db.query(summaryQuery, [countryId]);
        const summary = summaryRows[0] || { total_skus: 0, active_skus: 0, inactive_skus: 0 };

        if (products.length === 0) {
            return { products: [], summary };
        }

        // Map products
        const reportProducts = products.map((p, index) => ({
            sr_no: index + 1,
            id: p.id,
            product_code: p.product_code,
            product_name: p.name,
            price: p.price,
            cost: p.cost,
            product_model: p.product_model,
            brand: p.brand,
            buyer: p.buyer,
            status: p.status,
            category: p.category,
            subcategory: p.subcategory,
            subsubcategory: p.subsubcategory,
            created_date: p.created_date
        }));

        // Return both products and summary counts
        return { 
            products: reportProducts, 
            summary 
        };
    }
}




export default SkuReport;