import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderSkuReportPage,
    handleSkuReportApi,
    handleSkuReportExcel
} from '../skuReport/skuReportController.js';

const router = express.Router(); // Create router instance

// ===============================================
// STOCK REPORT ROUTES
// ===============================================

// Render the Stock Report EJS page
router.get('/sku-report', verifyToken, (req, res) => {
    res.render('sku-report', {
        pageTitle: "SKU REPORT",
        user: req.user
    });
});

// Render Stock Report page (used internally by layout or routes)
router.get('/reports/skuReports', verifyToken, renderSkuReportPage);

// API: Fetch Stock Report data in JSON format
router.get('/api/reports/skuReports', verifyToken, handleSkuReportApi);

// API: Export Stock Report data to Excel
router.get('/api/reports/skuReports/export', verifyToken, handleSkuReportExcel);

export default router;
