import Sku from '../skuReport/skuReportModel.js';
import exceljs from 'exceljs';
import db from '../../config/db.js'; // Assuming db is in config

// =====================================================
// RENDER STOCK REPORT PAGE
// =====================================================
export const renderSkuReportPage = (req, res) => {
    res.render('sku-report', { user: req.user, pageTitle: "SKU Report" });
};

// =====================================================
// API: GET STOCK REPORT DATA
// =====================================================
export const handleSkuReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Country is mandatory for this report
        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required filter." });
        }

        const data = await Sku.getSkuReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching sku report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// =====================================================
// API: EXPORT STOCK REPORT TO EXCEL
// =====================================================
export const handleSkuReportExcel = async (req, res) => {
    try {
        const filters = req.query;

        // Fetch full SKU report data
        const { products } = await Sku.getSkuReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('SKU Report');

        // Define all column headers and keys
        worksheet.columns = [
            { header: 'Sr. No.', key: 'sr_no', width: 10 },
            { header: 'SKU', key: 'product_code', width: 25 },
            { header: 'Product Name', key: 'product_name', width: 40 },
            { header: 'Price', key: 'price', width: 15, style: { numFmt: '0.00' } },
            { header: 'Cost', key: 'cost', width: 15, style: { numFmt: '0.00' } },
            { header: 'Category', key: 'category', width: 20 },
            { header: 'Sub Category', key: 'subcategory', width: 20 },
            { header: 'Sub Sub Category', key: 'subsubcategory', width: 20 },
            { header: 'Product Model', key: 'product_model', width: 20 },
            { header: 'Brand', key: 'brand', width: 20 },
            { header: 'Buyer', key: 'buyer', width: 25 },
             { header: 'Created Date', key: 'created_date', width: 25 },
            { header: 'Status', key: 'status', width: 10 }
        ];

        // Make header bold
        worksheet.getRow(1).font = { bold: true };

        // Append all product rows
        products.forEach(product => {
            worksheet.addRow({
                sr_no: product.sr_no,
                product_code: product.product_code,
                product_name: product.product_name,
                price: product.price,
                cost: product.cost,
                category: product.category,
                subcategory: product.subcategory,
                subsubcategory: product.subsubcategory,
                product_model: product.product_model,
                brand: product.brand,
                buyer: product.buyer,
                created_date: product.created_date,
                status: product.status
            });
        });

        // Set response headers for Excel download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="SKUReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};

