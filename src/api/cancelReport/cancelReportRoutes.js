import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderCancelReportPage,
    handleCancelReportApi,
    handleCancelReportExcel
} from './cancelReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/cancelReport', verifyToken, renderCancelReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/cancelReport', verifyToken, handleCancelReportApi);

//FIXED: Corrected function name
router.get('/api/reports/cancelReport/export', verifyToken, handleCancelReportExcel);

// Order report EJS route (can be removed, since handled below)
router.get('/cancel-report', verifyToken, (req, res) => {
    res.render('cancel-report', {
        pageTitle: "Cancel Report",
        user: req.user
    });
});

export default router; // Export the configured router
