import db from '../../config/db.js';
import mysql from 'mysql2/promise';
import { getCancelStatusEnum } from '../../utils/commons.js'

export const handleCancelStatus = (cancel_status) => {
    return getCancelStatusEnum(cancel_status)
}

class CancelOrder {
    static async getCancelOrderReport(filters) {
        const conditions = [];
        const params = [];

        // --- Dynamic Filters (for outer WHERE only) ---
        if (filters.country) {
            conditions.push(`oo.country_id = ?`);
            params.push(filters.country);
        }

        // Always exclude test names
        conditions.push(`
            (
                JSON_VALID(oo.customer_details) = 0
                OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
            )
        `);

        // Final WHERE for outer query
        const whereClause = conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';

        // -------------------------------
        // Data Query (uses ooh join + whereClause)
        // -------------------------------
        const dataConditions = [];
        const dataParams = [...params];

        if (filters.country) {
            dataConditions.push(`oo.country_id = ?`);
        }

        dataConditions.push(`ooh.order_statusid IN (7,11)`);
        dataConditions.push(`ooh.order_status_type = "order_status"`);

        if (filters.fromDate && filters.toDate) {
            dataConditions.push(`DATE(ooh.statusdate) BETWEEN DATE(?) AND DATE(?)`);
            dataParams.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            dataConditions.push(`DATE(ooh.statusdate) = DATE(?)`);
            dataParams.push(filters.fromDate);
        }

        dataConditions.push(`
            (
                JSON_VALID(oo.customer_details) = 0
                OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
            )
        `);

        const dataWhere = dataConditions.length ? `WHERE ${dataConditions.join(' AND ')}` : '';

        const dataQuery = `
            SELECT
                oo.order_ref_code as orderid,
                oo.country_id,
                ac.name,
                oo.staff_id,
                oo.processing_fee,
                oo.order_status,
                oo.notes AS remarks,
                oo.gway_transaction_id AS fort_id,
                MAX(CASE WHEN ooh.order_statusid = 3 AND ooh.order_status_type = "order_status" THEN ooh.statusdate END) AS despatch_date,
                oo.charge,
                MAX(CASE WHEN ooh.order_statusid IN (7, 11) AND ooh.order_status_type = "order_status" THEN ooh.status END) AS cancel_status,
                MAX(CASE WHEN ooh.order_statusid IN (7, 11) AND ooh.order_status_type = "order_status" THEN ooh.statusdate END) AS cancel_date,
                oo.delivery_date,
                oo.total_amount,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.mobile')) END AS contact,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')) END AS customer,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.gender')) END AS gender,
                oo.order_date,
                oo.orderfrom,
                driver.firstname AS delivery_agent,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.emirate')) END AS emirate_name,
                oo.payment_methodid AS payment_type,
                DATEDIFF(
                    MAX(CASE WHEN ooh.order_statusid IN (7, 11) AND ooh.order_status_type = "order_status" THEN ooh.statusdate END),
                    oo.order_date
                ) AS durdays,
                oo.total_amount AS display_amount,
                oo.payment_method AS payment_mode,
                MAX(CASE WHEN ooh.order_statusid IN (7, 11) AND ooh.order_status_type = "order_status" THEN ooh.staff_comments END) AS cancel_reason,
                MAX(CASE WHEN oo.order_statusid IN (7, 11) AND ooh.order_status_type = "order_status" THEN cancel_user.firstname END) AS cancelled_by
            FROM oms_orders AS oo
            LEFT JOIN oms_order_history AS ooh ON oo.orderid = ooh.orderid
            LEFT JOIN aauth_users AS driver ON oo.driver_id = driver.id
            LEFT JOIN aauth_users AS cancel_user ON oo.staff_id = cancel_user.id
            LEFT JOIN admin_country AS ac ON oo.country_id = ac.country_id
            ${dataWhere}
            GROUP BY oo.orderid
            ORDER BY oo.order_date ASC
        `;

        // -------------------------------
        // Summary Query (date filter only in subquery)
        // -------------------------------
        const subConditions = [`order_statusid IN (7,11)`, `order_status_type = 'order_status'`];
        const subParams = [];

        if (filters.fromDate && filters.toDate) {
            subConditions.push(`DATE(statusdate) BETWEEN DATE(?) AND DATE(?)`);
            subParams.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            subConditions.push(`DATE(statusdate) = DATE(?)`);
            subParams.push(filters.fromDate);
        }

        const subWhere = `WHERE ${subConditions.join(' AND ')}`;

        const summaryQuery = `
            SELECT
                COUNT(*) AS totalOrders,
                SUM(oo.sub_total) AS rawTotalAmount,
                SUM(oo.tax_amount) AS vat,
                SUM(oo.shipping_charges) AS shippingCharge,
                SUM(oo.processing_fee) AS processingFees,
                SUM(oo.discount_amount) AS discountAmount
            FROM oms_orders oo
            JOIN (
                SELECT DISTINCT orderid
                FROM oms_order_history
                ${subWhere}
            ) oh ON oo.orderid = oh.orderid
            ${whereClause}
        `;

        // Debug helper
        function formatQuery(query, params) {
            let i = 0;
            return query.replace(/\?/g, () => {
                const val = params[i++];
                if (val === null) return 'NULL';
                if (typeof val === 'number') return val;
                return `'${val}'`;
            });
        }

        // console.log('Final dataQuery:', formatQuery(dataQuery, dataParams));
        // console.log('Final summaryQuery:', formatQuery(summaryQuery, [...subParams, ...params]));
        // console.log('params data:', dataParams);
        // console.log('params summary:', [...subParams, ...params]);

        // Run queries
        const [orders] = await db.query(dataQuery, dataParams);
        const [summaryRows] = await db.query(summaryQuery, [...subParams, ...params]);

        // Totals
        const totals = summaryRows[0] || {};
        if (totals.rawTotalAmount) {
            const rawTotal = parseFloat(totals.rawTotalAmount);
            const fees = parseFloat(totals.processingFees || 0);
            const totalVat = parseFloat(totals.vat || 0);
            const totalDiscount = parseFloat(totals.discountAmount || 0);
            const totalShipping = parseFloat(totals.shippingCharge || 0);

            totals.totalAmount = rawTotal - (fees + totalVat + totalDiscount);
            totals.grandTotal = rawTotal + totalShipping;
        }

        return { orders, totals };
    }

    static async getCancelOrderReportV2(filters) {
        console.log('Filters received:', filters);
        const conditions = [];
        const params = [];
        const { sender, paymode, paymentGatewayType, cancel_status, status, orderFrom, based_on, staffName, cancelled_staffName, cancelReason, duration, emirate, area } = filters;

        // --- Dynamic Filters (for outer WHERE only) ---
        if (filters.country) {
            conditions.push(`oo.country_id = ?`);
            params.push(filters.country);
        }

        // Always exclude test names
        conditions.push(`
            (
                JSON_VALID(oo.customer_details) = 0
                OR LOWER(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name'))) NOT LIKE '%test%'
            )
        `);

        if (sender) {
            conditions.push(`oo.driver_id = ?`);
            params.push(sender);
        }
        if (paymode) {
            conditions.push(`oo.payment_method = ?`);
            params.push(paymode);
        }
        if (paymentGatewayType) {
            conditions.push(`oo.payment_methodid = ?`);
            params.push(paymentGatewayType);
        }
        if (orderFrom) {
            conditions.push(`oo.order_from_id = ?`);
            params.push(orderFrom);
        }
        if (staffName) {
            conditions.push(`oo.staff_id = ?`);
            params.push(staffName);
        }
        if (area) {
            conditions.push(`oo.area_id = ?`);
            params.push(area);
        }

        // Final WHERE for outer query
        const whereClause = conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';

        // -------------------------------
        // Data Query (uses ooh join + whereClause)
        // -------------------------------
        const dataConditions = [...conditions];
        const dataParams = [...params];

        const subConditions = [`ooh.order_statusid IN (${handleCancelStatus(cancel_status)})`, `ooh.order_status_type = 'order_status'`];
        const subParams = [];
        if (filters.fromDate && filters.toDate) {
            subConditions.push(`DATE(ooh.statusdate) BETWEEN DATE(?) AND DATE(?)`);
            subParams.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            subConditions.push(`DATE(ooh.statusdate) = DATE(?)`);
            subParams.push(filters.fromDate);
        }

        // this is for oms_order_history table
        let statusQuery = ''; 
        if (status) {
            statusQuery = `AND ooh.status = '${status}'`;
            subConditions.push(`ooh.status = ?`);
            subParams.push(status);
        }
        if (cancelReason) {
            subConditions.push(`ooh.staff_comments = ?`);
            subParams.push(cancelReason);
        }

        dataConditions.push(...subConditions);
        dataParams.push(...subParams);

        const dataWhere = dataConditions.length ? `WHERE ${dataConditions.join(' AND ')}` : '';

        let orderBy = 'oo.order_date DESC'; // default
        if (based_on && based_on  === 'status_date') {
            orderBy = 'ooh.statusdate DESC';
        }

        const dataQuery = `
            SELECT
                oo.order_ref_code as orderid,
                oo.country_id,
                ac.name AS country_name,
                oo.staff_id,
                oo.processing_fee,
                oo.order_status,
                oo.notes AS remarks,
                oo.gway_transaction_id AS fort_id,
                MAX(CASE WHEN ooh.order_statusid = 3 THEN ooh.statusdate END) AS despatch_date,
                oo.charge,
                MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN ooh.status END) AS cancel_status,
                MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN ooh.statusdate END) AS cancel_date,
                oo.delivery_date,
                oo.total_amount,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.mobile')) END AS contact,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')) END AS customer,
                CASE WHEN JSON_VALID(oo.customer_details) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.gender')) END AS gender,
                oo.order_date,
                al.list_lable AS orderfrom,
                oo.order_from_id,
                oo.payment_method,
                oo.payment_methodid,
                driver.firstname AS delivery_agent,
                driver.id AS delivery_agent_id,
                driver.id AS sender,
                CASE WHEN JSON_VALID(oo.shippingaddress) 
                    THEN JSON_UNQUOTE(JSON_EXTRACT(oo.shippingaddress, '$.emirate')) END AS emirate_id,
                oo.payment_methodid AS payment_type,
                DATEDIFF(
                    MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN ooh.statusdate END),
                    oo.order_date
                ) AS durdays,
                oo.total_amount AS display_amount,
                oo.payment_method AS payment_mode,
                MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN ooh.staff_comments END) AS cancel_reason,
                MAX(CASE WHEN oo.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN confirmed_user.firstname END) AS confirmed_by,
                MAX(CASE WHEN ooh.order_statusid IN (${handleCancelStatus(cancel_status)}) THEN cancel_user.firstname END) AS cancelled_by,
                ooh.status AS order_history_status,
                oo.area_id

            FROM oms_orders AS oo
            LEFT JOIN oms_order_history AS ooh ON oo.orderid = ooh.orderid
            LEFT JOIN aauth_users AS driver ON oo.driver_id = driver.id
            LEFT JOIN aauth_users AS confirmed_user ON oo.staff_id = confirmed_user.id
            LEFT JOIN aauth_users AS cancel_user ON ooh.staff_id = cancel_user.id
            LEFT JOIN admin_country AS ac ON oo.country_id = ac.country_id
            LEFT JOIN admin_lists AS al ON al.list_type = 'order_from' AND oo.order_from_id = al.list_value
            ${dataWhere}
            GROUP BY oo.orderid, ooh.order_statusid
            ORDER BY ${orderBy}
        `;

        const subWhere = `WHERE ${subConditions.join(' AND ')}`;
        const summaryQuery = `
            SELECT
                COUNT(*) AS totalOrders,
                SUM(oo.total_amount) AS rawTotalAmount,
                SUM(oo.tax_amount) AS vat,
                SUM(oo.shipping_charges) AS shippingCharge,
                SUM(oo.processing_fee) AS processingFees,
                SUM(oo.discount_amount) AS discountAmount
            FROM oms_orders oo
            JOIN (
                SELECT DISTINCT orderid, order_statusid
                FROM oms_order_history AS ooh
                ${subWhere}
            ) oh ON oo.orderid = oh.orderid
            ${whereClause}
        `;


        // console.log('Final dataQuery:', formatQuery(dataQuery, dataParams));
        // console.log('Final summaryQuery:', formatQuery(summaryQuery, [...subParams, ...params]));
        // console.log('params data:', dataParams);
        // console.log('params summary:', [...subParams, ...params]);

        // Run queries
        // console.log(mysql.format(dataQuery, dataParams));
        const [orders] = await db.query(dataQuery, dataParams);
        const [summaryRows] = await db.query(summaryQuery, [...subParams, ...params]);

        // Set emirates
        const emirateIds = Array.from(new Set(orders.map(order => order.emirate_id).filter(id => id).map(id => Number(id))));
        console.log(emirateIds)
        if(emirateIds.length) {
            const [emirateData] = await db.query(`SELECT emirateid, emirate FROM admin_country_emirates WHERE emirateid  IN (${emirateIds.join(',')})`);
            console.log(emirateData);
            orders.forEach(order => {
                const emirate = emirateData.find(e => e.emirateid === Number(order.emirate_id));
                order.emirate_name = emirate ? emirate.emirate : '';
            });
        }

        // Totals
        const totals = summaryRows[0] || {};
        if (totals.rawTotalAmount) {
            const rawTotal = parseFloat(totals.rawTotalAmount);
            const fees = parseFloat(totals.processingFees || 0);
            const totalVat = parseFloat(totals.vat || 0);
            const totalDiscount = parseFloat(totals.discountAmount || 0);
            const totalShipping = parseFloat(totals.shippingCharge || 0);

            totals.totalAmount = rawTotal - (fees + totalVat + totalDiscount);
            totals.grandTotal = rawTotal + totalShipping;
        }

        return { orders, totals };
    }
}

export default CancelOrder;
