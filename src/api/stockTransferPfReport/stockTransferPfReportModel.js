import db from '../../config/db.js';
import { getCategoryLineage } from '../../utils/commons.js';

class StockTransferPfReport {
    static async getReport(filters) {
        const { countryId, fromDate, toDate, store, status } = filters;
        if (!countryId) return [];

        // --- Build dynamic WHERE conditions ---
        let conditions = `
            prod.productid = hanp.productid
            AND hanp.handover_id = han.handover_id
            AND prod.categoryid IS NOT NULL
            AND prod.categoryid != 0
        `;
        const params = [];

        if (fromDate && toDate) {
            conditions += " AND date(han.bill_date) BETWEEN ? AND ?";
            params.push(fromDate, toDate);
        } else if (fromDate) {
            conditions += " AND date(han.bill_date) = ?";
            params.push(fromDate);
        }

        if (store) {
            conditions += " AND han.transfer_to = ?";
            params.push(store);
        }

        if (status) {
            conditions += " AND han.status = ?";
            params.push(status);
        }

        // --- Query to get grouped transfer data ---
        const sql = `
            SELECT 
            prod.categoryid AS category_id,
            hanp.productid AS product_id,
            prod.title AS product_name,
            hanp.cost,
            hanp.price,
            SUM(hanp.inventory) AS total_quantity
            FROM procurement_stockhandover han
            INNER JOIN procurement_stockhandover_products hanp ON han.handover_id = hanp.handover_id
            INNER JOIN catalog_product prod ON hanp.productid = prod.productid
            INNER JOIN catalog_product_inventory inv ON hanp.productid = inv.productid AND inv.country_id = ${countryId}
            WHERE ${conditions}
            GROUP BY prod.categoryid, hanp.productid, prod.title, hanp.cost, hanp.price
            ORDER BY prod.title
        `;

        const [results] = await db.query(sql, params);

        // --- Group data by category with computed totals ---
        const reportData = {};

        await Promise.all(results.map(async (row) => {
            const category = await getCategoryLineage(db, row.category_id);
            const { category_name, category_id } = category;

            if (!reportData[category_id]) {
                reportData[category_id] = {
                    categoryId: category_id,
                    categoryName: category_name,
                    products: [],
                    totals: { qty: 0, price: 0, cost: 0, profit: 0 }
                };
            }

            const totalQuantity = parseInt(row.total_quantity || 0, 10);
            const pricePerPc = parseFloat(row.price || 0);
            const costPerPc = parseFloat(row.cost || 0);

            const totalPrice = pricePerPc * totalQuantity;
            const totalCost = costPerPc * totalQuantity;
            const profit = totalPrice - totalCost;
            const gp = totalPrice > 0 ? (profit / totalPrice) * 100 : 0;

            reportData[category_id].products.push({
                productName: row.product_name,
                qty: totalQuantity,
                totalPrice: totalPrice,
                pricePerPc: pricePerPc,
                totalCost: totalCost,
                costPerPc: costPerPc,
                profit: profit,
                gp: gp.toFixed(2)
            });

            reportData[category_id].totals.qty += totalQuantity;
            reportData[category_id].totals.price += totalPrice;
            reportData[category_id].totals.cost += totalCost;
            reportData[category_id].totals.profit += profit;
        }));

        Object.values(reportData).forEach(cat => {
            cat.totals.gp = cat.totals.price > 0
                ? ((cat.totals.profit / cat.totals.price) * 100).toFixed(2)
                : '0.00';
        });

        return Object.values(reportData);

    }
}

export default StockTransferPfReport;
