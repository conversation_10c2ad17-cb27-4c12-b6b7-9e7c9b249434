import StockTransferPfReport from '../stockTransferPfReport/stockTransferPfReportModel.js';
import exceljs from 'exceljs';

// ===============================================
// STOCK TRANSFER PROFIT/LOSS REPORT CONTROLLER
// ===============================================

// Render the Transfer Profit/Loss Report EJS page
export const renderStockTransferPfPage = (req, res) => {
    res.render('reports/stock-transfer-pf-report', {
        user: req.user,
        pageTitle: "Transfer Profit/Loss Report"
    });
};

// API: Fetch Transfer Profit/Loss Report data
export const handleStockTransferPfApi = async (req, res) => {
    try {
        const reportData = await StockTransferPfReport.getReport(req.query);
        res.json({ success: true, data: reportData });
    } catch (error) {
        console.error("API Error fetching stock transfer P/F report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// API: Export Transfer Profit/Loss Report to Excel
export const handleStockTransferPfExcel = async (req, res) => {
    try {
        const reportData = await StockTransferPfReport.getReport(req.query);
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Transfer Profit Report');

        // Define Excel columns
        worksheet.columns = [
            { header: 'Product', key: 'product', width: 60 },
            { header: 'Qty', key: 'qty', width: 10, style: { alignment: { horizontal: 'center' } } },
            { header: 'Price', key: 'price', width: 20, style: { numFmt: '#,##0.00', alignment: { horizontal: 'right' } } },
            { header: 'Cost', key: 'cost', width: 20, style: { numFmt: '#,##0.00', alignment: { horizontal: 'right' } } },
            { header: 'Profit', key: 'profit', width: 20, style: { numFmt: '#,##0.00', alignment: { horizontal: 'right' } } },
            { header: 'GP%', key: 'gp', width: 15, style: { alignment: { horizontal: 'right' } } },
        ];
        worksheet.getRow(1).font = { bold: true };

        // Initialize grand totals
        let grandTotals = { qty: 0, price: 0, cost: 0, profit: 0 };

        // Add category-wise data rows
        reportData.forEach(category => {
            // Category Header
            const categoryRow = worksheet.addRow({ product: category.categoryName });
            categoryRow.font = { bold: true, size: 14 };
            categoryRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDDDDDD' } };
            worksheet.mergeCells(`A${categoryRow.number}:F${categoryRow.number}`);

            // Product rows under category
            category.products.forEach(product => {
                worksheet.addRow({
                    product: product.productName,
                    qty: product.qty,
                    price: product.totalPrice,
                    cost: product.totalCost,
                    profit: product.profit,
                    gp: `${product.gp}%`
                });
            });

            // Category Totals
            const categoryTotalRow = worksheet.addRow({
                product: 'Total',
                qty: category.totals.qty,
                price: category.totals.price,
                cost: category.totals.cost,
                profit: category.totals.profit,
                gp: `${category.totals.gp}%`
            });
            categoryTotalRow.font = { bold: true };
            categoryTotalRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFEEEEEE' } };

            // Update grand totals
            grandTotals.qty += category.totals.qty;
            grandTotals.price += category.totals.price;
            grandTotals.cost += category.totals.cost;
            grandTotals.profit += category.totals.profit;

            worksheet.addRow([]); // Spacer row
        });

        // Calculate Grand GP%
        const grandGP = grandTotals.price > 0 ? ((grandTotals.profit / grandTotals.price) * 100).toFixed(2) : '0.00';

        worksheet.addRow([]); // Spacer

        // Add Grand Total Summary section
        worksheet.addRow({ D: 'Total Quantity', E: ':', F: grandTotals.qty });
        worksheet.addRow({ D: 'Total Price', E: ':', F: grandTotals.price }).getCell('F').numFmt = '#,##0.00';
        worksheet.addRow({ D: 'Total Cost', E: ':', F: grandTotals.cost }).getCell('F').numFmt = '#,##0.00';

        const profitRow = worksheet.addRow({ D: 'Total Profit', E: ':', F: `${grandTotals.profit.toFixed(2)} AED` });
        profitRow.font = { bold: true };
        profitRow.getCell('F').numFmt = '"AED "#,##0.00';

        const gpRow = worksheet.addRow({ D: 'Total GP %', E: ':', F: `${grandGP}%` });
        gpRow.font = { bold: true };

        // Stream the Excel file
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="TransferProfitReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
