// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderStockTransferPfPage,
    handleStockTransferPfApi,
    handleStockTransferPfExcel
} from '../stockTransferPfReport/stockTransferPfReportController.js';

const router = express.Router();

// ===============================================
// PAGE RENDER ROUTE
// ===============================================
// EJS Render: Load the main Stock Transfer Profit/Loss Report page
router.get('/stock-transfer-pf-report', verifyToken, (req, res) => {
    res.render('stock-transfer-pf-report', {
        pageTitle: "STOCK TRANSFER P/F REPORT",
        user: req.user
    });
});

// ===============================================
// STOCK TRANSFER P/F REPORT ROUTES
// ===============================================

// Route: Render Stock Transfer PF report (server-side HTML render)
router.get('/reports/stock-transfer-pf', verifyToken, renderStockTransferPfPage);

// API Route: Fetch Stock Transfer PF report data (JSON response)
router.get('/api/reports/stock-transfer-pf', verifyToken, handleStockTransferPfApi);

// API Route: Export Stock Transfer PF report to Excel
router.get('/api/reports/stock-transfer-pf/export', verifyToken, handleStockTransferPfExcel);

export default router;
