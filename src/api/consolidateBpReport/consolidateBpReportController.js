import ConsolidateBpReport from '../consolidateBpReport/consolidateBpReportModel.js';
import exceljs from 'exceljs';

// Render the consolidate buyer performance report page
export const renderConsolidateBpPage = (req, res) => {
    res.render('consolidate-bp-report', {
        user: req.user,
        pageTitle: "Consolidate Buyer Performance"
    });
};

// Handle API call to fetch report data (JSON)
export const handleConsolidateBpApi = async (req, res) => {
    try {
        const filters = req.query;
        const data = await ConsolidateBpReport.getReport(filters);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching consolidate BP report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// Generate and download Excel version of the report
export const handleConsolidateBpExcel = async (req, res) => {
    try {
        const filters = req.query;
        const results = await ConsolidateBpReport.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Consolidate Buyer Report');

        // Define Excel columns
        worksheet.columns = [
            { header: 'BUYER', key: 'buyer', width: 20 },
            { header: 'METRIC', key: 'metric', width: 15 },
            { header: 'UAE', key: 'uae', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'OMAN', key: 'oman', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'QATAR', key: 'qatar', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'KUWAIT', key: 'kuwait', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'BAHRAIN', key: 'bahrain', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'TOTAL AED', key: 'total', width: 20, style: { numFmt: '#,##0.00' } },
        ];
        worksheet.getRow(1).font = { bold: true };

        // Add data rows for each buyer
        results.forEach(row => {
            const orderTotal = (row.UAE || 0) + (row.Oman || 0) + (row.Qatar || 0) + (row.Kuwait || 0) + (row.Bahrain || 0);
            const deliveryTotal = (row['1'] || 0) + (row['2'] || 0) + (row['3'] || 0) + (row['5'] || 0) + (row['6'] || 0);
            const profitTotal = (row.AED || 0) + (row.OMR || 0) + (row.QAR || 0) + (row.KWD || 0) + (row.BHD || 0);
            const gpTotal = deliveryTotal > 0 ? (profitTotal / deliveryTotal) * 100 : 0;

            worksheet.addRows([
                { buyer: row.buyer, metric: 'ORDER', uae: row.UAE, oman: row.Oman, qatar: row.Qatar, kuwait: row.Kuwait, bahrain: row.Bahrain, total: orderTotal },
                { metric: 'DELIVERY', uae: row['1'], oman: row['2'], qatar: row['3'], kuwait: row['5'], bahrain: row['6'], total: deliveryTotal },
                { metric: 'PROFIT', uae: row.AED, oman: row.OMR, qatar: row.QAR, kuwait: row.KWD, bahrain: row.BHD, total: profitTotal },
                {
                    metric: 'GP %',
                    uae: `${(row['UAE-gp'] || 0).toFixed(2)} %`,
                    oman: `${(row['Oman-gp'] || 0).toFixed(2)} %`,
                    qatar: `${(row['Qatar-gp'] || 0).toFixed(2)} %`,
                    kuwait: `${(row['Kuwait-gp'] || 0).toFixed(2)} %`,
                    bahrain: `${(row['Bahrain-gp'] || 0).toFixed(2)} %`,
                    total: `${gpTotal.toFixed(2)} %`
                },
                {} // Spacer row
            ]);
        });

        // Set response headers and send Excel file
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="ConsolidateBuyerReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
