import db from "../../config/db.js";

class ConsolidateBpReportold {
  static async getReport(filters) {
    const { fromDate, toDate } = filters;
    const aggregatedData = {};

    // --- Build date conditions ---
    let cond_order_date = "",
      cond_delivery_date = "";

    if (fromDate && !toDate) {
      cond_order_date = `AND DATE(oms_o.order_date) = '${fromDate}'`;
      cond_delivery_date = `AND DATE(oms_or_h.statusdate) = '${fromDate}'`;
    } else if (fromDate && toDate) {
      cond_order_date = `AND DATE(oms_o.order_date) BETWEEN '${fromDate}' AND '${toDate}'`;
      cond_delivery_date = `AND DATE(oms_or_h.statusdate) BETWEEN '${fromDate}' AND '${toDate}'`;
    } else {
      const today = new Date().toISOString().slice(0, 10);
      cond_order_date = `AND DATE(oms_o.order_date) = '${today}'`;
      cond_delivery_date = `AND DATE(oms_or_h.statusdate) = '${today}'`;
    }

    // --- Fetch country and buyer data ---
    const [countries] = await db.query(
      "SELECT * FROM admin_country WHERE status = 1"
    );
    const [buyers] = await db.query(`
            SELECT au.id, CONCAT(au.firstname, ' ', au.lastname) AS name 
            FROM aauth_user_to_group ag 
            LEFT JOIN aauth_users au ON au.id = ag.user_id 
            WHERE au.departmentid = 2 
              AND ag.group_id = 7 
              AND au.rstatus = 1 
            ORDER BY au.id ASC
        `);

    // Initialize structure
    buyers.forEach((buyer) => {
      aggregatedData[buyer.name] = { buyerId: buyer.id };
      countries.forEach((c) => {
        aggregatedData[buyer.name][c.name] = 0;
        aggregatedData[buyer.name][c.country_id] = 0;
        aggregatedData[buyer.name][c.currency] = 0;
        aggregatedData[buyer.name][`${c.name}-gp`] = 0;
      });
    });

    // --- Query all orders once ---
    const orderQuery = `
            SELECT COALESCE(CONCAT(au.firstname, ' ', au.lastname), 'unassigned') as buyer,
                   oms_o.country_id,
                   SUM(oms_od.mrp * oms_od.quantity) AS price
            FROM oms_orders oms_o
            INNER JOIN oms_order_detail oms_od ON oms_od.os_prod_order_id = oms_o.orderid
            INNER JOIN catalog_product sp ON sp.productid = oms_od.os_prod_order_id
            LEFT JOIN aauth_user_to_group ag ON ag.user_id = sp.buyer_id  
            LEFT JOIN aauth_users au ON au.id = ag.user_id    
            WHERE ag.group_id = 7
            ${cond_order_date}
            GROUP BY COALESCE(CONCAT(au.firstname, ' ', au.lastname), 'unassigned'), oms_o.country_id
        `;
    const [orderResults] = await db.query(orderQuery);

    // Fill order results
    orderResults.forEach((row) => {
      const buyerData = aggregatedData[row.buyer];
      if (buyerData) {
        const country = countries.find((c) => c.country_id === row.country_id);
        if (country) {
          buyerData[country.name] =
            parseFloat(row.price || 0) * country.currency_value;
        }
      }
    });

    // --- Query all deliveries once ---
    const deliveryQuery = `
            SELECT COALESCE(CONCAT(au.firstname, ' ', au.lastname), 'unassigned') as buyer, 
                   au.id as buyerId,
                   oms_o.country_id,
                   SUM(oms_od.mrp * oms_od.quantity) as price,
                   SUM(oms_od.cost * oms_od.quantity) as cost
            FROM oms_orders oms_o 
            LEFT JOIN oms_order_history oms_or_h ON oms_o.orderid = oms_or_h.orderid
            INNER JOIN oms_order_detail oms_od ON oms_od.os_prod_order_id = oms_o.orderid
            INNER JOIN catalog_product sp ON sp.productid = oms_od.os_prod_order_id
            LEFT JOIN aauth_user_to_group ag ON ag.user_id = sp.buyer_id  
            LEFT JOIN aauth_users au ON au.id = ag.user_id    
            WHERE ag.group_id = 7
              AND oms_o.order_statusid = 6
            ${cond_delivery_date}
            GROUP BY COALESCE(CONCAT(au.firstname, ' ', au.lastname), 'unassigned'), au.id, oms_o.country_id
        `;
    const [deliveryResults] = await db.query(deliveryQuery);

    // Fill delivery results
    deliveryResults.forEach((row) => {
      const buyerData = aggregatedData[row.buyer];
      if (buyerData) {
        const country = countries.find((c) => c.country_id === row.country_id);
        if (country) {
          const totalPrice = parseFloat(row.price || 0);
          const totalCost = parseFloat(row.cost || 0);

          let inclusiveTax = 0;
          if (country.country_id == 3 || country.country_id == 5)
            inclusiveTax = 0;
          else if (country.country_id == 6)
            inclusiveTax = (totalPrice * 10) / 110;
          else inclusiveTax = (totalPrice * 5) / 105;

          const netExcluded = totalPrice - inclusiveTax;
          const profit = netExcluded - totalCost;

          buyerData[country.country_id] = totalPrice * country.currency_value;
          buyerData[country.currency] = profit * country.currency_value;
          buyerData[`${country.name}-gp`] =
            netExcluded > 0 ? (profit / netExcluded) * 100 : 0;
          buyerData["buyerId"] = row.buyerId;
        }
      }
    });

    // --- Format output ---
    const finalData = Object.entries(aggregatedData).map(([buyer, data]) => {
      const completeData = { buyer, buyerId: data.buyerId };
      countries.forEach((c) => {
        completeData[c.name] = data[c.name] || 0;
        completeData[c.country_id] = data[c.country_id] || 0;
        completeData[c.currency] = data[c.currency] || 0;
        completeData[`${c.name}-gp`] = data[`${c.name}-gp`] || 0;
      });
      return completeData;
    });

    finalData.sort((a, b) => (a.buyer || "").localeCompare(b.buyer || ""));
    return finalData;
  }
}
class ConsolidateBpReport {
  static async getReport(filters) {
    const { fromDate, toDate } = filters;
    const aggregatedData = {};
    const orderDateCond = fromDate
      ? toDate
        ? `AND DATE(oo.order_date) BETWEEN ? AND ?`
        : `AND DATE(oo.order_date) = ?`
      : "";
    const deliveryDateCond = fromDate
      ? toDate
        ? `AND DATE(ooh.statusdate) BETWEEN ? AND ?`
        : `AND DATE(ooh.statusdate) = ?`
      : "";
    const orderParams = [];
    const deliveryParams = [];
    if (fromDate) {
      if (toDate) {
        orderParams.push(fromDate, toDate);
        deliveryParams.push(fromDate, toDate);
      } else {
        orderParams.push(fromDate);
        deliveryParams.push(fromDate);
      }
    }
    const orderQuery = `
            SELECT DISTINCT
                COALESCE(CONCAT(au.firstname,' ', au.lastname), 'unassigned') AS buyer,
                ac.name AS country_name,
                oo.country_id,
                oo.orderid,
                oo.sub_total as sub_total,
                oo.tax_amount,
                ood.orderdetail_id,
                ood.quantity,
                cp.buyer_id
            FROM oms_orders oo
            JOIN oms_order_detail ood ON oo.orderid = ood.orderid
            JOIN catalog_product cp ON ood.productid = cp.productid
            LEFT JOIN aauth_users au ON cp.buyer_id = au.id
            JOIN admin_country ac ON oo.country_id = ac.country_id
            WHERE oo.order_statusid > 0 and oo.type = "order"
              AND (JSON_VALID(oo.customer_details) = 0 
                   OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%')
              
              ${orderDateCond}
              group by oo.orderid ,COALESCE(CONCAT(au.firstname,' ', au.lastname), 'unassigned'), ac.name

        `;
    const [orderResults] = await db.query(orderQuery, orderParams);
     const deliveryQuery = `
         SELECT DISTINCT
        COALESCE(CONCAT(au.firstname, ' ', au.lastname), 'unassigned') AS buyer,
        ac.name as country_name,
        oo.country_id,
        ood.selling_price,
        oo.sub_total,
        ooh.orderid,
        ooh.statusdate,
        oo.tax_amount,
        ood.cost,
        ood.orderdetail_id,
        cp.buyer_id,
        ood.quantity
      FROM oms_order_history ooh
      JOIN oms_orders oo ON ooh.orderid = oo.orderid
      JOIN oms_order_detail ood ON oo.orderid = ood.orderid
      JOIN catalog_product cp ON ood.productid = cp.productid
      LEFT JOIN aauth_users au ON cp.buyer_id = au.id
      JOIN admin_country ac ON oo.country_id = ac.country_id
      JOIN (
        SELECT orderid, MIN(statusdate) AS min_delivery
        FROM oms_order_history
        WHERE order_statusid = 6 and order_status_type = 'order_status'
        GROUP BY orderid
      ) first_delivery ON ooh.orderid = first_delivery.orderid AND ooh.statusdate = first_delivery.min_delivery
      WHERE ooh.order_statusid = 6 and oo.type = "order" 
        AND ooh.order_status_type = 'order_status'
        AND ac.status = 1
         AND (JSON_VALID(oo.customer_details) = 0 
                   OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%')
           
      ${deliveryDateCond}
    group by oo.orderid
    `;
    const [deliveryResults] = await db.query(deliveryQuery, deliveryParams);
    const allBuyers = [
      ...new Set([
        ...orderResults.map((r) => r.buyer),
        ...deliveryResults.map((r) => r.buyer),
      ]),
    ];
    const allCountries = [
      ...new Set([
        ...orderResults.map((r) => r.country_name),
        ...deliveryResults.map((r) => r.country_name),
      ]),
    ];
 
    for (const buyer of allBuyers) {
      aggregatedData[buyer] = {};
      for (const country of allCountries) {
        aggregatedData[buyer][country] = 0; // ORDER
        aggregatedData[buyer][`${country}-delivery`] = 0; // DELIVERY
        aggregatedData[buyer][`${country}-profit`] = 0; // PROFIT
        aggregatedData[buyer][`${country}-gp`] = 0; // GP %
      }
    }

    for (const row of orderResults) {
      aggregatedData[row.buyer][row?.country_name] += parseFloat(row.sub_total);
      // aggregatedData[row.buyer][row?.country_name] +=
      //   parseFloat(row.totalAmount);
    }
    for (const row of deliveryResults) {
      const totalPrice =
        parseFloat(row.sub_total);
      
      const totalCost = parseFloat(row.cost) * parseFloat(row.quantity);

      let inclusiveTax = row.tax_amount;
      const netExcluded = totalPrice - inclusiveTax;
      const profit = netExcluded - totalCost;

      aggregatedData[row.buyer][`${row.country_name}-delivery`] += totalPrice;
      aggregatedData[row.buyer][`${row.country_name}-profit`] += profit;
      aggregatedData[row.buyer][`${row.country_name}-gp`] =
        netExcluded > 0
          ? (aggregatedData[row.buyer][`${row.country_name}-profit`] /
              totalCost) *
            100
          : 0;
    }

    // --- Return formatted report ---
    return Object.entries(aggregatedData).map(([buyer, data]) => ({
      buyer,
      ...data,
    }));
  }
}
export default ConsolidateBpReport;
