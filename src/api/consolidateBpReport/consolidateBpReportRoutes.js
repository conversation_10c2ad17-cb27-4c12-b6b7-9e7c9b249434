import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderConsolidateBpPage,
    handleConsolidateBpApi,
    handleConsolidateBpExcel
} from '../consolidateBpReport/consolidateBpReportController.js';

const router = express.Router(); // Create router instance

// Render static consolidate BP report page (direct path)
router.get('/consolidate-bp-report', verifyToken, (req, res) => {
    res.render('consolidate-bp-report', {
        pageTitle: "CONSOLIDATE BUYER PERFORMANCE REPORT",
        user: req.user
    });
});

// ============================
// CONSOLIDATE BP REPORT ROUTES
// ============================

// Render report page
router.get('/reports/consolidate-bp', verifyToken, renderConsolidateBpPage);

// Fetch report data (API)
router.get('/api/reports/consolidate-bp', verifyToken, handleConsolidateBpApi);

// Export report to Excel
router.get('/api/reports/consolidate-bp/export', verifyToken, handleConsolidateBpExcel);

export default router;
