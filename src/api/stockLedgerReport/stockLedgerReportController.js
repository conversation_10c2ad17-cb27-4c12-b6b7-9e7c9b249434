// src/controllers/stockLedgerReportController.js

import StockLedgerReport from '../stockLedgerReport/stockLedgerReportModel.js';

// Render the Stock Ledger report page
export const renderStockLedgerPage = (req, res) => {
    res.render('reports/stock-ledger-report', {
        user: req.user,
        pageTitle: "Stock Ledger"
    });
};

// API: Fetch stock ledger report data
export const handleStockLedgerApi = async (req, res) => {
    try {
        const data = await StockLedgerReport.getReport(req.query);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching stock ledger report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};
