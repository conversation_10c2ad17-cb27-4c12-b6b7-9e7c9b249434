import db from '../../config/db.js';

// Helper function (no changes)
// const getTableName = (countryId, tableType) => {
//     const code = String(countryId);
//     const sharedTables = ['admins', 'supplier', 'complaints', 'replacement', 'purchase_return_parent', 'stockhandover', 'stockhandoverproducts', 'store'];
//     if (sharedTables.includes(tableType)) return `shopee_${tableType}`;
//     const sharedOtherTables = ['ourshopee_products'];
//     if (sharedOtherTables.includes(tableType)) return tableType;
//     const prefixes = { '1': 'shopee', '2': 'oman_sm', '3': 'qatar_sm', '5': 'kuwait_sm', '6': 'bahrain_sm', '7': 'saudi_sm' };
//     const prefix = prefixes[code] || 'shopee';
//     const tableMap = {
//         products: 'products', purchase_products: 'purchaseproducts', product_orders: 'productorders',
//         return_products: 'returnproducts', replace_products: 'replaceproducts',
//         purchase_return: 'purchasereturn', purchase: 'purchase', orders: 'orders'
//     };
//     if (code === '1') return `shopee_${tableMap[tableType]}`;
//     return `${prefix}${tableMap[tableType]}`;
// };

class StockLedgerReport {
    static async getReport(filters) {
        const { countryId, productId, fromDate, toDate } = filters;
        if (!countryId || !productId) return { transactions: [], openingBalance: 0, productName: '' };
        
        // const productsTbl = getTableName(countryId, 'products');
        // const purchaseTbl = getTableName(countryId, 'purchase');
        // const purchaseProductsTbl = getTableName(countryId, 'purchase_products');
        // const returnProductsTbl = getTableName(countryId, 'return_products');
        // const replaceProductsTbl = getTableName(countryId, 'replace_products');
        // const productOrdersTbl = getTableName(countryId, 'product_orders');
        // const handoverProductsTbl = getTableName('1', 'stockhandoverproducts');
        // const handoverTbl = getTableName('1', 'stockhandover');
        // const purchaseReturnTbl = getTableName(countryId, 'purchase_return');
        // const ordersTbl = getTableName(countryId, 'orders');
        // const complaintsTbl = getTableName(countryId, 'complaints');
        // const replacementTbl = getTableName(countryId, 'replacement');
        // const supplierTbl = getTableName('1', 'supplier');
        // const storeTbl = getTableName('1', 'store');
        // const purchaseReturnParentTbl = getTableName('1', 'purchase_return_parent');

        const [[productDetails]] = await db.query(`SELECT title as name, sku as product_code FROM catalog_product WHERE productid = ?`, [productId]);
        const productName = productDetails?.name || 'Unknown Product';
        const smProductId = productDetails?.product_code;

        let openingBalance = 0;
        let openingIn = 0;
        let openingOut = 0;

        if (fromDate) {
            if (String(countryId) === '1') {
                const physicalStockSql = `SELECT bill_date FROM procurement_purchase WHERE supplierid = '8' AND date(bill_date) < ? ORDER BY bill_date DESC LIMIT 1`;
                const [[physicalStockEntry]] = await db.query(physicalStockSql, [fromDate]);
                const obStartDate = physicalStockEntry ? new Date(physicalStockEntry.bill_date).toISOString().slice(0, 10) : '1970-01-01';

                const inQuery = `SELECT (SELECT COALESCE(SUM(b.qty), 0) FROM procurement_purchase a JOIN $procurement_purchase_products b ON a.id = b.purchase_id WHERE a.supplierid != 8 AND b.productid = ? AND date(b.order_date) >= ? AND date(b.order_date) <= ?) + (SELECT COALESCE(SUM(quantity), 0) FROM ${returnProductsTbl} WHERE product_id = ? AND date(order_date) >= ? AND date(order_date) <= ?) + (SELECT COALESCE(SUM(quantity), 0) FROM ${replaceProductsTbl} WHERE product_id = ? AND date(order_date) >= ? AND date(order_date) <= ?) as total`;
                const [[inResult]] = await db.query(inQuery, [productId, obStartDate, fromDate, productId, obStartDate, fromDate, productId, obStartDate, fromDate]);
                openingIn = parseInt(inResult.total, 10);

                const outQuery = `SELECT (SELECT COALESCE(SUM(quantity), 0) FROM catalog_product WHERE product_id = ? AND country_id = 1 AND date(order_date) >= ? AND date(order_date) <= ?) + (SELECT COALESCE(SUM(qty), 0) FROM procurement_stockhandover_products WHERE productid = ? AND date(order_date) >= ? AND date(order_date) <= ?) as total`;
                const [[outResult]] = await db.query(outQuery, [productId, obStartDate, fromDate, productId, obStartDate, fromDate]);
                openingOut = parseInt(outResult.total, 10);
            } else {
                const [[inResult]] = await db.query(`SELECT COALESCE(SUM(qty), 0) as total FROM procurement_purchase_products WHERE productid = ? AND date(order_date) < ?`, [productId, fromDate]);
                openingIn = parseInt(inResult.total, 10);
                const [[outResult]] = await db.query(`SELECT COALESCE(SUM(quantity), 0) as total FROM catalog_product WHERE product_id = ? AND date(order_date) < ?`, [productId, fromDate]);
                openingOut = parseInt(outResult.total, 10);
            }
            openingBalance = openingIn - openingOut;
        }

        const dateCondition = (fromDate && toDate) ? `AND date(a.order_date) BETWEEN ? AND ?` : '';
        const dateParams = (fromDate && toDate) ? [fromDate.split(' ')[0], toDate.split(' ')[0]] : [];
        const handoverItemIdentifier = String(countryId) === '1' ? productId : smProductId;

        const commonSelects = [
            `(SELECT 'IN_Purchase' as type, productid as product_id, qty, order_date, purchase_id, NULL as order_id, NULL as ref_id, NULL as handover_id, NULL as purchasereturn_id, 'Purchase' as status, NULL as quantity FROM procurement_purchase_products a WHERE a.productid = ? ${dateCondition})`,
            `(SELECT 'OUT_Order' as type, product_id, NULL as qty, order_date, NULL, order_id, NULL, NULL, NULL, 'Sale' as status, quantity FROM catalog_product a WHERE a.product_id = ? AND country_id = ? ${dateCondition})`,
            `(SELECT 'IN_Return' as type, product_id, NULL as qty, order_date, NULL, order_id, NULL, NULL, NULL, status, quantity FROM ${returnProductsTbl} a WHERE a.product_id = ? ${dateCondition})`,
            `(SELECT 'IN_Replace' as type, product_id, NULL as qty, order_date, NULL, NULL, ref_id, NULL, NULL, status, quantity FROM ${replaceProductsTbl} a WHERE a.product_id = ? ${dateCondition})`,
            `(SELECT 'OUT_PurchaseReturn' as type, product_id, NULL as qty, order_date, NULL, NULL, NULL, NULL, purchasereturn_id, 'PurchaseReturn' as status, quantity FROM ${purchaseReturnTbl} a WHERE a.product_id = ? ${dateCondition})`
        ];

        let unionParams = [
            productId, ...dateParams,
            productId, countryId, ...dateParams,
            productId, ...dateParams,
            productId, ...dateParams,
            productId, ...dateParams,
        ];
        
        if (String(countryId) === '1') {
            commonSelects.push(`(SELECT 'OUT_Handover' as type, a.productid as product_id, a.qty, a.order_date, NULL as purchase_id, NULL as order_id, NULL as ref_id, a.handover_id, NULL as purchasereturn_id, 'Handover' as status, NULL as quantity FROM procurement_stockhandover_products a WHERE a.productid = ? ${dateCondition})`);
            unionParams.push(handoverItemIdentifier, ...dateParams);
        } else {
            commonSelects.push(`(SELECT 'OUT_Handover' as type, a.productid as product_id, a.qty, a.order_date, NULL as purchase_id, NULL as order_id, NULL as ref_id, a.handover_id, NULL as purchasereturn_id, 'Handover' as status, NULL as quantity FROM procurement_stockhandover_products a JOIN ${handoverTbl} b ON a.handover_id = b.id WHERE a.productid = ? AND b.transfer_from = ? ${dateCondition})`);
            unionParams.push(handoverItemIdentifier, countryId, ...dateParams);
        }
        
        const unionSql = commonSelects.join(' UNION ALL ') + ' ORDER BY order_date ASC, type DESC';
        
        const [transactions] = await db.query(unionSql, unionParams);


        const purchaseIds = transactions.map(t => t.purchase_id).filter(id => id);
        const orderIds = transactions.map(t => t.order_id).filter(id => id);
        const refIds = transactions.map(t => t.ref_id).filter(id => id);
        const handoverIds = transactions.map(t => t.handover_id).filter(id => id);
        const purchaseReturnIds = transactions.map(t => t.purchasereturn_id).filter(id => id);

        const refDetails = {};
        if (purchaseIds.length > 0) {
            const [rows] = await db.query(`SELECT p.id, p.bill_no, p.supplierid, s.company_name FROM ${purchaseTbl} p LEFT JOIN admin_supplier s ON p.supplierid = s.id WHERE p.id IN (?)`, [purchaseIds]);
            rows.forEach(r => refDetails[`purchase_${r.id}`] = r);
        }
        if (orderIds.length > 0) {
            const [rows] = await db.query(`SELECT id, orderid, customer, order_date FROM oms_orders WHERE id IN (?)`, [orderIds]);
            rows.forEach(r => refDetails[`order_${r.id}`] = r);
        }
        if (refIds.length > 0) {
            const [rows] = await db.query(`SELECT r.id, c.invoiceno, c.complaint_id, c.name FROM ${replacementTbl} r JOIN ${complaintsTbl} c ON r.complaint_id = c.id WHERE r.id IN (?)`, [refIds]);
            rows.forEach(r => refDetails[`ref_${r.id}`] = r);
        }
        if (handoverIds.length > 0) {
            const [rows] = await db.query(`SELECT h.id, h.bill_no, s.name as store_name FROM ${handoverTbl} h LEFT JOIN admin_country s ON h.store_name = s.id WHERE h.id IN (?)`, [handoverIds]);
            rows.forEach(r => refDetails[`handover_${r.id}`] = r);
        }
        if (purchaseReturnIds.length > 0) {
            const [rows] = await db.query(`SELECT pr.id, pr.bill_no, s.company_name FROM ${purchaseReturnParentTbl} pr LEFT JOIN admin_supplier s ON pr.supplier_id = s.id WHERE pr.id IN (?)`, [purchaseReturnIds]);
            rows.forEach(r => refDetails[`purchasereturn_${r.id}`] = r);
        }

        transactions.forEach(t => {
            t.inQty = 0;
            t.outQty = 0;
            const orderDetail = refDetails[`order_${t.order_id}`];
            t.transaction_date = (orderDetail && t.status !== 'return') ? orderDetail.order_date : t.order_date;

            switch (t.type) {
                case 'IN_Purchase': {
                    const detail = refDetails[`purchase_${t.purchase_id}`];
                    t.refNo = detail?.bill_no;
                    t.businessExecutive = `${detail?.company_name || ''} [stock-in]`;
                    if (String(detail?.supplierid) === '8') {
                        t.isPhysicalStock = true;
                    }
                    t.inQty = t.qty;
                    break;
                }
                case 'OUT_PurchaseReturn': {
                    const detail = refDetails[`purchasereturn_${t.purchasereturn_id}`];
                    t.outQty = t.quantity;
                    t.refNo = detail?.bill_no;
                    t.businessExecutive = `${detail?.company_name} [Purchase_return - stock-out]`;
                    break;
                }
                case 'OUT_Handover': {
                    const detail = refDetails[`handover_${t.handover_id}`];
                    t.outQty = t.qty;
                    t.refNo = detail?.bill_no;
                    t.businessExecutive = `${detail?.store_name} [stock-out]`;
                    break;
                }
                case 'OUT_Order': {
                    t.outQty = t.quantity;
                    t.refNo = orderDetail?.orderid;
                    t.businessExecutive = `${orderDetail?.customer} [Stock_out]`;
                    break;
                }
                case 'IN_Return': {
                    t.inQty = t.quantity;
                    t.refNo = orderDetail?.orderid;
                    t.businessExecutive = `${orderDetail?.customer} [${t.status}]`;
                    break;
                }
                case 'IN_Replace': {
                    const detail = refDetails[`ref_${t.ref_id}`];
                    t.inQty = t.quantity;
                    t.refNo = `${detail?.invoiceno}-${detail?.complaint_id}`;
                    t.businessExecutive = `${detail?.name} [Return-Replace]`;
                    break;
                }
            }
        });

        return { 
            transactions, 
            openingBalance, 
            openingIn, 
            openingOut, 
            productName 
        };
    }
}

export default StockLedgerReport;