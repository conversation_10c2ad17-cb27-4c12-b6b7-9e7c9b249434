// src/routes/reportRoutes.js

import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderStockLedgerPage,
    handleStockLedgerApi
} from '../stockLedgerReport/stockLedgerReportController.js';

const router = express.Router();

// ===============================================
// RENDER STOCK LEDGER REPORT PAGE
// ===============================================
router.get('/stock-ledger-report', verifyToken, (req, res) => {
    res.render('stock-ledger-report', {
        pageTitle: "STOCK LEDGER REPORT",
        user: req.user
    });
});

// ===============================================
// STOCK LEDGER REPORT ROUTES
// ===============================================
router.get('/reports/stock-ledger', verifyToken, renderStockLedgerPage);
router.get('/api/reports/stock-ledger', verifyToken, handleStockLedgerApi);

export default router;
