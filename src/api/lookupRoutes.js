import express from 'express'; // Import express framework
import {
    getSenders,            // Controller: fetch delivery team
    getStaff,              // Controller: fetch active staff/admins
    getNationalities,      // Controller: fetch nationality list
    getPaymentGateways,    // Controller: fetch payment gateway list
    getCountries,          // Controller: fetch country list
    getBuyers,             // Controller: fetch buyers list
    getCategories,         // Controller: fetch category list
    searchProducts,        // Controller: search products by name/category
    getStores,             // Controller: fetch stores list
    searchSkus,             // Controller: search SKUs (product codes)
    getOrderFrom,
    getCancelReason,
    getEmirate,
    getArea,
    getSuppliers,
    getSubCategories,
    getSubSubCategories,
    getBrand,
    getCatelogueAgent

} from '../api/lookupController.js';

import { verifyToken } from '../middleware/authMiddleware.js'; // Auth middleware

const router = express.Router(); // Create router instance

// Middleware to protect all routes (disabled currently)
// router.use(verifyToken);

// ============================
// Lookup API Routes (GET)
// ============================

// Lookup route to get all senders
router.get('/api/lookups/senders', getSenders);

// Lookup route to get all staff/admin users
router.get('/api/lookups/staff', getStaff);

// Lookup route to get nationalities
router.get('/api/lookups/nationalities', getNationalities);

// Lookup route to get payment gateways
router.get('/api/lookups/payment-gateways', getPaymentGateways);

// Lookup route to get countries
router.get('/api/lookups/countries', getCountries);

// Lookup route to get buyers
router.get('/api/lookups/buyers', getBuyers);

// Lookup route to get categories
router.get('/api/lookups/categories', getCategories);

// Lookup route to get subcategories
router.get('/api/lookups/subcategories',getSubCategories);

// Lookup route to get categories
router.get('/api/lookups/subsubcategories', getSubSubCategories);

// Lookup route to get categories
router.get('/api/lookups/brand', getBrand);

// Lookup route to get categories
router.get('/api/lookups/catelogueagent', getCatelogueAgent);

// Product search route (by name/category)
router.get('/api/lookups/products', searchProducts);

router.get('/api/lookups/order-from', getOrderFrom);
router.get('/api/lookups/cancel-reason',getCancelReason);
router.get('/api/lookups/emirates',getEmirate);
router.get('/api/lookups/areas',getArea);

// Lookup route to get stores
router.get('/api/lookups/stores', getStores);

// SKU search route (requires authentication)
router.get('/api/lookups/skus', verifyToken, searchSkus);

router.get('/api/lookups/suppliers', verifyToken, getSuppliers);

export default router; // Export router
