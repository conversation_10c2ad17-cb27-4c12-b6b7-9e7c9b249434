// src/models/helpers/getTableName.js
export const getTableName = (countryCode, type = 'orders') => {
  const code = String(countryCode);

  const tables = {
    orders: {
      '1': 'shopee_orders',
      '2': 'oman_smorders',
      '3': 'qatar_smorders',
      '5': 'kuwait_smorders',
      '6': 'bahrain_smorders',
      '7': 'saudi_smorders'
    },
    sender: {
      '1': 'shopee_senderdetails',
      '2': 'oman_smsenderdetails',
      '3': 'qatar_smsenderdetails',
      '5': 'kuwait_smsenderdetails',
      '6': 'bahrain_smsenderdetails',
      '7': 'saudi_smsenderdetails'
    },
    productOrders: {
      '1': 'shopee_productorders',
      '2': 'oman_smproductorders',
      '3': 'qatar_smproductorders',
      '5': 'kuwait_smproductorders',
      '6': 'bahrain_smproductorders',
      '7': 'saudi_smproductorders'
    },
    purchase: {
      '1': 'shopee_purchase',
      '2': 'oman_smpurchase',
      '3': 'qatar_smpurchase',
      '5': 'kuwait_smpurchase',
      '6': 'bahrain_smpurchase',
      '7': 'saudi_smpurchase'
    },
    products: {
      '1': 'shopee_products',
      '2': 'oman_smproducts',
      '3': 'qatar_smproducts',
      '5': 'kuwait_smproducts',
      '6': 'bahrain_smproducts',
      '7': 'saudi_smproducts'
    },
    purchaseproducts: {
      '1': 'shopee_purchaseproducts',
      '2': 'oman_smpurchaseproducts',
      '3': 'qatar_smpurchaseproducts',
      '5': 'kuwait_smpurchaseproducts',
      '6': 'bahrain_smpurchaseproducts',
      '7': 'saudi_smpurchaseproducts'
    },
    ourshopee_products: {
      '1': 'ourshopee_products',
      '2': 'oman_cms_products',
      '3': 'qatar_cms_products',
      '5': 'kuwait_cms_products',
      '6': 'bahrain_cms_products',
      '7': 'saudi_cms_products'
    },
    airway: {
      '1': 'shopee_apirecords'
      // add more if other countries store airway records separately
    },
    comments: {
      '1': 'shopee_comments',
      '2': 'oman_smcomments',
      '3': 'qatar_smcomments',
      '5': 'kuwait_smcomments',
      '6': 'bahrain_smcomments',
      '7': 'saudi_smcomments'
    }
  };

  return tables[type]?.[code] || tables[type]['1'];
};
