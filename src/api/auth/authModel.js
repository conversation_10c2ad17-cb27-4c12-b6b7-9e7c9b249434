import db from '../../config/db.js'; // Import DB connection

class User {
    // Find user by username
    static async findByUsername(username) {
        const [rows] = await db.query(
            'SELECT * FROM aauth_users WHERE username = ?', 
            [username]
        );
        return rows[0]; // Return the first result
    }

    // Additional methods can be added here
}

export default User; // Export User class
