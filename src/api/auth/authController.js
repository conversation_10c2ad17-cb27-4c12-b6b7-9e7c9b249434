import bcrypt from 'bcrypt'; // Password hashing
import jwt from 'jsonwebtoken'; // JWT token generation
import User from './authModel.js'; // Import User model
import { hashPassword } from '../..//utils/passwordUtils.js';

// Render the login page
export const showLoginPage = (req, res) => {
    res.render('login');
};

// Handle login request
export const handleLogin = async (req, res) => {
    try {
        const { username, password } = req.body;

        // Validate input
        if (!username || !password) {
            return res.status(400).json({ success: false, error: 'Username and password are required.' });
        }
        
        // Fetch user by username
        const user = await User.findByUsername(username);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Invalid Username or Password!' });
        }

        // Check password
        const hashedPassword = await hashPassword(password, user.id.toString());
        //const isPasswordCorrect = await bcrypt.compare(password, user.password);
        if (!hashedPassword) {
            return res.status(401).json({ success: false, error: 'Invalid Username or Password!' });
        }

        const profilePicUrl = Array.isArray(user.profile_pic) && user.profile_pic[0]?.file_url
        ? `${process.env.CDN_IMAGE_BASE_URL}${user.profile_pic[0].file_url}`
        : '';
        // Generate JWT token
        const payload = { id: user.id, username: user.username, firstname: user.firstname, lastname: user.lastname, profile_pic: profilePicUrl };
        const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '15h' });
        
        // Set token in cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 54000000 
        });

        res.status(200).json({ success: true });

    } catch (err) {
        console.error("Error during login:", err);
        res.status(500).json({ success: false, error: 'A server error occurred.' });
    }
};

// Handle logout and clear session
export const handleLogout = (req, res) => {
    res.clearCookie('token');
    res.redirect('/login');
};
