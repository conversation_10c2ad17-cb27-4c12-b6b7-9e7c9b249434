import express from 'express';
import {
    showLoginPage,
    handleLogin,
    handleLogout
} from '../auth/authController.js';
import { redirectIfLoggedIn } from '../../../src/middleware/authMiddleware.js';

const router = express.Router();

// Show login page (redirect if already logged in)
router.get('/login', redirectIfLoggedIn, showLoginPage);

// Process login request
router.post('/api/login', handleLogin);

// Logout user and clear session
router.get('/logout', handleLogout);

export default router;
