import db from '../../config/db.js';

class ConsolidateOsReport {
    static async getReport(filters) {
        let { fromDate, toDate } = filters;
        if (!fromDate) {
            fromDate = new Date().toISOString().slice(0, 10);
        }

        const [countries] = await db.query("SELECT * FROM admin_country WHERE status = 1");
        const reportData = {};

        for (const country of countries) {
            const crValue = parseFloat(country.currency_value) || 1;
            const countryId = country.country_id;

            const orderDateParams = [countryId];
            let orderDateWhere = `oo.country_id = ?`;

            if (toDate) {
                orderDateWhere += ` AND DATE(oo.order_date) BETWEEN ? AND ?`;
                orderDateParams.push(fromDate, toDate);
            } else {
                orderDateWhere += ` AND DATE(oo.order_date) = ?`;
                orderDateParams.push(fromDate);
            }

            const orderBasedSql = `
                SELECT
                    ac.name,
                    COALESCE(COUNT(oo.orderid), 0) AS total_orders,
                    COALESCE(SUM(oo.total_amount), 0) AS total_value,

                    -- Delivered
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 6 THEN 1 ELSE 0 END), 0) AS delivered_count,
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 6 THEN oo.total_amount ELSE 0 END), 0) AS delivered_value_for_total,

                    -- Cancelled - Before Dispatch
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 7 THEN 1 ELSE 0 END), 0) AS cancelled_before_dispatch_count,
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 7 THEN oo.total_amount ELSE 0 END), 0) AS cancelled_before_dispatch_value,

                    -- Cancelled - After Dispatch
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 11 THEN 1 ELSE 0 END), 0) AS cancelled_after_dispatch_count,
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 11 THEN oo.total_amount ELSE 0 END), 0) AS cancelled_after_dispatch_value,

                    -- Cancelled - Total
                    COALESCE(
                        SUM(CASE WHEN oo.order_statusid = 7 THEN 1 ELSE 0 END) +
                        SUM(CASE WHEN oo.order_statusid = 11 THEN 1 ELSE 0 END), 0
                    ) AS cancelled_count,
                    COALESCE(
                        SUM(CASE WHEN oo.order_statusid = 7 THEN oo.total_amount ELSE 0 END) +
                        SUM(CASE WHEN oo.order_statusid = 11 THEN oo.total_amount ELSE 0 END), 0
                    ) AS total_cancelled_value,

                    -- Dispatched
                    COALESCE(SUM(CASE WHEN oo.order_statusid IN (3,5) THEN 1 ELSE 0 END), 0) AS dispatched_count,
                    COALESCE(SUM(CASE WHEN oo.order_statusid IN (3,5) THEN oo.total_amount ELSE 0 END), 0) AS dispatched_value,

                    -- Pending
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 1 THEN 1 ELSE 0 END), 0) AS to_be_dispatched_count,
                    COALESCE(SUM(CASE WHEN oo.order_statusid = 1 THEN oo.total_amount ELSE 0 END), 0) AS to_be_dispatched_value

                FROM oms_orders oo
                INNER JOIN admin_country ac ON oo.country_id = ac.country_id
                WHERE ${orderDateWhere}
                AND oo.order_statusid > 0 AND oo.type = "order"
                AND (
                    JSON_VALID(oo.customer_details) = 0
                    OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
                )
                GROUP BY ac.name;
            `;


            // Run the query safely
            const [orderRows] = await db.query(orderBasedSql, orderDateParams);

            // ✅ Fallback if no data is returned
            const orderData = orderRows && orderRows[0] ? orderRows[0] : {
                total_orders: 0,
                total_value: 0,
                delivered_count: 0,
                delivered_value_for_total: 0,
                cancelled_before_dispatch_count: 0,
                cancelled_before_dispatch_value: 0,
                cancelled_after_dispatch_count: 0,
                cancelled_after_dispatch_value: 0,
                cancelled_count: 0,
                total_cancelled_value: 0,
                dispatched_count: 0,
                dispatched_value: 0,
                to_be_dispatched_count: 0,
                to_be_dispatched_value: 0
            };

            // Build report object
            reportData[country.name] = {
                webFlagImage: country.web_flag_png,
                currency: country.currency,

                // --- ORDER ---
                totalOrders: orderData.total_orders || 0,
                totalValue: (orderData.total_value || 0) * crValue,

                // --- DELIVERED ---
                deliveredCount: orderData.delivered_count || 0,
                deliveredValueCM: (orderData.delivered_value_for_total || 0) * crValue,

                // --- CANCELLED ---
                cancelledCount: orderData.cancelled_count || 0,
                cancelledValue: (orderData.total_cancelled_value || 0) * crValue,
                cancelledBeforeCount: orderData.cancelled_before_dispatch_count || 0,
                cancelledBeforeValue: (orderData.cancelled_before_dispatch_value || 0) * crValue,
                cancelledAfterCount: orderData.cancelled_after_dispatch_count || 0,
                cancelledAfterValue: (orderData.cancelled_after_dispatch_value || 0) * crValue,

                // --- DISPATCHED ---
                dispatchedCount: orderData.dispatched_count || 0,
                dispatchedValue: (orderData.dispatched_value || 0) * crValue,

                // --- TO BE DISPATCHED ---
                toBeDispatchedCount: orderData.to_be_dispatched_count || 0,
                toBeDispatchedValue: (orderData.to_be_dispatched_value || 0) * crValue,
            };
        }

        return reportData;
    }
}


export default ConsolidateOsReport;