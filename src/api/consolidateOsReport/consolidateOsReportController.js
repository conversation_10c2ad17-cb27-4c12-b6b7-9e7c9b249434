// src/controllers/consolidateOsReportController.js

import ConsolidateOsReport from '../consolidateOsReport/consolidateOsReportModel.js';
// We don't need exceljs for this report yet as per the PHP code.

export const renderConsolidateOsPage = (req, res) => {
    res.render('reports/consolidate-os-report', {
        user: req.user,
        pageTitle: "Consolidate Order Status Report"
    });
};

export const handleConsolidateOsApi = async (req, res) => {
    try {
        const reportData = await ConsolidateOsReport.getReport(req.query);
        res.json({ success: true, data: reportData });
    } catch (error) {
        console.error("API Error fetching consolidate OS report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};