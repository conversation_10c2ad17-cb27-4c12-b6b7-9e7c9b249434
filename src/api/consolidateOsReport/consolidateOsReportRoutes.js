// src/routes/reportRoutes.js
import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderConsolidateOsPage,
    handleConsolidateOsApi
} from '../consolidateOsReport/consolidateOsReportController.js';

const router = express.Router(); // Initialize router

// Render CP report page
router.get('/consolidate-os-report', verifyToken, (req, res) => {
    res.render('consolidate-os-report', {
        pageTitle: "CONSOLIDATE ORDER STATUS REPORT",
        user: req.user
    });
});

// ===============================================
// CONSOLIDATE ORDER STATUS REPORT ROUTES
// ===============================================
router.get('/reports/consolidate-os', verifyToken, renderConsolidateOsPage);
router.get('/api/reports/consolidate-os', verifyToken, handleConsolidateOsApi);

export default router;
