import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderDailyOrderReportPage,
    handleDailyOrderReportApi,
} from './dailyOrderReportController.js';

const router = express.Router(); // Create router instance

// Order report EJS page
router.get('/reports/dailyOrderReport', verifyToken, renderDailyOrderReportPage);

// API: Get order report data in JSON format
router.get('/api/reports/dailyOrderReport', verifyToken, handleDailyOrderReportApi);

// Order report EJS route (can be removed, since handled below)
router.get('/daily-order-report', verifyToken, (req, res) => {
    res.render('daily-order-report', {
        pageTitle: "Daily Order Report",
        user: req.user
    });
});

export default router; // Export the configured router
