import db from '../../config/db.js';



// Helper function to build date conditions
function getDateConditions(filters) {
  const { fromDate, toDate } = filters;
  let orderCond = 'AND DATE(oo.order_date) = CURDATE()';
  let historyCond = 'AND DATE(ooh.statusdate) = CURDATE()';
  let params = [];

  if (fromDate && !toDate) {
    orderCond = 'AND DATE(oo.order_date) = ?';
    historyCond = 'AND DATE(ooh.statusdate) = ?';
    params = [fromDate];
  } else if (fromDate && toDate) {
    orderCond = 'AND DATE(oo.order_date) BETWEEN ? AND ?';
    historyCond = 'AND DATE(ooh.statusdate) BETWEEN ? AND ?';
    params = [fromDate, toDate];
  }
  return { orderCond, historyCond, params };
}
class DailyOrder {
  static async getDailyOrderReport(filters) {
    const { orderCond, historyCond, params } = getDateConditions(filters);

    // --- Query 1: Get all order data, including platform breakdown ---
    const totalOrderQuery = `
      SELECT
        ac.name AS country_name,
        ac.currency_value,
        ac.web_flag_png,
        ac.currency,
        COUNT(oo.orderid) AS totalOrders,
        SUM(oo.total_amount) AS totalValue,
        SUM(CASE WHEN oo.order_from_id = 1 THEN 1 ELSE 0 END) AS Website_count,
        SUM(CASE WHEN oo.order_from_id = 1 THEN oo.total_amount ELSE 0 END) AS Website_amt,
        SUM(CASE WHEN oo.order_from_id = 2 THEN 1 ELSE 0 END) AS Android_count,
        SUM(CASE WHEN oo.order_from_id = 2 THEN oo.total_amount  ELSE 0 END) AS Android_amt,
        SUM(CASE WHEN oo.order_from_id = 3 THEN 1 ELSE 0 END) AS IOS_count,
        SUM(CASE WHEN oo.order_from_id = 3 THEN oo.total_amount ELSE 0 END) AS IOS_amt,
        SUM(CASE WHEN oo.order_from_id = 4 THEN 1 ELSE 0 END) AS WhatsApp_count,
        SUM(CASE WHEN oo.order_from_id = 4 THEN oo.total_amount ELSE 0 END) AS WhatsApp_amt,
        SUM(CASE WHEN oo.order_from_id = 5 THEN 1 ELSE 0 END) AS Facebook_count,
        SUM(CASE WHEN oo.order_from_id = 5 THEN oo.total_amount  ELSE 0 END) AS Facebook_amt,
        SUM(CASE WHEN oo.order_from_id = 6 THEN 1 ELSE 0 END) AS WebFeed_count,
        SUM(CASE WHEN oo.order_from_id = 6 THEN oo.total_amount ELSE 0 END) AS WebFeed_amt,
        SUM(CASE WHEN oo.order_from_id = 7 THEN 1 ELSE 0 END) AS Telephone_count,
        SUM(CASE WHEN oo.order_from_id = 7 THEN oo.total_amount ELSE 0 END) AS Telephone_amt,
        SUM(CASE WHEN oo.order_from_id = 8 THEN 1 ELSE 0 END) AS Fbfeedlist_count,
        SUM(CASE WHEN oo.order_from_id = 8 THEN oo.total_amount ELSE 0 END) AS Fbfeedlist_amt,
        SUM(CASE WHEN oo.order_from_id = 9 THEN 1 ELSE 0 END) AS WebFeedOs_count,
        SUM(CASE WHEN oo.order_from_id = 9 THEN oo.total_amount ELSE 0 END) AS WebFeedOs_amt,
        SUM(CASE WHEN oo.order_from_id = 10 THEN 1 ELSE 0 END) AS APP_count,
        SUM(CASE WHEN oo.order_from_id = 10 THEN oo.total_amount ELSE 0 END) AS APP_amt,
        SUM(CASE WHEN oo.order_from_id = 11 THEN 1 ELSE 0 END) AS Other_count,
        SUM(CASE WHEN oo.order_from_id = 11 THEN oo.total_amount ELSE 0 END) AS Other_amt
      FROM oms_orders AS oo
      JOIN admin_country AS ac ON oo.country_id = ac.country_id
      WHERE ac.status = 1 
        AND (
            JSON_VALID(oo.customer_details) = 0
            OR (
                JSON_VALID(oo.customer_details) = 1 
                AND LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
            )
        )
      ${orderCond} and oo.order_statusid > 0 and oo.type = "order"
      GROUP BY ac.name, ac.currency_value, ac.web_flag_png, ac.currency
    `;

    //-- Query 2: Dispatch orders (earliest per order)
    const dispatchOrderQuery = `
      SELECT DISTINCT
        ac.name as country_name,
        ooh.orderid,
        (oo.total_amount) as orderValue
      FROM oms_order_history ooh
      JOIN oms_orders oo ON ooh.orderid = oo.orderid
      JOIN admin_country ac ON oo.country_id = ac.country_id
      JOIN (
        SELECT orderid, MIN(statusdate) AS min_dispatch
        FROM oms_order_history
        WHERE order_statusid = 3 AND status LIKE '%dispatch%' AND order_status_type = 'order_status'
        GROUP BY orderid
      ) first_dispatch ON ooh.orderid = first_dispatch.orderid AND ooh.statusdate = first_dispatch.min_dispatch
      WHERE ooh.order_statusid = 3
        AND ooh.order_status_type = 'order_status' 
        AND ac.status = 1
        AND (
            oo.customer_details IS NULL 
            OR NOT JSON_VALID(oo.customer_details)
            OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
        )
      ${historyCond}
    `;


    //-- Query 3: Delivered orders (earliest per order)
      const deliveredOrderQuery = `
      SELECT DISTINCT
        ac.name as country_name,
        ooh.orderid,
        (oo.total_amount) as orderValue
      FROM oms_order_history ooh
      JOIN oms_orders oo ON ooh.orderid = oo.orderid
      JOIN admin_country ac ON oo.country_id = ac.country_id
      JOIN (
        SELECT orderid, MIN(statusdate) AS min_delivery
        FROM oms_order_history
        WHERE order_statusid = 6 and order_status_type = 'order_status'
        GROUP BY orderid
      ) first_delivery ON ooh.orderid = first_delivery.orderid AND ooh.statusdate = first_delivery.min_delivery
      WHERE ooh.order_statusid = 6
        AND ooh.order_status_type = 'order_status'
        AND ac.status = 1
        AND (
            oo.customer_details IS NULL 
            OR NOT JSON_VALID(oo.customer_details)
            OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
        )
      ${historyCond}
    `;

    const [countries] = await db.query('SELECT * FROM admin_country WHERE status = 1');
      const [[totalOrderRows], [dispatchOrderRows], [deliveredOrderRows]] = await Promise.all([
        db.query(totalOrderQuery, params),
        db.query(dispatchOrderQuery, params),
        db.query(deliveredOrderQuery, params)
    ]);


    // --- Process results (same as your current logic) ---
    const ordersData = {};
    const orderPlatform = {};
    const grandTotals = {
        Website: { count: 0, amt: 0 }, Android: { count: 0, amt: 0 },
        IOS: { count: 0, amt: 0 }, WhatsApp: { count: 0, amt: 0 },
        Facebook: { count: 0, amt: 0 }, WebFeed: { count: 0, amt: 0 },
        Telephone: { count: 0, amt: 0 }, Fbfeedlist: { count: 0, amt: 0 },
        WebFeedOs: { count: 0, amt: 0 }, APP: { count: 0, amt: 0 },
        Other: { count: 0, amt: 0 }
    };

    // --- Aggregate dispatch in Node ---
      const dispatchMap = new Map();
      for (const row of dispatchOrderRows) {
        const key = row.country_name;
        if (!dispatchMap.has(key)) {
          dispatchMap.set(key, { orderCount: 0, totalValue: 0 });
        }
        const entry = dispatchMap.get(key);
        entry.orderCount += 1;
        entry.totalValue += parseFloat(row.orderValue) || 0;
      }
    
      // --- Aggregate delivered in Node ---
      const deliveredMap = new Map();
      for (const row of deliveredOrderRows) {
        const key = row.country_name;
        if (!deliveredMap.has(key)) {
          deliveredMap.set(key, { orderCount: 0, totalValue: 0 });
        }
        const entry = deliveredMap.get(key);
        entry.orderCount += 1;
        entry.totalValue += parseFloat(row.orderValue) || 0;
      }

    const totalOrderMap = new Map(totalOrderRows.map(i => [i.country_name, i]));

    for (const country of countries) {
        const countryName = country.name;
        const crValue = parseFloat(country.currency_value) || 1;

        const totalResult = totalOrderMap.get(countryName) || {};
        const dispatchResult = dispatchMap.get(countryName) || {};
        const deliveredResult = deliveredMap.get(countryName) || {};

        const totalOrders = totalResult.totalOrders || 0;
        const totalValue = parseFloat(totalResult.totalValue) || 0;
        const totalDispatchOrders = dispatchResult.orderCount || 0;
        const totalDispatchValue = dispatchResult.totalValue || 0;
        const totalDeliveredOrders = deliveredResult.orderCount || 0;
        const totalDeliveredValue = deliveredResult.totalValue || 0;

        ordersData[countryName] = {
            totalOrders,
            totalDispatchOrders,
            totalDeliveredOrders,
            webFlagImage: country.web_flag_png,
            currency: country.currency,
            totalValue,
            totalDispatchValue,
            totalDeliveredValue,
            totalValueAed: totalValue * crValue,
            totalDispatchValueAed: totalDispatchValue * crValue,
            totalDeliveredValueAed: totalDeliveredValue * crValue
        };

        orderPlatform[countryName] = {
            webFlagImage: country.web_flag_png,
            cvalue: crValue,
            currency: country.currency,
            grandCount: totalOrders,
            grandTot: totalValue,
            Websitetotal: totalResult.Website_count || 0, Website_amt: totalResult.Website_amt || 0,
            Androidtotal: totalResult.Android_count || 0, Android_amt: totalResult.Android_amt || 0,
            IOStotal: totalResult.IOS_count || 0, IOS_amt: totalResult.IOS_amt || 0,
            WhatsApptotal: totalResult.WhatsApp_count || 0, WhatsApp_amt: totalResult.WhatsApp_amt || 0,
            Facebooktotal: totalResult.Facebook_count || 0, Facebook_amt: totalResult.Facebook_amt || 0,
            WebFeedtotal: totalResult.WebFeed_count || 0, WebFeed_amt: totalResult.WebFeed_amt || 0,
            Telephonetotal: totalResult.Telephone_count || 0, Telephone_amt: totalResult.Telephone_amt || 0,
            Fbfeedlisttotal: totalResult.Fbfeedlist_count || 0, Fbfeedlist_amt: totalResult.Fbfeedlist_amt || 0,
            WebFeedOstotal: totalResult.WebFeedOs_count || 0, WebFeedOs_amt: totalResult.WebFeedOs_amt || 0,
            APPtotal: totalResult.APP_count || 0, APP_amt: totalResult.APP_amt || 0,
            Othertotal: totalResult.Other_count || 0, Other_amt: totalResult.Other_amt || 0,
        };

        Object.keys(grandTotals).forEach(platformKey => {
            const count = totalResult[`${platformKey}_count`] || 0;
            const amt = totalResult[`${platformKey}_amt`] || 0;
            grandTotals[platformKey].count += count;
            grandTotals[platformKey].amt += amt * crValue;
        });
    }

    const finalTotals = Object.values(ordersData).reduce((acc, data) => {
        acc.totalOrders += data.totalOrders;
        acc.totalDispatchOrders += data.totalDispatchOrders;
        acc.totalDeliveredOrders += data.totalDeliveredOrders;
        acc.totalValueAed += data.totalValueAed;
        acc.totalDispatchValueAed += data.totalDispatchValueAed;
        acc.totalDeliveredValueAed += data.totalDeliveredValueAed;
        return acc;
    }, {
        totalOrders: 0, totalDispatchOrders: 0, totalDeliveredOrders: 0,
        totalValueAed: 0, totalDispatchValueAed: 0, totalDeliveredValueAed: 0
    });

    const allcount = finalTotals.totalOrders;
    const allTotal = Object.values(grandTotals).reduce((sum, k) => sum + k.amt, 0);

    return {
      ordersData, orderPlatform, grandTotals, allcount, allTotal, ...finalTotals
    };
  }
}


export default DailyOrder;