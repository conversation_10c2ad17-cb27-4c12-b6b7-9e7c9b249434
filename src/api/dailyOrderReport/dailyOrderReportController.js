import dailyOrder from './dailyOrderReportModel.js';

// Render the order report page with user info
export const renderDailyOrderReportPage = (req, res) => {
    res.render('daily-order-report', { user: req.user });
};

// API handler to fetch order report data based on filters
export const handleDailyOrderReportApi = async (req, res) => {
    try {
        const filters = req.query;

        // Get filtered order data from model
        const data = await dailyOrder.getDailyOrderReport(filters);
        res.json({ success: true, data: { ...data, filters } });
    } catch (error) {
        console.error("API Error fetching order report:", error);
        res.status(500).json({ success: false, error: "Server error. Please check logs for details." });
    }
};


