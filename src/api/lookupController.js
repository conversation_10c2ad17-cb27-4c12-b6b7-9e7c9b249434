// Import required queries and DB utility
import {
    runQuery,
    getStoresQuery,
    getSendersQuery,
    getStaffQuery,
    getNationalitiesQuery,
    getPaymentGatewaysQuery,
    getCountriesQuery,
    getBuyersQuery,
    getCategoriesQuery,
    getSearchProductsQuery,
    getProductTableName,
    getSearchSkusQuery,
    getSubCategoriesQuery,
    getSubSubCategoriesQuery,
    getBrandQuery,
    getCatelogueAgentQuery
} from './lookupModel.js';

// Common function to run a query and send response
const fetchLookupData = async (query, res) => {
    try {
        const [rows] = await runQuery(query);
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch data' });
    }
};

// Fetch list of delivery team members (senders)
export const getSenders = (req, res) =>{
    const country = req.query.country || 1;
    const sql = `
        SELECT au.id, CONCAT(au.firstname, ' ', COALESCE(au.lastname, '')) AS text FROM aauth_user_to_group ag LEFT JOIN aauth_users au ON au.id = ag.user_id WHERE ag.group_id = 9 AND au.rstatus = 1 ORDER BY au.firstname ASC
    `;
  fetchLookupData(sql, res);
}

// Fetch list of delivery team members (senders)
export const getEmirate = (req, res) =>{
    const country = req.query.country || 1;
    const sql = `
        SELECT emirateid as id, emirate AS text 
        FROM admin_country_emirates 
        WHERE country_id = ${country}
        ORDER BY emirate ASC
    `;
  fetchLookupData(sql, res);
}

// Fetch list of delivery team members (senders)
export const getArea = (req, res) =>{
    const emirate = req.query.emirate;
    const sql = `
        SELECT area_id as id, name AS text 
        FROM admin_country_area 
        WHERE emirateid = ${emirate}
        ORDER BY name ASC
    `;
  fetchLookupData(sql, res);
}

// ==========================
// LOOKUP ROUTES CONTROLLERS
// ==========================

// Get store list
export const getStores = (req, res) =>
    fetchLookupData(getStoresQuery(), res);

// Get sender list
// export const getSenders = (req, res) =>
//     fetchLookupData(getSendersQuery(), res);
// 

// Get staff list
export const getStaff = (req, res) =>
    fetchLookupData(getStaffQuery(), res);

// Get nationality list
export const getNationalities = (req, res) =>
    fetchLookupData(getNationalitiesQuery(), res);

// Get payment gateway list
export const getPaymentGateways = (req, res) =>{
    const { country } = req.query; 
    fetchLookupData(getPaymentGatewaysQuery(country), res);
}
    

// Get country list
export const getCountries = (req, res) =>
    fetchLookupData(getCountriesQuery(), res);

// Get buyer list
export const getBuyers = async (req, res) => {
    try {
        const [rows] = await runQuery(getBuyersQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch buyers' });
    }
};

// Get category list
export const getCategories = async (req, res) => {
    try {
        const [rows] = await runQuery(getCategoriesQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch categories' });
    }
};

export const getSubCategories = async (req, res) => {
    try {
        const [rows] = await runQuery(getSubCategoriesQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch categories' });
    }
};

export const getSubSubCategories = async (req, res) => {
    try {
        const [rows] = await runQuery(getSubSubCategoriesQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch categories' });
    }
};

export const getBrand = async (req, res) => {
    try {
        const [rows] = await runQuery(getBrandQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch brand' });
    }
};


export const getCatelogueAgent = async (req, res) => {
    try {
        const [rows] = await runQuery(getCatelogueAgentQuery());
        res.json(rows);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch catelogue agent' });
    }
};



//export order from
export const getOrderFrom = (req, res) =>
fetchLookupData("SELECT list_value as id,list_lable as text FROM admin_lists where list_type = 'order_from' ORDER BY list_lable ASC", res);

//export Cancel Reason
export const getCancelReason = (req, res) =>
fetchLookupData("SELECT distinct(staff_comments) as text , staff_comments as id FROM oms_order_history where order_statusid in (7,11)", res);

// Search for products based on category and search term

// Product search based on term, category and country
export const searchProducts = async (req, res) => {
    try {
        const { term, categoryId, country } = req.query;

        if (!term || term.length < 2) {
            return res.json([]);
        }

        const table = getProductTableName(country || '1'); // Get dynamic product table
        //const hasCategory = !!categoryId;
        const hasCategory = categoryId !== undefined && categoryId !== null && categoryId !== '';


        const query = getSearchProductsQuery(table, hasCategory);
        const params = [ `%${term}%` ];
        if (hasCategory) params.push(categoryId);

        const [rows] = await runQuery(query, params);
        res.json(rows);
    } catch (error) {
        console.error("Product search error:", error);
        res.status(500).json({ error: 'Failed to search for products' });
    }
};

// SKU search with optional buyer filter
export const searchSkus = async (req, res) => {
    try {
        const searchTerm = req.query.q || '';
        const buyerId = req.query.buyer || null;

        if (searchTerm.length < 2) {
            return res.json({ results: [] });
        }

        const hasBuyer = !!buyerId;
        const query = getSearchSkusQuery(hasBuyer);
        const params = [`%${searchTerm}%`];
        if (hasBuyer) params.push(buyerId);

        const [skus] = await runQuery(query, params);
        res.json({ results: skus });
    } catch (error) {
        console.error("Error searching SKUs:", error);
        res.status(500).json({ results: [] });
    }
};

// Fetches all suppliers for Select2 with optional case-insensitive search
export const getSuppliers = (req, res) => {
    const term = req.query.term;
    let sql = "SELECT supplierid as id, company_name as text FROM admin_supplier";
    if (term && term.trim().length > 0) {
        sql += ` WHERE LOWER(company_name) LIKE '%${term.toLowerCase().replace(/'/g, "''")}%'`;
    }
    sql += " ORDER BY company_name ASC";
    fetchLookupData(sql, res);
};