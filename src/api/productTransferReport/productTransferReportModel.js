import db from '../../config/db.js';

class ProductTransferReport {
    
    static async getReport(filters) {
        const { store, status, fromDate, toDate } = filters;

        let conditions = [];
        const params = [];

        conditions.push('ss.transfer_from = ?');
        params.push(1);

        if (store) {
            conditions.push('ss.transfer_to = ?');
            params.push(store);
        }

        if (status) {
            conditions.push('ss.status = ?');
            params.push(status);
        }

        if (fromDate && !toDate) {
            conditions.push('DATE(ssp.order_date) = ?'); 
            params.push(fromDate);
        } else if (fromDate && toDate) {
            conditions.push('DATE(ssp.order_date) BETWEEN ? AND ?');
            params.push(fromDate, toDate);
        }
        
        const whereClause = `WHERE ${conditions.join(' AND ')}`;

        const sql = `
            SELECT 
                ss.bill_no,
                ssp.order_date,
                sp.sku AS product_code,
                sp.title AS product_name,
                ssp.inventory AS transfer_qty,
                COALESCE(spi.inventory, 0) AS stock_quantity,
                ssp.price_exclusive,
                ssp.vat,
                ssp.cost,
                ss.expected_delivery_date,
                ss.transfer_media,
                sa.firstname AS buyer_name,
                ss.transfer_to AS country_id
            -- Start from the main transfer table to ensure transfers always show up
            FROM procurement_stockhandover ss
            
            -- Left join products, so even if a transfer has no products, it might show (adjust if needed)
            LEFT JOIN procurement_stockhandover_products ssp ON ss.handover_id = ssp.handover_id
            LEFT JOIN catalog_product sp ON sp.productid = ssp.productid
            
            -- Left join inventory specifically for the destination country
            LEFT JOIN catalog_product_inventory spi ON spi.productid = sp.productid AND spi.country_id = ss.transfer_to
            
            LEFT JOIN aauth_users sa ON sa.id = sp.buyer_id
            ${whereClause}
            -- Filter out transfers that have no products listed
            AND ssp.id IS NOT NULL 
            ORDER BY ss.bill_date DESC
        `;
        
        const [reportData] = await db.query(sql, params);

        const processedData = reportData.map(row => ({
            ...row,
            charge: (parseFloat(row.price_exclusive) || 0) - (parseFloat(row.cost) || 0)
        }));

        const totals = processedData.reduce((acc, row) => {
            acc.total_qty += parseInt(row.transfer_qty, 10) || 0;
            return acc;
        }, { total_qty: 0 });

        return { reportData: processedData, totals };
    }
}

export default ProductTransferReport;