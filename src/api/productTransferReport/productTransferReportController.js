import ProductTransferReport from '../productTransferReport/productTransferReportModel.js';
import exceljs from 'exceljs';

export const renderProductTransferReportPage = (req, res) => {
    res.render('product-transfer-report', { user: req.user, pageTitle: "Product Transfer Report" });
};

export const handleProductTransferReportApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.fromDate || !filters.toDate) {
            return res.status(400).json({ success: false, error: "From Date and To Date are required." });
        }
        const data = await ProductTransferReport.getReport(filters);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching product transfer report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

export const handleProductTransferReportExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { reportData } = await ProductTransferReport.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('ProductTransferReport');

        worksheet.columns = [
            { header: 'No', key: 'sl_no', width: 8 },
            { header: 'Bill No', key: 'bill_no', width: 15 },
            { header: 'Send Date', key: 'order_date', width: 25 },
            { header: 'SKU', key: 'product_code', width: 20 },
            { header: 'Title', key: 'product_name', width: 50 },
            { header: 'Qty', key: 'transfer_qty', width: 10 },
            { header: 'Price', key: 'price_exclusive', width: 12, style: { numFmt: '#,##0.00' } },
            { header: 'VAT', key: 'vat', width: 12, style: { numFmt: '#,##0.00' } },
            { header: 'Pur. Cost', key: 'cost', width: 12, style: { numFmt: '#,##0.00' } },
            { header: 'Charge', key: 'charge', width: 12, style: { numFmt: '#,##0.00' } },
            { header: 'D. Mode', key: 'transfer_media', width: 15 },
            { header: 'Buyer', key: 'buyer_name', width: 20 },
        ];
        worksheet.getRow(1).font = { bold: true };

        reportData.forEach((row, index) => {
            worksheet.addRow({
                sl_no: index + 1, ...row,
                order_date: new Date(row.order_date).toLocaleString('en-GB', { timeZone: 'Asia/Dubai' }),
            });
        });

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="ProductTransferReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};