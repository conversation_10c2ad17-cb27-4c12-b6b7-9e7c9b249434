import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderProductTransferReportPage,
    handleProductTransferReportApi,
    handleProductTransferReportExcel
} from '../productTransferReport/productTransferReportController.js';

const router = express.Router();

router.get('/product-transfer-report', verifyToken, (req, res) => {
    res.render('product-transfer-report', {
        pageTitle: "PRODUCT TRANSFER REPORT",
        user: req.user
    });
});

router.get('/product-transfer-report', verifyToken, renderProductTransferReportPage);

router.get('/api/reports/product-transfer', verifyToken, handleProductTransferReportApi);

router.get('/api/reports/product-transfer/export', verifyToken, handleProductTransferReportExcel);

export default router;