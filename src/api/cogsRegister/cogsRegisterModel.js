import db from '../../config/db.js';



class CogsRegisterModel {
    static async getCogsRegister(filters) {
        const { country, fromDate, toDate } = filters;

        let whereClauses = [];
        let queryParams = [];

        const countryId = parseInt(country, 10);

        if (countryId) {
            whereClauses.push(`oo.country_id = ?`);
            queryParams.push(countryId);
        }

        // ✅ Use earliest delivered date from order_history instead of order_date
        if (fromDate && toDate) {
            whereClauses.push(`DATE(first_delivery.min_delivery) BETWEEN DATE(?) AND DATE(?)`);
            queryParams.push(fromDate, toDate);
        } else if (fromDate) {
            whereClauses.push(`DATE(first_delivery.min_delivery) = DATE(?)`);
            queryParams.push(fromDate);
        }

        // Always Delivered
        whereClauses.push(`oo.order_statusid = 6`);

        // Exclude test customers
        whereClauses.push(`
            (
                oo.customer_details IS NULL
                OR NOT JSON_VALID(oo.customer_details)
                OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
            )
        `);

        const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
 const query = `
           SELECT 
                ac.name,
                ac.currency,
                oo.order_ref_code AS order_id,
                DATE(oo.order_date) AS order_date,
                DATE(first_delivery.min_delivery) AS date,   
                c3.category_name AS category,
                c2.category_name AS sub_category,
                cp.title AS product_name,
                odd.product_sku AS sku,
                odd.cost AS cost_price,
                (odd.cost*odd.quantity) AS cost,
                oo.tax_amount,
                oo.total_amount,
                odd.quantity AS qty,
                a_s.firstname AS buyer,
                ac.name AS country_name,
                ac.currency AS country_currency,
               
                -- Sales Price Breakdown
               ROUND(
                CASE 
                    WHEN oo.country_id IN (1, 2) THEN ((odd.cost - (odd.cost * 5 / 105)) * odd.quantity)
                    WHEN oo.country_id = 6 THEN ((odd.cost - (odd.cost * 10 / 110)) * odd.quantity)
                    ELSE (odd.cost * odd.quantity)
                END, 2
            ) AS cost_before_vat,

            ROUND(
                CASE 
                    WHEN oo.country_id IN (1, 2) THEN ((odd.cost * odd.quantity) * 5.0 / 105.0)
                    WHEN oo.country_id = 6 THEN ((odd.cost * odd.quantity) * 10.0 / 110.0)
                    ELSE 0 
                END, 2
            ) AS purchase_vat,
             supp.company_name AS supplier,
             DATE_FORMAT(pur.entry_date, '%Y-%m-%d') AS purchase_date
            FROM oms_orders oo
            JOIN (
                SELECT orderid, MIN(statusdate) AS min_delivery
                FROM oms_order_history
                WHERE order_statusid = 6
                AND order_status_type = 'order_status'
                GROUP BY orderid
            ) first_delivery ON oo.orderid = first_delivery.orderid

            -- Earliest dispatch date per order
            LEFT JOIN (
                SELECT h.orderid, MIN(h.statusdate) AS dispatch_date
                FROM oms_order_history h
                WHERE h.order_status_type = 'order_status'
                AND h.order_statusid = 3
                GROUP BY h.orderid
            ) d ON oo.orderid = d.orderid

            LEFT JOIN admin_country ac ON oo.country_id = ac.country_id
            LEFT JOIN oms_order_detail odd ON oo.orderid = odd.orderid
            LEFT JOIN catalog_product cp ON odd.productid = cp.productid
            LEFT JOIN catalog_category AS c1 ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category AS c2 ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid) AND c2.category_level = 2
            LEFT JOIN catalog_category AS c3  ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
                AND c3.category_level = 1
            LEFT JOIN aauth_users AS a_s ON cp.buyer_id = a_s.id

                LEFT JOIN procurement_purchase pur 
                ON pur.purchase_id = (
                    SELECT ppp.purchase_id
                    FROM procurement_purchase_products ppp
                    JOIN procurement_purchase pp ON pp.purchase_id = ppp.purchase_id
                    WHERE ppp.productid = odd.productid
                        AND pp.entry_date <= d.dispatch_date AND pp.country_id = ${countryId}
                    ORDER BY pp.entry_date DESC
                    LIMIT 1
                )
                LEFT JOIN admin_supplier supp ON pur.supplierid = supp.supplierid


            WHERE oo.country_id = ?
            AND DATE(first_delivery.min_delivery) BETWEEN DATE(?) AND DATE(?)
            AND oo.order_statusid = 6
            AND (
                    oo.customer_details IS NULL
                    OR NOT JSON_VALID(oo.customer_details)
                    OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
                )
            ORDER BY order_id DESC`; 

        //console.log(db.format(query, queryParams));  

        const [data] = await db.query(query, queryParams);


        return { data};
    }
}



export default CogsRegisterModel;