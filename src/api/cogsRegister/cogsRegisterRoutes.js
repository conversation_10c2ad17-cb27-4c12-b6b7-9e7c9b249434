import express from 'express';
import { renderCogsRegisterPage, handleCogsRegisterApi, handleCogsRegisterExcel } from '../cogsRegister/cogsRegisterController.js';
import { verifyToken } from '../../middleware/authMiddleware.js';

const router = express.Router();


router.get('/cogs-register-report', verifyToken, (req, res) => {
    res.render('cogs-register', {
        pageTitle: "Cogs Register Report",
        user: req.user
    });
});


router.use(verifyToken);

router.get('/cogs-register', renderCogsRegisterPage);
router.get('/api/reports/cogs-register', handleCogsRegisterApi);
router.get('/api/reports/cogs-register/export', handleCogsRegisterExcel);

export default router;