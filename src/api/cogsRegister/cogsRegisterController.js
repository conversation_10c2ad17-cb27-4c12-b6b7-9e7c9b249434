import cogsRegisterModel from './cogsRegisterModel.js';
import exceljs from 'exceljs';

const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d)) return ''; // handle invalid date
    const year = d.getFullYear();
    const month = d.getMonth() + 1;
    const day = d.getDate();
    return `${year}-${month}-${day}`;
};

// Render the COGS Register report page
export const renderCogsRegisterPage = (req, res) => {
    res.render('cogs-register', { user: req.user });
};

// API handler to fetch report data
export const handleCogsRegisterApi = async (req, res) => {
    try {
        const filters = req.query;

        if (!filters.country) {
            return res.status(400).json({ success: false, error: "Country is a required parameter." });
        }

        const { data, summary } = await cogsRegisterModel.getCogsRegister(filters);
        res.json({ success: true, data: { data, summary,filters } });
    } catch (error) {
        console.error("API Error fetching COGS Register:", error);
        res.status(500).json({ success: false, error: "An error occurred while fetching the report." });
    }
};

// Handle Excel export for the report
export const handleCogsRegisterExcel = async (req, res) => {
    try {
        const filters = req.query;
        const { data } = await cogsRegisterModel.getCogsRegister(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('COGS Register');

        // Define worksheet columns
        worksheet.columns = [
            { header: 'S.No', key: 's_no', width: 8 },
            { header: 'Region', key: 'country_name', width: 15 },
            { header: 'Currency', key: 'country_currency', width: 15 },
            { header: 'Order ID', key: 'order_id', width: 15 },
            { header: 'Order Date', key: 'date', width: 15 },
            { header: 'Delivery Date', key: 'delivery_date', width: 15 },
            { header: 'Purchase Date', key: 'purchase_date', width: 15 },
            { header: 'Category', key: 'category', width: 20 },
            { header: 'Sub Category', key: 'sub_category', width: 20 },
            { header: 'Product', key: 'product_name', width: 40 },
            { header: 'SKU', key: 'sku', width: 20 },
            { header: 'QTY', key: 'qty', width: 10 },
            { header: 'Purchase Price (Before VAT)', key: 'purchase_price_before_vat', width: 22, style: { numFmt: '#,##0.00' } },
            { header: 'VAT on Purchase Price', key: 'purchase_vat', width: 20, style: { numFmt: '#,##0.00' } },
            { header: 'Total Cost of the product', key: 'total_cost', width: 22, style: { numFmt: '#,##0.00' } },
            { header: 'Supplier', key: 'supplier', width: 25 },
            { header: 'Buyer', key: 'buyer', width: 25 },
        ];

        // Bold header row
        worksheet.getRow(1).font = { bold: true };

        // Populate worksheet rows
        data.forEach((item, index) => {
            const rowData = {
                s_no: index + 1,
                country_name: item.country_name,
                country_currency: item.country_currency,
                order_id: item.order_id,
                date: formatDate(item.order_date),
                delivery_date: formatDate(item.date),
                purchase_date: formatDate(item.purchase_date),
                category: item.category,
                sub_category: item.sub_category,
                product_name: item.product_name,
                sku: item.sku,
                qty: parseFloat(item.qty),
                purchase_price_before_vat: parseFloat(item.cost_before_vat),
                purchase_vat: parseFloat(item.purchase_vat),
                total_cost: parseFloat(item.cost),
                supplier: item.supplier,
                buyer: item.buyer,
            };
            worksheet.addRow(rowData);
        });

        // Set response headers for Excel download
        res.setHeader('Content-Type', 'application/vnd.ms-excel');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="COGS-Register-${new Date().toISOString().slice(0, 10)}.xlsx"`
        );

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};