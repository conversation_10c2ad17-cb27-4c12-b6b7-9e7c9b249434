import db from '../../config/db.js';

class DeliveryCohortReport {
    
    static async getReport(filters) {
        const { fromDate, toDate } = filters;
        let countries = filters['country[]'] || filters.country; 
        let orderFroms = filters['orderFrom[]'] || filters.orderFrom;

        if (!fromDate || !countries) {
            return [];
        }
        
        if (!Array.isArray(countries)) {
            countries = [countries];
        }
        
        if (orderFroms && !Array.isArray(orderFroms)) {
            orderFroms = [orderFroms];
        }

        const params = [];
        const conditions = [];

        const countryPlaceholders = countries.map(() => '?').join(',');
        conditions.push(`o.country_id IN (${countryPlaceholders})`);
        params.push(...countries);

        if (toDate) {
            conditions.push('DATE(o.order_date) BETWEEN ? AND ?');
            params.push(fromDate, toDate);
        } else {
            conditions.push('DATE(o.order_date) = ?');
            params.push(fromDate);
        }
        
        if (orderFroms && orderFroms.length > 0) {
            const placeholders = orderFroms.map(() => '?').join(',');
            conditions.push(`o.orderfrom IN (${placeholders})`);
            params.push(...orderFroms);
        }

        const whereClause = `WHERE ${conditions.join(' AND ')}`;

        const sql = `
            WITH base_data AS (
                -- First, efficiently gather only the necessary orders and calculate the delivery day difference
                SELECT
                    o.orderid,
                    DATE(o.order_date) AS order_date,
                    DATEDIFF(
                        MIN(CASE WHEN oh.order_statusid = 6 THEN DATE(oh.statusdate) END), 
                        DATE(o.order_date)
                    ) AS day_diff
                FROM oms_orders o
                -- This JOIN is optimized to only look for 'Delivered' statuses
                LEFT JOIN oms_order_history oh ON o.orderid = oh.orderid AND oh.order_statusid = 6
                -- The WHERE clause is dynamically inserted here, allowing indexes to be used
                ${whereClause}
                GROUP BY o.orderid, DATE(o.order_date)
            ),
            aggregated_data AS (
                -- Second, aggregate the results by date, calculating total orders and all cumulative counts in one pass
                SELECT
                    order_date,
                    COUNT(orderid) AS total_orders,
                    COUNT(CASE WHEN day_diff = 0 THEN 1 END) AS d0_count,
                    COUNT(CASE WHEN day_diff >= 0 AND day_diff <= 1 THEN 1 END) AS d1_count,
                    COUNT(CASE WHEN day_diff >= 0 AND day_diff <= 2 THEN 1 END) AS d2_count,
                    COUNT(CASE WHEN day_diff >= 0 AND day_diff <= 3 THEN 1 END) AS d3_count,
                    COUNT(CASE WHEN day_diff >= 0 AND day_diff <= 4 THEN 1 END) AS d4_count
                FROM base_data
                GROUP BY order_date
            )
            -- Finally, select and format the results into the required string format
            SELECT
                DATE(a.order_date) AS "Date",
                a.total_orders AS "Orders",
                CONCAT(a.d0_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d0_count / a.total_orders * 100), 0), '%)') AS "D0",
                CONCAT(a.d1_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d1_count / a.total_orders * 100), 0), '%)') AS "D1",
                CONCAT(a.d2_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d2_count / a.total_orders * 100), 0), '%)') AS "D2",
                CONCAT(a.d3_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d3_count / a.total_orders * 100), 0), '%)') AS "D3",
                CONCAT(a.d4_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d4_count / a.total_orders * 100), 0), '%)') AS "D4",
                CONCAT(a.d4_count, ' (', ROUND(IF(a.total_orders = 0, 0, a.d4_count / a.total_orders * 100), 0), '%)') AS "< D5"
            FROM aggregated_data a
            ORDER BY "Date" DESC;
        `;

        try {
            const [rows] = await db.query(sql, params);
            return rows;
        } catch (error) {
            console.error('Error fetching delivery cohort report:', error);
            return [];
        }
    }
}

export default DeliveryCohortReport;