import DeliveryCohortReport from './deliveryCohortReportModel.js';
import exceljs from 'exceljs';

export const renderPage = (req, res) => {
    res.render('delivery-cohort-report', { user: req.user, pageTitle: "Delivery Cohort Report" });
};

export const handleApi = async (req, res) => {
    try {
        const filters = req.query;
        if (!filters.fromDate || (!filters['country[]'] && !filters.country)) {
            return res.status(400).json({ success: false, error: "From Date and Country are required filters." });
        }
        const reportData = await DeliveryCohortReport.getReport(filters);
        res.json({ success: true, data: reportData });
    } catch (error) {
        console.error("API Error fetching delivery cohort report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

export const handleExcel = async (req, res) => {
    try {
        const filters = req.query;
        
        const reportRows = await DeliveryCohortReport.getReport(filters);
        
        if (!reportRows || reportRows.length === 0) {
            return res.status(404).send("No data found for the selected filters to generate an Excel file.");
        }

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet(`Delivery Cohort Report`);
        
        worksheet.columns = [
            { header: 'Date', key: 'Date', width: 15 },
            { header: 'Orders', key: 'Orders', width: 12 },
            { header: 'D0', key: 'D0', width: 20 },
            { header: 'D1', key: 'D1', width: 20 },
            { header: 'D2', key: 'D2', width: 20 },
            { header: 'D3', key: 'D3', width: 20 },
            { header: 'D4', key: 'D4', width: 20 },
            { header: '< D5', key: '< D5', width: 22 }, 
        ];
        worksheet.getRow(1).font = { bold: true };
        
        reportRows.forEach(row => {
            const addedRow = worksheet.addRow(row);
            addedRow.getCell('< D5').font = { bold: true };
        });
        
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="DeliveryCohortReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate the Excel file.");
    }
};