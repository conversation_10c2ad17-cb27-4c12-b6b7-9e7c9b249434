import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';
import {
    renderPage,
    handleApi,
    handleExcel
} from '../deliveryCohortReport/deliveryCohortReportController.js';

const router = express.Router();

// Render the Delivery Cohort Report EJS page
router.get('/delivery-cohort-report', verifyToken, renderPage);

// API: Fetch Delivery Cohort Report data in JSON format
router.get('/api/reports/delivery-cohort', verifyToken, handleApi);

// API: Export Delivery Cohort Report data to Excel
router.get('/api/reports/delivery-cohort/export', verifyToken, handleExcel);

export default router;