import express from 'express'; // Import express framework
import { verifyToken } from '../../middleware/authMiddleware.js'; // Middleware to verify auth token

import {
    renderOrderPendingPage,           // Controller to render report listing
    handleOrderPendingApi,            // Controller to handle API data fetch
    handleOrderPendingExcel           // Controller to handle Excel export
} from '../orderPendingReport/orderPendingReportController.js';

const router = express.Router(); // Create router instance

// Route to render EJS view for Order Pending Report page
router.get('/order-pending-report', verifyToken, (req, res) => {
    res.render('order-pending-report', {
        pageTitle: "ORDER PENDING REPORT",
        user: req.user
    });
});

// ======================================================
// ORDER PENDING REPORT ROUTES
// ======================================================

// Render Order Pending Report page with filters and data
router.get('/reports/order-pending', verifyToken, renderOrderPendingPage);

// API route to fetch filtered Order Pending data (JSON)
router.get('/api/reports/order-pending', verifyToken, handleOrderPendingApi);

// API route to export Order Pending data to Excel
router.get('/api/reports/order-pending/export', verifyToken, handleOrderPendingExcel);

export default router; // Export router
