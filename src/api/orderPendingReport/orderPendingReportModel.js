import db from '../../config/db.js';

class OrderPendingOld {
    static async getReport(filters) {
        const whereConditions = [
            "oo.order_statusid = 1",
            "oo.country_id = ?"
        ];
        const queryParams = [filters.country];

        if (filters.category) {
            whereConditions.push("cp.categoryid = ?");
            queryParams.push(filters.category);
        }

        if (filters.product) {
            whereConditions.push("cp.productid = ?");
            queryParams.push(filters.product);
        }

        if (filters.buyer) {
            whereConditions.push("(cp.buyer_id = ? OR cp.buyer_id = 0)");
            queryParams.push(filters.buyer);
        }

        if (filters.fromDate && filters.toDate) {
            whereConditions.push("date(oo.order_date) BETWEEN ? AND ?");
            queryParams.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            whereConditions.push("date(oo.order_date) = ?)");
            queryParams.push(filters.fromDate, filters.fromDate);
        }

        const finalQuery = `
            WITH StockHandoverAgg AS (
                SELECT
                    b.productid,
                    SUM(b.inventory) AS trans_qty
                FROM procurement_stockhandover a
                JOIN procurement_stockhandover_products b 
                    ON a.handover_id = b.handover_id
                WHERE a.status = 'Pending'
                GROUP BY b.productid
            )
            SELECT
                inv.cost,
                inv.selling_price AS selling_price, 
                inv.inventory AS stock_qty,
                c3.category_name AS category_name,
                cp.title AS product_name,
                buyer.firstname AS buyer_name,
                sup.company_name AS supplier_name,
                sup.secondary_contact AS supplier_contact,
                cp.sku AS sku,
                ood.quantity AS order_qty,
                oo.order_ref_code,
                sh.trans_qty,
                DATE_FORMAT(oo.order_date, '%d-%m-%dY') AS order_date,
                DATEDIFF(CURDATE(), oo.order_date) AS days_diff
            FROM oms_orders AS oo
            LEFT JOIN oms_order_detail AS ood 
                ON oo.orderid = ood.orderid
            LEFT JOIN catalog_product AS cp 
                ON ood.productid = cp.productid
            LEFT JOIN catalog_category AS c1 
              ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category AS c2 
              ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
              AND c2.category_level = 2
            LEFT JOIN catalog_category AS c3 
              ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
              AND c3.category_level = 1
            LEFT JOIN admin_country AS ac 
                ON oo.country_id = ac.country_id
            LEFT JOIN catalog_product_inventory AS inv 
                ON inv.productid = cp.productid 
                AND inv.country_id = oo.country_id
            LEFT JOIN StockHandoverAgg AS sh 
                ON sh.productid = cp.productid
            LEFT JOIN aauth_users AS buyer 
                ON cp.buyer_id = buyer.id
            LEFT JOIN admin_supplier AS sup 
                ON ood.supplierid = sup.supplierid
            WHERE ${whereConditions.join(" AND ")}
        `;

        console.log(db.format(finalQuery, queryParams));


        const [results] = await db.query(finalQuery, queryParams);
        return results;
    }
}


class OrderPending {
    static async getReport(filters) {
        const whereConditions = ["oo.order_statusid = 1"];
        const queryParams = [];

        // Country condition
        if (filters.country) {
            whereConditions.push("oo.country_id = ?");
            queryParams.push(filters.country);
        }

        // Category filter
        if (filters.category) {
            whereConditions.push("cp.categoryid = ?");
            queryParams.push(filters.category);
        }

        // Product filter
        if (filters.product) {
            whereConditions.push("cp.productid = ?");
            queryParams.push(filters.product);
        }

        // Buyer filter
        if (filters.buyer) {
            whereConditions.push("(cp.buyer_id = ? OR cp.buyer_id = 0)");
            queryParams.push(filters.buyer);
        }

        // Date filters
        if (filters.fromDate && filters.toDate) {
            whereConditions.push("DATE(oo.order_date) BETWEEN ? AND ?");
            queryParams.push(filters.fromDate, filters.toDate);
        } else if (filters.fromDate) {
            whereConditions.push("DATE(oo.order_date) = ?");
            queryParams.push(filters.fromDate);
        }

        // Build final query
        const finalQuery = `
            WITH StockHandoverAgg AS (
                SELECT
                    b.productid,
                    a.transfer_to AS country_id,
                    SUM(b.inventory) AS trans_qty
                FROM procurement_stockhandover a
                JOIN procurement_stockhandover_products b 
                    ON a.handover_id = b.handover_id
                WHERE a.status = 'Pending'
                GROUP BY b.productid, a.transfer_to
            )
            SELECT
                ac.name AS country_name,
                oo.country_id,
                inv.cost,
                inv.selling_price AS selling_price,
                inv.inventory AS stock_qty,
                c3.category_name AS category_name,
                c2.category_name AS sucategory,
                cp.title AS product_name,
                buyer.firstname AS buyer_name,
                sup.company_name AS supplier_name,
                sup.secondary_contact AS supplier_contact,
                cp.sku AS sku,
                ood.quantity AS order_qty,
                oo.order_ref_code,
                sh.trans_qty,
                DATE_FORMAT(oo.order_date, '%Y-%m-%d') AS order_date,
                bd.brand_name as brand_name,
                DATEDIFF(CURDATE(), oo.order_date) AS days_diff
            FROM oms_orders AS oo
            LEFT JOIN oms_order_detail AS ood 
                ON oo.orderid = ood.orderid
            LEFT JOIN catalog_product AS cp 
                ON ood.productid = cp.productid
            LEFT JOIN catalog_category AS c1 
              ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category AS c2 
              ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
              AND c2.category_level = 2
            LEFT JOIN catalog_category AS c3 
              ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
              AND c3.category_level = 1
            LEFT JOIN admin_country AS ac 
                ON oo.country_id = ac.country_id
            LEFT JOIN catalog_product_inventory AS inv 
                ON inv.productid = cp.productid 
                AND inv.country_id = oo.country_id
            LEFT JOIN StockHandoverAgg AS sh 
                ON sh.productid = cp.productid
                AND sh.country_id = oo.country_id
            LEFT JOIN aauth_users AS buyer 
                ON cp.buyer_id = buyer.id
            LEFT JOIN admin_supplier AS sup 
                ON ood.supplierid = sup.supplierid
            LEFT JOIN catalog_brand AS bd
                ON cp.brandid = bd.brandid
            WHERE ${whereConditions.join(" AND ")}
             AND (
                oo.customer_details IS NULL
                OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
            )`;

       // console.log(db.format(finalQuery, queryParams));

        const [results] = await db.query(finalQuery, queryParams);
        return results;
    }
}


export default OrderPending;