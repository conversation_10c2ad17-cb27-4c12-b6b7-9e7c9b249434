import OrderPending from './orderPendingReportModel.js';
import exceljs from 'exceljs';

// render order pending page
export const renderOrderPendingPage = (req, res) => {
    res.render('order-pending-report', {
        user: req.user,
        pageTitle: "Order Pending Report"
    });
};

// api handler to fetch pending order report data
export const handleOrderPendingApi = async (req, res) => {
    try {
        const filters = req.query;

        //if (!filters.country) {
            //return res.status(400).json({
                //success: false,
                //error: "Country is a required filter."
           // });
        //}

        const data = await OrderPending.getReport(filters);
        res.json({ success: true, data });

    } catch (error) {
        console.error("API Error fetching pending order report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// excel export handler for pending order report
export const handleOrderPendingExcel = async (req, res) => {
    try {
        const filters = req.query;
        const results = await OrderPending.getReport(filters);

        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Pending Purchase Report');

        // define excel columns
        worksheet.columns = [
            { header: 'Country', key: 'country_name', width: 30 },
            { header: 'Order Date', key: 'order_date', width: 25 },
            { header: 'Order Id', key: 'order_ref_code', width: 25 },
            { header: 'Category Name', key: 'category_name', width: 30 },
            { header: 'Sub Category', key: 'sucategory', width: 25 },
            { header: 'Brand', key: 'brand_name', width: 25 },
            { header: 'Buyer Name', key: 'buyer_name', width: 25 },
            { header: 'SKU', key: 'sku', width: 20 },
            { header: 'Product Name', key: 'product_name', width: 60 },
            { header: 'Order Qty', key: 'order_qty', width: 15 },
            { header: 'In Transit', key: 'trans_qty', width: 15 },
            { header: 'Stock Qty', key: 'stock_qty', width: 15 },
             { header: 'Selling Price', key: 'selling_price', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Cost', key: 'cost', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'Supplier Name', key: 'supplier_name', width: 30 },
            { header: 'Supplier Contact', key: 'supplier_contact', width: 20 },
            { header: 'Delay', key: 'days_diff', width: 20 }
           
        ];

        worksheet.getRow(1).font = { bold: true };

        // add data rows
        results.forEach(row => {
            worksheet.addRow(row);
        });

        // set response headers and send file
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="PendingPurchaseReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);

        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
