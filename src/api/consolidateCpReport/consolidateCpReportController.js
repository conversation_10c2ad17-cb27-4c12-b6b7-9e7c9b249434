import ConsolidateCpReport from '../consolidateCpReport/consolidateCpReportModel.js';
import exceljs from 'exceljs';

// Render category performance report page
export const renderConsolidateCpPage = (req, res) => {
    res.render('consolidate-cp-report', {
        user: req.user,
        pageTitle: "Consolidate Category Performance"
    });
};

// Return category performance data as JSON
export const handleConsolidateCpApi = async (req, res) => {
    try {
        const data = await ConsolidateCpReport.getReport(req.query);
        res.json({ success: true, data });
    } catch (error) {
        console.error("API Error fetching consolidate CP report:", error);
        res.status(500).json({ success: false, error: "Server error occurred." });
    }
};

// Export category performance report as Excel file
export const handleConsolidateCpExcel = async (req, res) => {
    try {
        const results = await ConsolidateCpReport.getReport(req.query);
        const workbook = new exceljs.Workbook();
        const worksheet = workbook.addWorksheet('Consolidate Category Report');

        // Define Excel columns
        worksheet.columns = [
            { header: 'CATEGORY', key: 'category', width: 25 },
            { header: 'METRIC', key: 'metric', width: 15 },
            { header: 'UAE', key: 'uae', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'OMAN', key: 'oman', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'QATAR', key: 'qatar', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'KUWAIT', key: 'kuwait', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'BAHRAIN', key: 'bahrain', width: 15, style: { numFmt: '#,##0.00' } },
            { header: 'TOTAL AED', key: 'total', width: 20, style: { numFmt: '#,##0.00' } },
        ];
        worksheet.getRow(1).font = { bold: true };

        // Add rows for each category with calculated totals
        results.forEach(row => {
            const orderTotal = (row.UAE || 0) + (row.Oman || 0) + (row.Qatar || 0) + (row.Kuwait || 0) + (row.Bahrain || 0);
            const deliveryTotal = (row['UAE-delivery'] || 0) + (row['Oman-delivery'] || 0) + (row['Qatar-delivery'] || 0) + (row['Kuwait-delivery'] || 0) + (row['Bahrain-delivery'] || 0);
            const profitTotal = (row['UAE-profit'] || 0) + (row['Oman-profit'] || 0) + (row['Qatar-profit'] || 0) + (row['Kuwait-profit'] || 0) + (row['Bahrain-profit'] || 0);
            const gpTotal = deliveryTotal > 0 ? (profitTotal / deliveryTotal) * 100 : 0;

            worksheet.addRows([
                { category: row.category, metric: 'ORDER', uae: row.UAE, oman: row.Oman, qatar: row.Qatar, kuwait: row.Kuwait, bahrain: row.Bahrain, total: orderTotal },
                { metric: 'DELIVERY', uae: row['UAE-delivery'], oman: row['Oman-delivery'], qatar: row['Qatar-delivery'], kuwait: row['Kuwait-delivery'], bahrain: row['Bahrain-delivery'], total: deliveryTotal },
                { metric: 'PROFIT', uae: row['UAE-profit'], oman: row['Oman-profit'], qatar: row['Qatar-profit'], kuwait: row['Kuwait-profit'], bahrain: row['Bahrain-profit'], total: profitTotal },
                {
                    metric: 'GP %',
                    uae: `${(row['UAE-gp'] || 0).toFixed(2)} %`,
                    oman: `${(row['Oman-gp'] || 0).toFixed(2)} %`,
                    qatar: `${(row['Qatar-gp'] || 0).toFixed(2)} %`,
                    kuwait: `${(row['Kuwait-gp'] || 0).toFixed(2)} %`,
                    bahrain: `${(row['Bahrain-gp'] || 0).toFixed(2)} %`,
                    total: `${gpTotal.toFixed(2)} %`
                },
                {} // Spacer row
            ]);
        });

        // Set response headers and send Excel file
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="ConsolidateCategoryReport-${new Date().toISOString().slice(0, 10)}.xlsx"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        console.error("Excel Export Error:", error);
        res.status(500).send("Could not generate Excel file.");
    }
};
