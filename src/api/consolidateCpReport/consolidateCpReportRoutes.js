import express from 'express';
import { verifyToken } from '../../middleware/authMiddleware.js';

import {
    renderConsolidateCpPage,
    handleConsolidateCpApi,
    handleConsolidateCpExcel
} from '../consolidateCpReport/consolidateCpReportController.js';

const router = express.Router(); // Initialize router

// Render CP report page
router.get('/consolidate-cp-report', verifyToken, (req, res) => {
    res.render('consolidate-cp-report', {
        pageTitle: "CONSOLIDATE CATEGORY PERFORMANCE REPORT",
        user: req.user
    });
});

// Consolidate Category Performance report routes
router.get('/reports/consolidate-cp', verifyToken, renderConsolidateCpPage);          // Page view
router.get('/api/reports/consolidate-cp', verifyToken, handleConsolidateCpApi);       // JSON API
router.get('/api/reports/consolidate-cp/export', verifyToken, handleConsolidateCpExcel); // Excel export

export default router;
