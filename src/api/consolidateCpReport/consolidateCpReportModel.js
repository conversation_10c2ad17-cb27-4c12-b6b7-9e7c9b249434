import db from "../../config/db.js";

class ConsolidateCpReportOld {
    static async getReport(filters) {
        const { fromDate, toDate, buyer } = filters;
        const aggregatedData = {};

        // Get active countries and categories
        const [countries] = await db.query("SELECT * FROM admin_country WHERE status = 1 ORDER BY name ASC");
        const [categories] = await db.query("SELECT categoryid, category_name AS name FROM catalog_category WHERE rstatus=1 AND category_level = 1 ORDER BY categoryid ASC");

        // Initialize result structure
        for (const category of categories) {
            aggregatedData[category.name] = {};
            for (const country of countries) {
                aggregatedData[category.name][country.name] = 0;
                aggregatedData[category.name][`${country.name}-delivery`] = 0;
                aggregatedData[category.name][`${country.name}-profit`] = 0;
                aggregatedData[category.name][`${country.name}-gp`] = 0;
            }
        }

        
        
        // Loop through each country
        for (const country of countries) {
            const countryId = country.country_id;

            const orderParams = [countryId];
            const deliveryParams = [countryId];
            let orderDateCond = '', deliveryDateCond = '', buyerCond = '';

            if (fromDate && !toDate) {
                orderDateCond = ` AND DATE(oo.order_date) = DATE(?)`;
                deliveryDateCond = `AND first_status.first_status_date = (?)`;
                orderParams.push(fromDate);
                deliveryParams.push(fromDate);
            } else if (fromDate && toDate) {
                orderDateCond = `AND DATE(oo.order_date) BETWEEN DATE(?) AND DATE(?)`;
                deliveryDateCond = ` AND first_status.first_status_date BETWEEN DATE(?) AND DATE(?)`;
                orderParams.push(fromDate, toDate);
                deliveryParams.push(fromDate, toDate);
            }

            if (buyer) {
                buyerCond = `AND cp.buyer_id = ?`;
                orderParams.push(buyer);
                deliveryParams.push(buyer);
            }
            
           const orderQuery = `
                SELECT 
                        c3.category_name AS category, 
                        SUM(ood.selling_price * ood.quantity) AS total
                    FROM oms_orders AS oo
                    LEFT JOIN oms_order_detail AS ood 
                        ON oo.orderid = ood.orderid
                    LEFT JOIN catalog_product AS cp 
                        ON ood.productid = cp.productid
                    LEFT JOIN catalog_category AS c1 
                        ON cp.categoryid = c1.categoryid 
                    AND c1.category_level = 3
                    LEFT JOIN catalog_category AS c2 
                        ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
                    AND c2.category_level = 2
                    LEFT JOIN catalog_category AS c3 
                        ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
                    AND c3.category_level = 1
                    LEFT JOIN aauth_users AS au 
                        ON cp.buyer_id = au.id
                    LEFT JOIN admin_country as ac 
                        ON oo.country_id = ac.country_id
                    WHERE  oo.country_id = ?  ${orderDateCond}
                            ${buyerCond}
                     AND oo.order_statusid > 0
                     AND oo.type = 'order'
                     AND oo.shippingaddress NOT LIKE "%test%"

                    AND (
                            JSON_VALID(oo.customer_details) = 0
                            OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
                        )
                    GROUP BY c3.category_name
                    ORDER BY oo.country_id ASC;
                    `;
            


           
            const [orderResults] = await db.query(orderQuery, orderParams);

            for (const row of orderResults) {
               // console.log(row.category);
                if (aggregatedData[row.category]) {
                    aggregatedData[row.category][country.name] = (parseFloat(row.total) || 0);
                }
            }

            

      // --- Query 2: Get delivered, cost, profit (LOGIC MERGED) ---
      const deliveryQuery = `
                    SELECT 
                        c3.category_name AS category, 
                        SUM(ood.selling_price * ood.quantity) AS total,
                        SUM(ood.cost * ood.quantity) AS totalCost,
                        first_status_date
                    FROM oms_orders AS oo
                    LEFT JOIN oms_order_detail AS ood 
                        ON oo.orderid = ood.orderid
                    LEFT JOIN (
                        SELECT 
                            ooh.orderid,
                            MIN(DATE(ooh.statusdate)) AS first_status_date
                        FROM oms_order_history ooh
                        WHERE ooh.order_statusid = 6
                        AND ooh.order_status_type = 'order_status'
                        GROUP BY ooh.orderid
                    ) AS first_status
                        ON oo.orderid = first_status.orderid
                    LEFT JOIN catalog_product AS cp 
                        ON ood.product_sku = cp.sku
                    LEFT JOIN catalog_category AS c1 
                        ON cp.categoryid = c1.categoryid 
                    AND c1.category_level = 3
                    LEFT JOIN catalog_category AS c2 
                        ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
                    AND c2.category_level = 2
                    LEFT JOIN catalog_category AS c3 
                        ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
                    AND c3.category_level = 1
                    LEFT JOIN aauth_users AS au 
                        ON cp.buyer_id = au.id
                    LEFT JOIN admin_country as ac 
                        ON oo.country_id = ac.country_id
                    WHERE  oo.country_id = ? ${deliveryDateCond}
                            ${buyerCond}
                    AND oo.type = 'order'
                    AND oo.shippingaddress NOT LIKE "%test%"
                    AND (
                            JSON_VALID(oo.customer_details) = 0
                            OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%'
                        )
                    GROUP BY c3.category_name
                    ORDER BY oo.country_id ASC;
                    `;

                     
                    // Log raw delivery query results
                    //console.log("Delivery Query:", deliveryQuery);
                    //console.log("Delivery Params:", deliveryParams);
                    const [deliveryResults] = await db.query(deliveryQuery, deliveryParams);
                    //console.log("Delivery Results:", JSON.stringify(deliveryResults, null, 2));



                    //console.log(deliveryResults);

            for (const row of deliveryResults) {
                if (aggregatedData[row.category]) {
                    const totalPrice = parseFloat(row.total || 0);
                    const totalCost = parseFloat(row.totalCost || 0);

                    let inclusiveTax = 0;
                    if (countryId == 3 || countryId == 5) inclusiveTax = 0;
                    else if (countryId == 6) inclusiveTax = totalPrice * 10 / 110;
                    else inclusiveTax = totalPrice * 5 / 105;

                    const netExcluded = totalPrice - inclusiveTax;
                    const profit = netExcluded - totalCost;

                    aggregatedData[row.category][`${country.name}-delivery`] = totalPrice;
                    aggregatedData[row.category][`${country.name}-profit`] = profit;
                    aggregatedData[row.category][`${country.name}-gp`] = netExcluded > 0 ? (profit / netExcluded) * 100 : 0;
                }
            }
        }


        // Return the formatted report
        return Object.entries(aggregatedData).map(([category, data]) => ({
            category,
            ...data
        }));
    }
}

class ConsolidateCpReport {
    static async getReport(filters) {
        const { fromDate, toDate, buyer } = filters;
        const aggregatedData = {};

        // --- Build conditions to match getProfitReport logic exactly ---
        const orderDateCond = fromDate ? (toDate ? `AND DATE(oo.order_date) BETWEEN DATE(?) AND DATE(?)` : `AND DATE(oo.order_date) = DATE(?)`) : '';
        const deliveryDateCond = fromDate ? (toDate ? `AND DATE(ooh.statusdate) BETWEEN ? AND ?` : `AND DATE(ooh.statusdate) = ?`) : '';
        const buyerCond = buyer ? `AND cp.buyer_id = ?` : '';

        const orderParams = [];
        const deliveryParams = [];

        if (fromDate) {
            if (toDate) {
                orderParams.push(fromDate, toDate);
                deliveryParams.push(fromDate, toDate);
            } else {
                orderParams.push(fromDate);
                deliveryParams.push(fromDate);
            }
        }
        if (buyer) {
            orderParams.push(buyer);
            deliveryParams.push(buyer);
        }

    // --- Fetch raw order records with category & country names (matching getProfitReport logic) ---
    const orderQuery = `
            WITH product_categories AS (
            SELECT
                cp.productid,
                cp.categoryid,
                COALESCE(c3.category_name, c2.category_name, c1.category_name) AS category_name
            FROM catalog_product cp
            LEFT JOIN catalog_category c1
                ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category c2
                ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
                AND c2.category_level = 2
            LEFT JOIN catalog_category c3
                ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
                AND c3.category_level = 1
            )
            SELECT
                COALESCE(pc.category_name, 'Uncategorized') AS category,
                ac.name AS country_name,
                oo.country_id,
                SUM(oo.sub_total) AS order_subtotal
            FROM oms_orders oo
            JOIN oms_order_detail ood ON oo.orderid = ood.orderid
            JOIN catalog_product cp ON ood.productid = cp.productid
            LEFT JOIN product_categories pc ON cp.productid = pc.productid
            LEFT JOIN aauth_users au ON cp.buyer_id = au.id
            JOIN admin_country ac ON oo.country_id = ac.country_id
            WHERE oo.order_statusid > 0
            AND oo.shippingaddress NOT LIKE "%test%"
            AND (JSON_VALID(oo.customer_details) = 0
                   OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%')
              ${orderDateCond} ${buyerCond}
            GROUP BY COALESCE(pc.category_name, 'Uncategorized'), ac.name, oo.country_id;
        `;
    const [orderResults] = await db.query(orderQuery, orderParams);

    // --- Fetch raw delivery records with category & country names ---
    const deliveryQuery = `
    WITH product_categories AS (
            SELECT
                cp.productid,
                cp.categoryid,
                COALESCE(c3.category_name, c2.category_name, c1.category_name) AS category_name
            FROM catalog_product cp
            LEFT JOIN catalog_category c1
                ON cp.categoryid = c1.categoryid AND c1.category_level = 3
            LEFT JOIN catalog_category c2
                ON (c1.categorypid = c2.categoryid OR cp.categoryid = c2.categoryid)
                AND c2.category_level = 2
            LEFT JOIN catalog_category c3
                ON (c2.categorypid = c3.categoryid OR cp.categoryid = c3.categoryid)
                AND c3.category_level = 1
            )

            SELECT DISTINCT
                pc.category_name AS category,
                ac.name AS country_name,
                oo.country_id,
                oo.total_amount as totalAmount,
                oo.tax_amount,
                ood.selling_price,
                oo.sub_total,
                ood.orderdetail_id,
                ood.quantity,
                ood.cost,
                cp.buyer_id,
                ooh.statusdate
            FROM oms_order_history ooh
            JOIN oms_orders oo ON ooh.orderid = oo.orderid
            JOIN oms_order_detail ood ON oo.orderid = ood.orderid
            JOIN catalog_product cp ON ood.productid = cp.productid
            LEFT JOIN product_categories pc ON cp.productid = pc.productid
            LEFT JOIN aauth_users au ON cp.buyer_id = au.id
            JOIN admin_country ac ON oo.country_id = ac.country_id
              JOIN (
        SELECT orderid, MIN(statusdate) AS min_delivery
        FROM oms_order_history
        WHERE order_statusid = 6 and order_status_type = 'order_status'
        GROUP BY orderid
      ) first_delivery ON ooh.orderid = first_delivery.orderid AND ooh.statusdate = first_delivery.min_delivery
      WHERE ooh.order_statusid = 6
        AND ooh.order_status_type = 'order_status'
        AND ac.status = 1
        AND oo.shippingaddress NOT LIKE "%test%"
        AND oo.type = "order"
              AND (JSON_VALID(oo.customer_details) = 0
                   OR LOWER(TRIM(JSON_UNQUOTE(JSON_EXTRACT(oo.customer_details, '$.name')))) NOT LIKE '%test%')
              ${deliveryDateCond} ${buyerCond}
              group by oo.orderid;
        `;
        const [deliveryResults] = await db.query(deliveryQuery, deliveryParams);

        // --- Initialize aggregated structure dynamically from fetched data ---
        const allCategories = [...new Set([...orderResults.map(r => r.category), ...deliveryResults.map(r => r.category)])];
        const allCountries = [...new Set([...orderResults.map(r => r.country_name), ...deliveryResults.map(r => r.country_name)])];

        for (const category of allCategories) {
            aggregatedData[category] = {};
            for (const country of allCountries) {
                aggregatedData[category][country] = 0;               // ORDER
                aggregatedData[category][`${country}-delivery`] = 0; // DELIVERY
                aggregatedData[category][`${country}-profit`] = 0;   // PROFIT
                aggregatedData[category][`${country}-gp`] = 0;       // GP %
            }
        }

        // --- Aggregate order totals (using sub_total to match getProfitReport) ---
        for (const row of orderResults) {
            if (row.category && aggregatedData[row.category]) {
                aggregatedData[row.category][row.country_name] += parseFloat(row.order_subtotal || 0);
            }
        }

        // --- Aggregate delivery, profit, GP% (without currency conversion to match getProfitReport) ---
        for (const row of deliveryResults) {
            const totalPrice = parseFloat(row.sub_total);
            const totalCost = parseFloat(row.cost) * parseFloat(row.quantity);

            let inclusiveTax = parseFloat(row.tax_amount || 0);

            const netExcluded = totalPrice - inclusiveTax;
            const profit = netExcluded - totalCost;

            aggregatedData[row.category][`${row.country_name}-delivery`] += totalPrice;
            aggregatedData[row.category][`${row.country_name}-profit`] += profit;
            aggregatedData[row.category][`${row.country_name}-gp`] = netExcluded > 0
                ? (aggregatedData[row.category][`${row.country_name}-profit`] / totalCost) * 100
                : 0;
        }

        // --- Return formatted report ---
        return Object.entries(aggregatedData).map(([category, data]) => ({
            category,
            ...data
        }));
    }
}


export default ConsolidateCpReport;
