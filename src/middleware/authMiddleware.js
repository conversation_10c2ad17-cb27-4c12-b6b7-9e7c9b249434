import jwt from 'jsonwebtoken'; // For token verification

// Protects routes - allows access only if token is valid
export const verifyToken = (req, res, next) => {
    const token = req.cookies.token;
    if (!token) return res.redirect('/login');

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded; // Attach user info to request
        next();
    } catch (err) {
        return res.redirect('/login');
    }
};

// Prevents logged-in users from accessing login page
export const redirectIfLoggedIn = (req, res, next) => {
    const token = req.cookies.token;

    if (token) {
        jwt.verify(token, process.env.JWT_SECRET, (err) => {
            if (err) {
                next(); // Invalid token, proceed to login
            } else {
                res.redirect('/dashboard'); // Already logged in
            }
        });
    } else {
        next(); // No token, proceed to login
    }
};
