import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import cookieParser from 'cookie-parser';

// ===============================================
// ROUTE MODULE IMPORTS
// ===============================================
import authRoutes from './api/auth/authRoutes.js';
import orderReportRoutes from './api/orderReport/orderReportRoutes.js';
import stockReportRoutes from './api/stockReport/stockReportRoutes.js';
import currentStockReportRoutes from './api/currentStockReport/currentStockReportRoutes.js';
import orderPendingReportRoutes from './api/orderPendingReport/orderPendingReportRoutes.js';
import consolidateBpReportRoutes from './api/consolidateBpReport/consolidateBpReportRoutes.js';
import consolidateCpReportRoutes from './api/consolidateCpReport/consolidateCpReportRoutes.js';
import procurementDelayReportRoutes from './api/procurementDelayReport/procurementDelayReportRoutes.js';
import procurementReportRoutes from './api/procurementReport/procurementReportRoutes.js';
import stockLedgerReportRoutes from './api/stockLedgerReport/stockLedgerReportRoutes.js';
import stockTransferPfReportRoutes from './api/stockTransferPfReport/stockTransferPfReportRoutes.js';
import stockTransferReportRoutes from './api/stockTransferReport/stockTransferReportRoutes.js';
import topSellingReportRoutes from './api/topSellingReport/topSellingReportRoutes.js';
import consolidateOsReportRoutes from './api/consolidateOsReport/consolidateOsReportRoutes.js';
import lookupRoutes from './api/lookupRoutes.js';
import cancelReportRoutes from './api/cancelReport/cancelReportRoutes.js';
import dailyOrderReportRoutes from './api/dailyOrderReport/dailyOrderReportRoutes.js';
import senderReportRoutes from './api/senderReport/senderReportRoutes.js';
import ageingReportRoutes from './api/ageingReport/ageingReportRoutes.js';
import profitReportRoutes from './api/profitReport/profitReportRoutes.js';
import donationReportRoutes from './api/donationReport/donationReportRoutes.js';
import supplierWiseReportRoutes from './api/supplierWiseReport/supplierWiseReportRoutes.js';
import cancelProductReportRoutes from './api/cancelProductReport/cancelProductReportRoutes.js';
import purchaseReportRoutes from './api/purchaseReport/purchaseReportRoutes.js';
import agentReportRoutes from './api/agentReport/agentReportRoutes.js';
import driverReportRoutes from './api/driverReport/driverReportRoutes.js';
import webFeedReportRoutes from './api/webFeedReport/webFeedReportRoutes.js';
import quickReportRoutes from './api/quickReport/quickReportRoutes.js';
import productTransferReportRoutes from './api/productTransferReport/productTransferReportRoutes.js';
import monthlyOrderReportRoutes from './api/monthlyOrderReport/monthlyOrderReportRoutes.js'; 
import logisticDeliveryReportRoutes from './api/logisticDeliveryReport/logisticDeliveryReportRoutes.js'; 
import deliveryCohortReportRoutes from './api/deliveryCohortReport/deliveryCohortReportRoutes.js';
import salesRegisterRoutes from './api/salesRegister/salesRegisterRoutes.js';
import cogsRegisterRoutes from './api/cogsRegister/cogsRegisterRoutes.js';
import skuReportRoutes from './api/skuReport/skuReportRoutes.js';
import dailyOrderStatusRoutes from './api/dailyOrderStatus/dailyOrderStatusRoutes.js';
import productpurchaseReport from './api/productPurchaseReport/purchaseReportRoutes.js';
import depositReportRoutes from './api/deposit/depositReportRoutes.js';


// ===============================================
// APP INITIALIZATION
// ===============================================
const __filename = fileURLToPath(import.meta.url);         // Get full path to current file
const __dirname = path.dirname(__filename);                // Get directory name
const app = express();                                     // Initialize Express application

// ===============================================
// MIDDLEWARE CONFIGURATION
// ===============================================
app.set('view engine', 'ejs');                             // Set EJS as templating engine
app.set('views', path.join(__dirname, '../views'));        // Define views directory
app.use(express.static(path.join(__dirname, '../public'))); // Serve static files
app.use(express.urlencoded({ extended: true }));           // Parse form data
app.use(express.json());                                   // Parse JSON payloads
app.use(cookieParser());                                   // Enable cookie handling

// ✅ Global middleware to expose values to all EJS views
app.use((req, res, next) => {
    res.locals.currentPath = req.path;                     // Pass current route path to views
    res.locals.baseUrl = process.env.BASE_URL || '';       // Pass base URL to views
    next();
});

// ===============================================
// ROUTE REGISTRATION
// ===============================================
app.use(lookupRoutes);                     // Lookup values (dropdowns, filters, etc.)
app.use(authRoutes);                       // Authentication routes (login/logout)
app.use(orderReportRoutes);                // Order reports
app.use(stockReportRoutes);                // Stock reports
app.use(currentStockReportRoutes);         // Current stock snapshot
app.use(orderPendingReportRoutes);         // Pending order report
app.use(consolidateBpReportRoutes);        // Consolidated BP report
app.use(consolidateCpReportRoutes);        // Consolidated CP report
app.use(procurementDelayReportRoutes);     // Procurement delays
app.use(procurementReportRoutes);          // Procurement reporting
app.use(stockLedgerReportRoutes);          // Stock ledger
app.use(stockTransferPfReportRoutes);      // Stock transfer profit/loss
app.use(stockTransferReportRoutes);      // Stock transfer profit/loss
app.use(topSellingReportRoutes);           // Top-selling pending products
app.use(cancelReportRoutes)
app.use(dailyOrderReportRoutes);
app.use(senderReportRoutes);
app.use(ageingReportRoutes);
app.use(profitReportRoutes);
app.use(consolidateOsReportRoutes);
app.use(donationReportRoutes);
app.use(supplierWiseReportRoutes);
app.use(cancelProductReportRoutes);
app.use(purchaseReportRoutes);
app.use(agentReportRoutes);
app.use(driverReportRoutes);
app.use(webFeedReportRoutes);
app.use(quickReportRoutes); 
app.use(productTransferReportRoutes);
app.use(monthlyOrderReportRoutes);
app.use(logisticDeliveryReportRoutes);
app.use(deliveryCohortReportRoutes);
app.use(salesRegisterRoutes);
app.use(cogsRegisterRoutes);
app.use(skuReportRoutes);
app.use(productpurchaseReport);
app.use(dailyOrderStatusRoutes);
app.use(productpurchaseReport);
app.use(dailyOrderStatusRoutes);
app.use(productpurchaseReport);

app.use(depositReportRoutes);

// ✅ Redundant but safe fallback for public directory
app.use(express.static('public'));

// ===============================================
// DEFAULT ROUTE
// ===============================================
app.get('/', (req, res) => {
    res.redirect('/login');                // Redirect base path to login page
});

// ===============================================
// EXPORT APP INSTANCE
// ===============================================
export default app;
