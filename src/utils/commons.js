export const getCategoryLineage = async (db, categoryid) => {
    const sql = `
        SELECT
            c1.categoryid AS lvl1_id,
            c1.categorypid AS lvl1_pid,
            c1.category_level AS lvl1_level,
            c1.category_name AS lvl1_name,
            c2.categoryid AS lvl2_id,
            c2.categorypid AS lvl2_pid,
            c2.category_level AS lvl2_level,
            c2.category_name AS lvl2_name,
            c3.categoryid AS lvl3_id,
            c3.categorypid AS lvl3_pid,
            c3.category_level AS lvl3_level,
            c3.category_name AS lvl3_name
        FROM catalog_category c1
        LEFT JOIN catalog_category c2 ON c1.categorypid = c2.categoryid
        LEFT JOIN catalog_category c3 ON c2.categorypid = c3.categoryid
        WHERE c1.categoryid = ?
    `;
    const [rows] = await db.query(sql, [categoryid]);
    if (rows.length === 0) return null;
    const row = rows[0];

    // Determine actual level and map accordingly
    if (row.lvl1_level === 1) {
        // Level 1: Only category
        return {
            category_id: row.lvl1_id,
            category_name: row.lvl1_name,
            subcategory_id: null,
            subcategory_name: null,
            sub_subcategory_id: null,
            sub_subcategory_name: null,
        };
    } else if (row.lvl1_level === 2) {
        // Level 2: Subcategory
        return {
            category_id: row.lvl2_id,
            category_name: row.lvl2_name,
            subcategory_id: row.lvl1_id,
            subcategory_name: row.lvl1_name,
            sub_subcategory_id: null,
            sub_subcategory_name: null,
        };
    } else if (row.lvl1_level === 3) {
        // Level 3: Sub-subcategory
        return {
            category_id: row.lvl3_id,
            category_name: row.lvl3_name,
            subcategory_id: row.lvl2_id,
            subcategory_name: row.lvl2_name,
            sub_subcategory_id: row.lvl1_id,
            sub_subcategory_name: row.lvl1_name,
        };
    }
    // Default:
    return null;
}


// Recursively collects all child category IDs for a given parent ID
export const getAllDescendantCategoryIds = async (db, parentId) => {
    const sql = `
        WITH RECURSIVE category_tree AS (
            SELECT categoryid FROM catalog_category WHERE categoryid = ?
            UNION ALL
            SELECT c.categoryid
            FROM catalog_category c
            INNER JOIN category_tree ct ON c.categorypid = ct.categoryid
        )
        SELECT categoryid FROM category_tree;
    `;
    const [rows] = await db.query(sql, [parentId]);
    return rows.map(row => row.categoryid);
};

export const orderFromId = {
    1: 'WEBSITE',
    2: 'ANDROID',
    3: 'IOS',
    4: 'WHATSAPP',
    5: 'FACEBOOK',
    6: 'WEBFEED',
    7: 'TELEPHONE',
    8: 'FBFEEDLIST',
    9: 'Webfeed_OS',
    10: 'APP',
    11: 'OTHER'
};

export const orderStatus = {
    1: 'Pending',
    2: 'Processed',
    3: 'Ready to Dispatch',
    5: 'Out For Delivery',
    6: 'Delivered',
    7: 'Cancelled b/f Dispatch',
    11: 'Cancelled a/f Dispatch'
}

const CancelStatusEnums = {
    CANCEL_BEFORE_DISPATCH: 'cal_before_dis',
    CANCEL_AFTER_DISPATCH: 'cal_after_dis',
}

export const getCancelStatusEnum = (cancel_status) => {
    switch (cancel_status) {
        case CancelStatusEnums.CANCEL_BEFORE_DISPATCH:
            return "7";
        case CancelStatusEnums.CANCEL_AFTER_DISPATCH:
            return "11";
        default:
            return "7, 11";
    }
}

export const paymentMethodIdEnum = {
    "1": "CASH",
    "2": "CREDIT",
    "4": "CRM",
}

export const setCancelReportPaymentType = (num) => {
    return paymentMethodIdEnum[String(num)] ? paymentMethodIdEnum[String(num)]: '';
}
