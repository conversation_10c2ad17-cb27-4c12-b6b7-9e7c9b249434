/**
 * A standardized class for creating successful API responses.
 */
class ApiResponse {
    /**
     * @param {number} statusCode - The HTTP status code for the response.
     * @param {any} data - The payload/data to be included in the response.
     * @param {string} [message="Success"] - A descriptive message for the response.
     */
    constructor(statusCode, data, message = "Success") {
        this.statusCode = statusCode;
        this.data = data;
        this.message = message;
        // Success is determined by the status code (codes below 400 are generally success).
        this.success = statusCode < 400;
    }
}

export { ApiResponse };