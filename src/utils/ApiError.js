/**
 * Custom Error class for handling API-specific errors.
 * Extends the built-in Error class to include a status code and other structured details.
 */
class ApiError extends Error {
    /**
     * @param {number} statusCode - The HTTP status code for the error.
     * @param {string} [message="Something went wrong"] - A descriptive error message.
     * @param {Array} [errors=[]] - An array of validation or other specific errors.
     * @param {string} [stack=""] - The error stack trace.
     */
    constructor(
        statusCode,
        message = "Something went wrong",
        errors = [],
        stack = ""
    ) { 
        super(message);
        this.statusCode = statusCode;
        this.data = null; // 'data' is always null for an error response.
        this.message = message;
        this.success = false; // 'success' is always false for an error.
        this.errors = errors;

        // Capture stack trace if not provided.
        if (stack) {
            this.stack = stack;
        } else {
            Error.captureStackTrace(this, this.constructor);
        }
    }

    /**
     * Overrides the default toJSON method to ensure a consistent error response format.
     * @returns {object} A JSON-representable object for the error.
     */
    toJSON() {
        return {
            success: this.success,
            message: this.message,
            errors: this.errors,
            data: this.data
        };
    }
}

export { ApiError };