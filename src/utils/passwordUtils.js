import { scrypt } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

/**
 * Hashes a password using scrypt and a given userId as salt.
 * @param {string} pass - The plain text password to hash.
 * @param {string} userId - The user ID used as salt.
 * @returns {Promise<string>} - The hashed password as a hex string.
 */
export const hashPassword = async (pass, userId) => {
    const salt = userId; // Using userId as the salt
    const derivedKey = await scryptAsync(pass, salt, 64); // 64-byte key
    return derivedKey.toString('hex');
};
