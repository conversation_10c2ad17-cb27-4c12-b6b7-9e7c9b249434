import dotenv from 'dotenv';
dotenv.config(); // Load environment variables from .env file

import app from './app.js'; // Import configured Express app

const PORT = process.env.PORT || 3000; // Define the server port (default to 3000)

// Set global variable accessible in views
app.locals.baseUrl = process.env.BASE_URL;

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
