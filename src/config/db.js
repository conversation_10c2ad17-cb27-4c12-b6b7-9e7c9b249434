import mysql from 'mysql2/promise'; // Import MySQL with promise support
import dotenv from 'dotenv';        // Import dotenv for env vars

dotenv.config(); // Load .env file

// Create MySQL connection pool
const pool = mysql.createPool({
    host: process.env.DB_HOST,       // DB host
    user: process.env.DB_USER,       // DB user
    password: process.env.DB_PASSWORD, // DB password
    database: process.env.DB_NAME,   // DB name
    waitForConnections: true,        // Wait if no connection available
    connectionLimit: 10,             // Max connections
    queueLimit: 0                    // Unlimited queue
});

export default pool; // Export pool
