import ProfitReport from './src/api/profitReport/profitReportModel.js';
import ConsolidateCpReport from './src/api/consolidateCpReport/consolidateCpReportModel.js';

async function testReportMatching() {
    try {
        console.log('Testing report matching between getProfitReport and ConsolidateCpReport...\n');
        
        // Test with sample filters
        const testFilters = {
            fromDate: '2024-01-01',
            toDate: '2024-01-31',
            country: 1, // UAE
            based_on: 'delivery_date' // Use delivery_date since created_date has SQL syntax error
        };
        
        console.log('Test filters:', testFilters);
        console.log('='.repeat(50));
        
        // Get data from both reports
        console.log('Fetching getProfitReport data...');
        const profitReportData = await ProfitReport.getProfitReport(testFilters);
        
        console.log('Fetching ConsolidateCpReport data...');
        const consolidateReportData = await ConsolidateCpReport.getReport({
            fromDate: testFilters.fromDate,
            toDate: testFilters.toDate
        });
        
        console.log('\n' + '='.repeat(50));
        console.log('RESULTS COMPARISON:');
        console.log('='.repeat(50));
        
        // Display getProfitReport orderSummary
        console.log('\ngetProfitReport orderSummary:');
        console.log('- Total Orders:', profitReportData.orderSummary.totalorder);
        console.log('- Subtotal:', profitReportData.orderSummary.subtotal);
        console.log('- Total Amount:', profitReportData.orderSummary.total);
        
        // Calculate totals from ConsolidateCpReport by country
        console.log('\nConsolidateCpReport totals by country:');
        let totalOrdersByCountry = {};

        consolidateReportData.forEach(categoryRow => {
            Object.keys(categoryRow).forEach(key => {
                if (key !== 'category' && !key.includes('-delivery') && !key.includes('-profit') && !key.includes('-gp')) {
                    // This is a country order total
                    if (!totalOrdersByCountry[key]) {
                        totalOrdersByCountry[key] = 0;
                    }
                    totalOrdersByCountry[key] += parseFloat(categoryRow[key] || 0);
                }
            });
        });

        console.log('Order totals by country:', totalOrdersByCountry);

        // For fair comparison, get only UAE total from ConsolidateCpReport (since getProfitReport filters by country=1 which is UAE)
        const uaeTotal = totalOrdersByCountry['UAE'] || 0;
        console.log('ConsolidateCpReport UAE Total:', uaeTotal);

        // Calculate grand total from ConsolidateCpReport (for reference)
        const consolidateGrandTotal = Object.values(totalOrdersByCountry).reduce((sum, val) => sum + val, 0);
        console.log('ConsolidateCpReport Grand Total (all countries):', consolidateGrandTotal);
        
        // Compare the totals (UAE only for fair comparison)
        console.log('\n' + '='.repeat(50));
        console.log('COMPARISON (UAE only):');
        console.log('='.repeat(50));
        console.log('getProfitReport subtotal (UAE):', profitReportData.orderSummary.subtotal);
        console.log('ConsolidateCpReport UAE total:', uaeTotal);
        console.log('Difference:', Math.abs(profitReportData.orderSummary.subtotal - uaeTotal));

        const tolerance = 0.01; // Allow small floating point differences
        const isMatching = Math.abs(profitReportData.orderSummary.subtotal - uaeTotal) < tolerance;
        
        console.log('Match Status:', isMatching ? '✅ MATCHING' : '❌ NOT MATCHING');
        
        if (!isMatching) {
            console.log('\n⚠️  Reports do not match. Possible reasons:');
            console.log('1. Different filtering conditions');
            console.log('2. Different date handling');
            console.log('3. Different order status filtering');
            console.log('4. Different test data exclusion');
        }
        
        return isMatching;
        
    } catch (error) {
        console.error('Error testing report matching:', error);
        return false;
    }
}

// Run the test
testReportMatching().then(result => {
    console.log('\nTest completed. Result:', result ? 'PASS' : 'FAIL');
    process.exit(result ? 0 : 1);
}).catch(error => {
    console.error('Test failed with error:', error);
    process.exit(1);
});
