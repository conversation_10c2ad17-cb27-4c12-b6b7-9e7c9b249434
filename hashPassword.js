import bcrypt from 'bcrypt';

const password = 'khush<PERSON>@123'; // The password you want to hash
const saltRounds = 10; // A good standard value

bcrypt.hash(password, saltRounds, function(err, hash) {
    if (err) {
        console.error("Error hashing password:", err);
        return;
    }
    // Now, copy this hash and put it in the 'password' column
    // for your 'admin' user in the shopee_admins table.
});