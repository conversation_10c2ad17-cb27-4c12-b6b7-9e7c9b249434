{"name": "report-mailer", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node -r dotenv/config src/server.js", "worker": "node -r dotenv/config src/worker.js", "dev": "nodemon -r dotenv/config src/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.6.1", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "nodemailer": "^7.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}