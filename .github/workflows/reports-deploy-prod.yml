name: Deploy on Reports prod server 

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to execute workflow on'
        required: true
        default: 'main'  # Set a default branch

jobs:
  deploy-reports-prod:
    runs-on: ubuntu-latest
    steps:

      - name: Check if user is allowed
        run: |
          ALLOWED_USERS=("ankush-ourshopee" "HarishSharma4782" "vishalKOS" "qaseem-ourshopee" "aayushourshopee" "ankitsura" "tek-thapa-ourshopee")  # Add allowed GitHub usernames here
          if [[ ! " ${ALLOWED_USERS[@]} " =~ " ${GITHUB_ACTOR} " ]]; then
            echo "You are not authorized to trigger this workflow."
            exit 1
          fi
 
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}        

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region:            ${{ secrets.AWS_REGION }}

      - name: Upload Reports backend to S3
        run: |
          aws s3 sync . s3://ourshopee-build-backup/reports.ourshopee.com/production/ \
            --exclude ".git/*" \
            --exclude "node_modules/*" \
            --exclude ".github/*" \
            --delete
      - name: Get Production EC2 Instance IDs by Tag
        id: get-instances
        run: |
          INSTANCE_IDS=$(aws ec2 describe-instances \
            --filters "Name=tag:Name,Values=reports server" "Name=instance-state-name,Values=running" \
            --query "Reservations[].Instances[].InstanceId" \
            --output text)
          if [ -z "$INSTANCE_IDS" ]; then
            echo "❌ No running instances found for reports server"
            exit 1
          fi            
          echo "INSTANCE_IDS=$INSTANCE_IDS" >> $GITHUB_ENV
          echo "Target instances: $INSTANCE_IDS"
          
      - name: Trigger SSM deployment
        run: |
          aws ssm send-command \
            --document-name "AWS-RunShellScript" \
            --comment "Deploy Reports server via SSM" \
            --instance-ids $INSTANCE_IDS \
            --region ${{ secrets.AWS_REGION }} \
            --timeout-seconds 900 \
            --parameters '{
              "commands": [
                "APP_DIR=/var/www/reports/",
                "S3_PROD=s3://ourshopee-build-backup/reports.ourshopee.com/production/",
                "S3_BACKUP_BASE=s3://ourshopee-build-backup/reports.ourshopee.com/backup",
                "TIMESTAMP=$(date +%Y%m%d%H%M%S)",
                "S3_BACKUP_PATH=$S3_BACKUP_BASE/$TIMESTAMP",
                "SECRET_NAME=prod/ourshopee/CRM-REPORTS-BE",
                "REGION=ap-south-1",

                "echo Backing up current app to $S3_BACKUP_PATH...",
                "if [ -d $APP_DIR ]; then aws s3 sync $APP_DIR $S3_BACKUP_PATH > /dev/null 2>&1; fi",

                "echo Cleaning old backups...",
                "BACKUPS=$(aws s3 ls $S3_BACKUP_BASE/ | awk '\''{print $2}'\'' | sort)",
                "COUNT=$(echo \"$BACKUPS\" | wc -l)",
                "DEL=$(($COUNT - 3))",
                "if [ $DEL -gt 0 ]; then echo \"$BACKUPS\" | head -n $DEL | while read OLD; do aws s3 rm --recursive $S3_BACKUP_BASE/$OLD > /dev/null 2>&1; done; fi",

                "echo Syncing latest build...",
                "rm -rf $APP_DIR",
                "mkdir -p $APP_DIR",
                "aws s3 sync $S3_PROD $APP_DIR --delete > /dev/null 2>&1",

                "aws secretsmanager get-secret-value --secret-id \"$SECRET_NAME\" --region \"$REGION\" --query SecretString --output text | jq -r '\''to_entries[] | \"\\(.key)=\\(.value)\"'\'' > $APP_DIR/.env",

                "chown -R ubuntu:www-data $APP_DIR",
                "cd $APP_DIR",
                "echo Installing production deps...",
                "su - ubuntu -c \"cd $APP_DIR && npm install -f\"",

                "if ! npm install -f; then",
                "  echo Install failed. Rolling back...",
                "  rm -rf $APP_DIR",
                "  mkdir -p $APP_DIR",
                "  aws s3 sync $S3_BACKUP_PATH $APP_DIR > /dev/null 2>&1",
                "  cd $APP_DIR",
                "  chown -R ubuntu:www-data $APP_DIR",
                "  npm install -f",
                "fi",
                
                "chown -R ubuntu:www-data $APP_DIR",

                "echo Checking if PM2 process is running...",
                "su - ubuntu -c \"cd $APP_DIR && /home/<USER>/.nvm/versions/node/v20.19.3/bin/pm2 restart src/server.js --name crm-reports --update-env\"",
                "sudo systemctl restart nginx",
                "echo ✅ prod CRM reports deployed via SSM"
              ]
            }'
