name: Deploy on reports Ourshopee-Dev server 
on:
  push:
    branches:
      - dev
jobs:
  deploy-reports-prod:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region:            ${{ secrets.AWS_REGION }}

      - name: Upload Reports backend to S3
        run: |
          aws s3 sync . s3://ourshopee-build-backup/reports.ourshopee.com/dev/ \
            --exclude ".git/*" \
            --exclude "node_modules/*" \
            --exclude ".github/*" \
            --delete
      - name: Get Production EC2 Instance IDs by Tag
        id: get-instances
        run: |
          INSTANCE_IDS=$(aws ec2 describe-instances \
            --filters "Name=tag:Name,Values=Ourshopee-Dev" "Name=instance-state-name,Values=running" \
            --query "Reservations[].Instances[].InstanceId" \
            --output text)
          if [ -z "$INSTANCE_IDS" ]; then
            echo "❌ No running  Ourshopee-Dev instances found"
            exit 1
          fi            
          echo "INSTANCE_IDS=$INSTANCE_IDS" >> $GITHUB_ENV
          echo "Target instances: $INSTANCE_IDS"
          
      - name: Trigger SSM deployment
        run: |
          aws ssm send-command \
            --document-name "AWS-RunShellScript" \
            --comment "Deploy Reports server via SSM" \
            --instance-ids $INSTANCE_IDS \
            --region ${{ secrets.AWS_REGION }} \
            --timeout-seconds 900 \
            --parameters '{
              "commands": [
                "APP_DIR=/usr/share/nginx/reports-dev.ourshopee.com/",
                "S3_DEV=s3://ourshopee-build-backup/reports.ourshopee.com/dev/",
                "SECRET_NAME=dev/ourshopee/CRM-REPORTS-BE",

                "REGION=ap-south-1",
                "export PATH=$PATH:/home/<USER>/.nvm/versions/node/v20.19.4/bin",

                "echo Syncing latest build...",
                "rm -rf $APP_DIR",
                "mkdir -p $APP_DIR",
                "aws s3 sync $S3_DEV $APP_DIR --delete > /dev/null 2>&1",

                "aws secretsmanager get-secret-value --secret-id \"$SECRET_NAME\" --region \"$REGION\" --query SecretString --output text | jq -r '\''to_entries[] | \"\\(.key)=\\(.value)\"'\'' > $APP_DIR/.env",
                
                "chown -R ec2-user:ec2-user $APP_DIR",
                "cd $APP_DIR",
                "echo Installing Dev deps...",
                "npm install -f",

                "chown -R ec2-user:ec2-user $APP_DIR",
                "echo Restarting PM2...",
                "su - ec2-user -c '"'"'cd /usr/share/nginx/reports-dev.ourshopee.com/ && pm2 restart reports-dev --update-env || pm2 start src/server.js --name reports-dev --update-env || pm2 list'"'"' ",

                "echo ✅  reports-dev deployed via SSM"
              ]
            }'