<div class="wrapper">

    <!-- ✅ Include Header and Sidebar -->
    <%- include('partials/header', { user: user }) %>
    <%- include('partials/sidebar', { user: user }) %>

    <!-- ✅ Main Content Wrapper -->
    <div class="content-wrapper">

        <!-- ✅ Page Header -->
        <section class="content-header">
            <h5><%= pageTitle %></h5>
            <ol class="breadcrumb">
                <li><a href="/dashboard"><i class="fa fa-dashboard"></i> HOME</a></li>
                <li class="active"><%= pageTitle %></li>
            </ol>
        </section>

        <!-- ✅ Page Content -->
        <section class="content">

            <!-- ✅ Filter Section -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <!-- <h3 class="box-title"><%= pageTitle %></h3> -->
                </div>
                <div class="box-body">
                    <!-- ✅ Report Filter Form -->
                    <form id="report-filters">

                        <!-- ✅ Filter Row 1 -->
                        <div class="row">
                            <!-- Country (dynamic via AJAX) -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Select Country</label>
                                    <select  name="country" id="country" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                            
                            <!-- Order From (multi-select) -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Order From</label>
                                    <select name="orderFrom" id="orderFrom" class="form-control select2-multi" multiple="multiple">
                                        <option value="1">Website</option>
                                        <option value="7">Telephone</option>
                                        <option value="5">Facebook</option>
                                        <option value="10">App</option>
                                        <option value="3">iOS</option>
                                        <option value="2">Android</option>
                                        <option value="4">WhatsApp</option>
                                        <option value="6">WebFeed</option>
                                        <optgroup value="9">Webfeed Os</optgroup>
                                    </select>
                                </div>
                            </div>

                            <!-- From Date -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>From Date</label>
                                    <input required type="text" name="fromDate" class="form-control date-picker">
                                </div>
                            </div>

                             <!-- To Date -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>To Date</label>
                                    <input required type="text" name="toDate" class="form-control date-picker">
                                </div>
                            </div>
                           
                           
                        </div>

                    
                        <!-- ✅ Filter Row 4 -->
                        <div class="row">
                            <!-- Action Buttons -->
                            <div class="col-md-9">
                                <button type="reset" class="btn btn-default">Cancel</button>
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>

            <!-- ✅ Report Output Area -->
            <div id="report-results-container" style="display:none;"></div>

            <!-- ✅ Loading Spinner -->
            <div id="loading-spinner" style="display: none; text-align: center; font-size: 2em; padding: 40px;">
                <i class="fa fa-refresh fa-spin"></i>
            </div>
        </section>
    </div>

    <!-- ✅ Footer Include -->
    <%- include('partials/footer') %>
</div>

<!-- ✅ Order Report Script -->
<script src="<%= baseUrl %>/js/daily-order-status.js"></script>
