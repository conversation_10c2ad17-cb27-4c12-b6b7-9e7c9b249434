<div class="wrapper">
    <!-- ✅ Include Header and Sidebar Partials -->
    <%- include('partials/header', { user: user }) %>
        <%- include('partials/sidebar', { user: user }) %>

            <!-- ✅ Main Content Wrapper -->
            <div class="content-wrapper">

                <!-- ✅ Page Title and Breadcrumb Navigation -->
                <section class="content-header">
                    <h5>
                        <%= pageTitle %>
                    </h5>
                    <ol class="breadcrumb">
                        <li><a href="<%= baseUrl %>/dashboard"><i class="fa fa-dashboard"></i> HOME</a></li>
                        <li class="active">
                            <%= pageTitle %>
                        </li>
                    </ol>
                </section>

                <!-- ✅ Page Content Section -->
                <section class="content">

                    <!-- ✅ Report Filters Box -->
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <!-- <h3 class="box-title"><%= pageTitle %></h3> -->
                        </div>
                        <div class="box-body">
                            <form id="report-filters">
                                
                                <!-- ✅ First Row of Filters -->
                                <div class="row">
                                    <!-- ✅ Country Select -->
                                    <!-- <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Select Country</label>
                                            <select name="countryId" id="country" class="form-control select2"
                                            required></select>
                                        </div>
                                    </div> -->
                                    
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Status</label>
                                            <select name="status" class="form-control select2">
                                                <option value="">Select One</option>
                                                <option value="Done">Done</option>
                                                <option value="Pending">Pending</option>
                                                <option value="Hold">Hold</option>
                                                <option value="Cancelled">Cancelled</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Transfer From</label>
                                            <select name="transferFrom" required id="transferFrom" class="form-control select2"></select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Transfer Through</label>
                                            <select name="transferMedia" class="form-control select2">
                                                <option value="">Select One</option>
                                                <option value=1>Road</option>
                                                <option value=2>Air</option>
                                                <option value=3>Sea</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Transfer To</label>
                                             <select name="transferTo" id="transferTo" class="form-control select2"></select>
                                        </div>
                                    </div>

                                    <!-- ✅ Status Dropdown -->
                                </div>

                                <!-- ✅ Second Row of Filters -->
                                <div class="row">
                                    <!-- ✅ From Date -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Select From Date</label>
                                            <input required type="text" name="fromDate"
                                                class="form-control date-picker">
                                        </div>
                                    </div>

                                    <!-- ✅ To Date -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Select To Date</label>
                                            <input required type="text" name="toDate" class="form-control date-picker">
                                        </div>
                                    </div>

                                    <!-- ✅ Action Buttons -->
                                    <div class="col-md-6 text-right" style="padding-top: 25px;">
                                        <button type="reset" id="reset-button" class="btn btn-default">Cancel</button>
                                        <button type="submit" class="btn btn-primary">Search</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- ✅ Report Output Placeholder -->
                    <div id="report-results-container" style="display:none;"></div>

                    <!-- ✅ Loading Spinner While Fetching Report -->
                    <div id="loading-spinner" style="display: none; text-align: center; font-size: 2em; padding: 40px;">
                        <i class="fa fa-refresh fa-spin"></i>
                    </div>
                </section>
            </div>

            <!-- ✅ Footer Partial -->
            <%- include('partials/footer') %>
</div>

<!-- ✅ Page-Specific JavaScript -->
<script src="/js/stock-transfer-report.js"></script>