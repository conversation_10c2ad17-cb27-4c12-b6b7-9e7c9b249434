<div class="wrapper">

    <!-- Header and Sidebar -->
    <%- include('partials/header', { user: user }) %>
    <%- include('partials/sidebar', { user: user }) %>

    <div class="content-wrapper">
        <!-- Page Header -->
        <section class="content-header">
            <h1><%= pageTitle %></h1>
            <ol class="breadcrumb">
                <li><a href="/dashboard"><i class="fa fa-dashboard"></i> HOME</a></li>
                <li class="active"><%= pageTitle %></li>
            </ol>
        </section>

        <section class="content">
            <!-- Filter Form -->
            <div class="box box-primary">
                <div class="box-header with-border"></div>
                <div class="box-body">
                    <form id="report-filters">
                        <!-- Filter Row 1 -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Select Country</label>
                                    <select required name="country" id="country" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Delivery Agent</label>
                                    <select name="sender" id="sender" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Select Paymode</label>
                                    <select name="paymode" class="form-control select2">
                                        <option value="">Select One</option>
                                        <option value="2">Cash on Delivery</option>
                                        <option value="1">Credit Card</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Payment Gateway Type</label>
                                    <select name="paymentGatewayType" id="paymentGatewayType" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Row 2 -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Select Status</label>
                                    <select name="status" class="form-control select2"> 
                                        <option value="5">Pending</option>
                                        <option value="11">Return</option>
                                        <option value="6">Delivered</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Order From</label>
                                    <select name="orderFrom" id="orderFrom" class="form-control select2-multi" multiple="multiple">
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Order Type</label>
                                    <select name="order_type" class="form-control select2">
                                        <option value="0">Regular</option>
								        <option value="1">Splitted</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>From Date</label>
                                    <input required type="text" name="fromDate" class="form-control date-picker">
                                </div>
                            </div>
                        </div>

                        <!-- Filter Row 3 -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>To Date</label>
                                    <input required type="text" name="toDate" class="form-control date-picker">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Customer Delivery Date</label>
                                    <input required type="text" name="cDate" class="form-control date-picker">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Select Product</label>
                                    <select name="productId" id="product" class="form-control select2-ajax" disabled></select>
                                </div>
                            </div>

                        </div>

                        <!-- Filter Row 4 -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Emirate</label>
                                    <select name="emirate" id="emirate" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Area</label>
                                    <select name="area" id="area" class="form-control select2-ajax"></select>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Row 5 -->
                        <div class="row">
                            <div class="col-md-3" style="padding-top: 25px;">
                                <button type="reset" class="btn btn-default">Cancel</button>
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report rendering area --> 
            <div id="report-results-container" style="display:none;"></div>
            <div id="loading-spinner" style="display: none; text-align: center; font-size: 2em; padding: 40px;">
                <i class="fa fa-refresh fa-spin"></i>
            </div>
        </section>

    </div>

    <%- include('partials/footer') %>
</div>

<script src="<%= baseUrl %>/js/sender-report.js"></script>