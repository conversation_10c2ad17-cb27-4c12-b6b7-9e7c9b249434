<div class="wrapper">
    <%- include('partials/header', { user: user }) %>
    <%- include('partials/sidebar', { user: user }) %>

    <div class="content-wrapper">
        <section class="content-header">
            <h5><%= pageTitle %></h5>
            <ol class="breadcrumb">
                <li><a href="<%= baseUrl %>/dashboard"><i class="fa fa-dashboard"></i> HOME</a></li>
                <li class="active"><%= pageTitle %></li>
            </ol>
        </section>

        <section class="content">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <!-- <h3 class="box-title"><%= pageTitle %></h3> -->
                </div>
                <div class="box-body">
                    <!-- The form now points to the new POST route for Excel download -->
                    <form id="download-form" action="/api/reports/supplier-wise-list/export" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Select one or more Suppliers</label>
                                    <select name="supplier[]" id="supplier-select" class="form-control select2" multiple="multiple" required data-placeholder="Select Suppliers"></select>
                                </div>
                            </div>
                            <div class="col-md-3" style="padding-top: 25px;">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-download"></i> Download
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>
    
    <%- include('partials/footer') %>
</div>

<script>
    $(document).ready(function() {
        // Initialize the Select2 dropdown
        $('#supplier-select').select2({
            placeholder: 'Select Suppliers',
            allowClear: true,
            width: '100%',
            ajax: {
                url: '/api/lookups/suppliers', // You will need to create this lookup endpoint
                dataType: 'json',
                processResults: data => ({ results: data })
            }
        });

        // Optional: Add a loading spinner on form submission
        $('#download-form').on('submit', function() {
            // Show a simple alert or a loading spinner
            // Note: A real spinner is tricky with file downloads
            setTimeout(() => {
                alert('Your download will begin shortly...');
            }, 100);
        });
    });
</script>