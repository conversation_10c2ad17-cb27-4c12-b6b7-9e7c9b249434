<%- include('partials/header') %>
<%- include('partials/sidebar') %>

<div class="content-wrapper">
    <section class="content-header">
        <h1><%= pageTitle %></h1>
        <ol class="breadcrumb">
            <li><a href="/"><i class="fa fa-dashboard"></i> Home</a></li>
            <li class="active"><%= pageTitle %></li>
        </ol>
    </section>

    <!-- Main content -->
    <section class="content">
        <!-- Filter Box -->
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">Report Filters</h3>
            </div>
            <form id="report-filters">
                <div class="box-body">
                    <div class="row">
                        <!-- From Date Filter -->
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="fromDate">From Date</label>
                                <input type="text" id="fromDate" name="fromDate" class="form-control date-picker" required>
                            </div>
                        </div>
                        
                        <!-- To Date Filter -->
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="toDate">To Date</label>
                                <input type="text" id="toDate" name="toDate" class="form-control date-picker">
                            </div>
                        </div>

                        <!-- Country Filter -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country" name="country[]" class="form-control select2-multi" multiple="multiple">
                                    <option value="1">UAE</option>
                                    <option value="2">Oman</option>
                                    <option value="3">Qatar</option>
                                    <option value="5">Kuwait</option>
                                    <option value="6">Bahrain</option>
                                    <option value="7">Saudi</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status" class="form-control" required>
                                    <option value="Despatch" selected>Dispatch</option>
                                    <option value="Delivered">Delivery</option>
                                </select>
                            </div>
                        </div>

                        <!-- Order From Filter -->
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="orderFrom">Order From</label>
                                <select id="orderFrom" name="orderFrom[]" class="form-control select2-multi" multiple="multiple">
                                    <option value="Website">Website</option>
                                    <option value="Telephone">Telephone</option>
                                    <option value="Facebook">Facebook</option>
                                    <option value="iOS">iOS</option>
                                    <option value="Android">Android</option>
                                    <option value="Whatsapp">Whatsapp</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
        </div>

        <div id="loading-spinner" style="display: none; text-align: center;"><i class="fa fa-refresh fa-spin fa-2x"></i></div>
        <div id="report-results-container" style="display:none;"></div>
    </section>
</div>

<%- include('partials/footer') %>
<script src="/js/delivery-cohort-report.js"></script>