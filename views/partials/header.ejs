<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Ourshopee.com - Stock Report</title>
    <meta content="width=device-width, initial-scale=1" name="viewport">

    <!-- ================================ -->
    <!-- ✅ CSS Libraries -->
    <!-- ================================ -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.4.18/css/AdminLTE.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.4.18/css/skins/skin-blue.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.5/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- ✅ Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700">

    <!-- ✅ Project Custom Styles -->
    <link rel="stylesheet" href="/css/style.css">
</head>

<body class="hold-transition skin-blue sidebar-mini">
<div class="wrapper">

    <!-- ================================ -->
    <!-- ✅ Header Section -->
    <!-- ================================ -->
    <header class="main-header">

        <!-- ✅ Logo -->
        <a href="<%= baseUrl %>/dashboard" class="logo">
            <span class="logo-mini"><b>O</b>S</span>
            <span class="logo-lg">
                <img src="<%= baseUrl %>/images/logo.png" alt="Ourshopee Logo" style="height: 40px;">
            </span>
        </a>

        <!-- ✅ Top Navbar -->
        <nav class="navbar navbar-static-top">

            <!-- ✅ Sidebar Toggle Button -->
            <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
                <span class="sr-only">Toggle navigation</span>
            </a>

            <!-- ✅ User Dropdown Menu -->
            <div class="navbar-custom-menu">
                <ul class="nav navbar-nav">
                    <li class="dropdown user user-menu">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <% if (!user.profile_pic) { %>
                                <img src="<%= baseUrl %>/images/female.png" class="img-circle" alt="User Image">
                            <% } else { %>
                                <img src="<%= user.profile_pic %>" class="img-circle" alt="User Image">
                            <% } %>
                            <span class="hidden-xs"><%= user.name %></span>
                        </a>
                        <ul class="dropdown-menu">

                            <!-- ✅ User Image in Dropdown -->
                            <li class="user-header">
                                <% if (!user.profile_pic) { %>
                                    <img src="<%= baseUrl %>/images/female.png" class="img-circle" alt="User Image">
                                <% } else { %>
                                    <img src="<%= user.profile_pic %>" class="img-circle" alt="User Image">
                                <% } %>
                                <p><%= user.firstname %> <%= user.lastname %></p>
                            </li>

                            <!-- ✅ User Actions (Footer of Dropdown) -->
                            <li class="user-footer">
                                <!-- 
                                <div class="pull-left">
                                    <a href="#" class="btn btn-default btn-flat">Profile</a>
                                </div> 
                                -->
                                <div class="pull-right">
                                    <a href="/logout" class="btn btn-default btn-flat">Sign out</a>
                                </div>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>

        </nav>
    </header>
