<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ourshopee | Log in</title>
    <meta content="width=device-width, initial-scale=1" name="viewport">
    
    <!-- ✅ CDN Stylesheets -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/2.4.18/css/AdminLTE.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/iCheck/1.0.2/skins/square/blue.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:400,300,500,700">
    
    <!-- ✅ Favicon -->
    <link rel="icon" href="/images/fav_icon.png" type="image/x-icon" />

    <!-- ✅ Global Font Style -->
    <style>
        body, .login-box-msg, .form-control, .btn {
            font-family: 'Open Sans', sans-serif;
        }
    </style>
</head>
<body class="hold-transition login-page">

<!-- ✅ Login Box Container -->
<div class="login-box">

  <!-- ✅ Logo Section -->
  <div class="login-logo">
    <a href="/"><img src="/images/logo.png" alt="Ourshopee Logo"/></a>
  </div>

  <!-- ✅ Login Box Body -->
  <div class="login-box-body">
    <p class="login-box-msg">Sign in to start your session</p>
    
    <!-- ✅ Login Form -->
    <form id="login-form">
      <div class="form-group has-feedback">
        <input type="text" class="form-control" placeholder="Username" id="username" name="username" required>
        <span class="glyphicon glyphicon-user form-control-feedback"></span>
      </div>
      <div class="form-group has-feedback">
        <input type="password" class="form-control" placeholder="Password" id="password" name="password" required>
        <span class="glyphicon glyphicon-lock form-control-feedback"></span>
      </div>

      <!-- ✅ Remember Me and Submit Button -->
      <div class="row">
        <div class="col-xs-8">
          <div class="checkbox icheck">
            <label><input type="checkbox" name="remember"> Remember Me</label>
          </div>
        </div>
        <div class="col-xs-4">
          <button type="submit" class="btn btn-primary btn-block btn-flat">Sign In</button>
        </div>
      </div>
    </form>
    
    <!-- ✅ Message Box for Info or Errors -->
    <div id="message-box" style="margin-top: 15px;">
        <div class="callout callout-info">
            <p>Please login with your Username and Password.</p>
        </div>
    </div>

  </div>
</div>

<!-- ✅ JS Libraries (CDN) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/iCheck/1.0.2/icheck.min.js"></script>

<script>
  // ✅ Initialize iCheck plugin
  $(function () {
    $('input').iCheck({
      checkboxClass: 'icheckbox_square-blue',
      increaseArea: '20%' // optional
    });
  });

  // ✅ Handle Login Form Submission
  const loginForm = document.getElementById('login-form');
  const messageBox = document.getElementById('message-box');

  loginForm.addEventListener('submit', async (event) => {
    event.preventDefault(); // Prevent form default submission

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
      // ✅ Send POST request to login API
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (data.success) {
        // ✅ Redirect to dashboard on success
        window.location.href = '/dashboard';
      } else {
        // ✅ Show API error message
        messageBox.innerHTML = `
          <div class="callout callout-danger">
            <p>${data.error}</p>
          </div>
        `;
      }
    } catch (err) {
      // ✅ Handle generic errors
      messageBox.innerHTML = `
        <div class="callout callout-danger">
          <p>An unexpected error occurred. Please try again.</p>
        </div>
      `;
    }
  });
</script>
</body>
</html>
