<%- include('partials/header') %>
<%- include('partials/sidebar') %>

<div class="content-wrapper">
    <section class="content-header">
        <h1><%= pageTitle %></h1>
        <ol class="breadcrumb">
            <li><a href="/"><i class="fa fa-dashboard"></i> Home</a></li>
            <li>Reports</li>
            <li class="active"><%= pageTitle %></li>
        </ol>
    </section>

    <!-- Main content -->
    <section class="content">
        <!-- Filter Box -->
        <div class="box box-primary">
            <div class="box-header with-border"><h3 class="box-title">Report Filters</h3></div>
            <form id="quick-report-filters">
                <div class="box-body">
                    <div class="row">
                        <!-- Country Filter -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select required id="country" name="country" class="form-control select2-ajax" required></select>
                            </div>
                        </div>

                        <!-- Paymode Filter -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="paymode">Paymode</label>
                                <select name="paymode" id="paymode" class="form-control">
                                    <option value="">All Paymodes</option>
                                    <option value="Cash">Cash on Delivery</option>
                                    <option value="Credit">Credit Card Payments</option>
                                </select>
                            </div>
                        </div>

                        <!-- Status Filter -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Invoiced">Invoiced</option>
                                    <option value="Despatch">Despatch</option>
                                    <option value="Delivered">Delivered</option>
                                    <option value="Cancelled">Cancelled</option>
                                    <option value="Return">Return</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <!-- ✅ From Date -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Select From Date</label>
                                <input required type="text" name="fromDate" class="form-control date-picker">
                            </div>
                        </div>

                        <!-- ✅ To Date -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Select To Date</label>
                                <input required type="text" name="toDate" class="form-control date-picker">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <button type="submit" class="btn btn-primary">
                         Search
                    </button>
                </div>
            </form>
        </div>

        <div id="report-results-container" style="display:none;"></div>

        <div id="loading-spinner" style="display: none; text-align: center; font-size: 2em; padding: 40px;">
            <i class="fa fa-refresh fa-spin"></i>
        </div>
    </section>
</div>

<%- include('partials/footer') %>

<!-- Link to the specific JS for this page -->
<script src="/js/quick-report.js"></script>